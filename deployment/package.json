{"name": "midastechnical-com", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "prebuild": "echo 'Starting build...'", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "lint:css": "stylelint '**/*.css'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "generate-sitemap": "node scripts/generate-sitemap.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@notionhq/client": "^2.3.0", "@stripe/stripe-js": "^7.2.0", "@supabase/supabase-js": "^2.49.4", "autoprefixer": "^10.4.21", "axios": "^1.3.6", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.7", "critters": "^0.0.23", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "express-rate-limit": "^7.5.0", "firebase": "^11.6.1", "jsonwebtoken": "^9.0.2", "messagebird": "^4.0.1", "micro": "^10.0.1", "multer": "^1.4.5-lts.2", "next": "^14.1.0", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "next-seo": "^6.4.0", "next-sitemap": "^4.2.3", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "pg": "^8.15.6", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-hook-form": "^7.48.2", "react-image-gallery": "^1.3.0", "react-loading-skeleton": "^3.3.1", "react-query": "^3.39.3", "react-toastify": "^9.1.3", "sharp": "^0.33.5", "stripe": "^18.0.0", "swr": "^2.2.4", "tailwindcss": "^4.1.5", "telegraf": "^4.16.3", "twilio": "^4.11.0", "xlsx": "^0.18.5", "yup": "^1.3.2", "zustand": "^4.4.7"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250430.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.1", "babel-jest": "^29.7.0", "cssnano": "^7.0.6", "cypress": "^13.17.0", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-react": "^7.33.2", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^15.2.0", "msw": "^2.0.11", "optimize-css-assets-webpack-plugin": "^6.0.1", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "postcss-flexbugs-fixes": "^5.0.2", "postcss-import": "^16.1.0", "postcss-preset-env": "^10.1.6", "prettier": "^3.1.1", "styled-components": "^6.1.8", "stylelint": "^16.2.1", "stylelint-config-recommended": "^14.0.0", "stylelint-config-standard": "^36.0.0", "stylelint-order": "^6.0.4", "supertest": "^7.1.0"}}
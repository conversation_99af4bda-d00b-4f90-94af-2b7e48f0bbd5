/**
 * Referral Program System
 * Manages customer referrals, rewards, and tracking
 */

import { Pool } from 'pg';
import { productionEmailService } from './email-service-production.js';
import { monitoringService } from './monitoring.js';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class ReferralProgramService {
  constructor() {
    this.rewards = {
      referrer: {
        type: 'credit',
        amount: 25, // $25 store credit
        description: 'Thank you for referring a friend!'
      },
      referee: {
        type: 'discount',
        amount: 15, // 15% discount
        description: 'Welcome! Enjoy 15% off your first order'
      }
    };

    this.minimumOrderValue = 50; // Minimum order value to qualify for referral rewards
  }

  /**
   * Generate referral code for user
   */
  async generateReferralCode(userId) {
    try {
      const client = await pool.connect();
      
      // Check if user already has a referral code
      const existingResult = await client.query(`
        SELECT referral_code FROM user_referrals WHERE user_id = $1
      `, [userId]);

      if (existingResult.rows.length > 0) {
        client.release();
        return existingResult.rows[0].referral_code;
      }

      // Generate unique referral code
      let referralCode;
      let isUnique = false;
      let attempts = 0;

      while (!isUnique && attempts < 10) {
        referralCode = this.createReferralCode(userId);
        
        const checkResult = await client.query(`
          SELECT id FROM user_referrals WHERE referral_code = $1
        `, [referralCode]);

        if (checkResult.rows.length === 0) {
          isUnique = true;
        }
        attempts++;
      }

      if (!isUnique) {
        throw new Error('Failed to generate unique referral code');
      }

      // Save referral code
      await client.query(`
        INSERT INTO user_referrals (
          user_id, referral_code, total_referrals, total_rewards_earned,
          created_at
        ) VALUES ($1, $2, 0, 0, CURRENT_TIMESTAMP)
      `, [userId, referralCode]);

      client.release();
      
      console.log(`✅ Referral code generated for user ${userId}: ${referralCode}`);
      return referralCode;

    } catch (error) {
      console.error('❌ Failed to generate referral code:', error);
      throw error;
    }
  }

  /**
   * Process referral signup
   */
  async processReferralSignup(newUserId, referralCode) {
    try {
      const client = await pool.connect();
      
      // Find referrer
      const referrerResult = await client.query(`
        SELECT ur.user_id, u.email, u.first_name, u.last_name
        FROM user_referrals ur
        JOIN users u ON ur.user_id = u.id
        WHERE ur.referral_code = $1
      `, [referralCode]);

      if (referrerResult.rows.length === 0) {
        console.warn(`⚠️ Invalid referral code: ${referralCode}`);
        client.release();
        return { success: false, message: 'Invalid referral code' };
      }

      const referrer = referrerResult.rows[0];

      // Check if new user was already referred
      const existingReferralResult = await client.query(`
        SELECT id FROM referral_signups WHERE referred_user_id = $1
      `, [newUserId]);

      if (existingReferralResult.rows.length > 0) {
        client.release();
        return { success: false, message: 'User already referred' };
      }

      // Record referral signup
      await client.query(`
        INSERT INTO referral_signups (
          referrer_user_id, referred_user_id, referral_code,
          status, created_at
        ) VALUES ($1, $2, $3, 'pending', CURRENT_TIMESTAMP)
      `, [referrer.user_id, newUserId, referralCode]);

      // Create referee discount
      const discountCode = await this.createRefereeDiscount(newUserId);

      // Send welcome email to referee
      await this.sendRefereeWelcomeEmail(newUserId, discountCode, referrer);

      // Track referral event
      await this.trackReferralEvent(referrer.user_id, 'referral_signup', {
        referred_user_id: newUserId,
        referral_code: referralCode
      });

      client.release();
      
      console.log(`✅ Referral signup processed: ${referrer.user_id} → ${newUserId}`);
      
      return { 
        success: true, 
        discountCode,
        referrerName: `${referrer.first_name} ${referrer.last_name}`
      };

    } catch (error) {
      console.error('❌ Failed to process referral signup:', error);
      throw error;
    }
  }

  /**
   * Process referral completion (when referee makes first purchase)
   */
  async processReferralCompletion(orderId, userId, orderValue) {
    try {
      const client = await pool.connect();
      
      // Check if this order qualifies for referral completion
      if (orderValue < this.minimumOrderValue) {
        client.release();
        return { success: false, message: 'Order value too low for referral completion' };
      }

      // Find pending referral
      const referralResult = await client.query(`
        SELECT rs.*, ur.user_id as referrer_id, u.email as referrer_email, 
               u.first_name as referrer_first_name
        FROM referral_signups rs
        JOIN user_referrals ur ON rs.referrer_user_id = ur.user_id
        JOIN users u ON ur.user_id = u.id
        WHERE rs.referred_user_id = $1 AND rs.status = 'pending'
      `, [userId]);

      if (referralResult.rows.length === 0) {
        client.release();
        return { success: false, message: 'No pending referral found' };
      }

      const referral = referralResult.rows[0];

      // Mark referral as completed
      await client.query(`
        UPDATE referral_signups 
        SET status = 'completed', completed_at = CURRENT_TIMESTAMP,
            first_order_id = $1, first_order_value = $2
        WHERE id = $3
      `, [orderId, orderValue, referral.id]);

      // Award referrer credit
      const referrerCredit = this.rewards.referrer.amount;
      await client.query(`
        UPDATE users 
        SET store_credit = COALESCE(store_credit, 0) + $1
        WHERE id = $2
      `, [referrerCredit, referral.referrer_id]);

      // Update referrer stats
      await client.query(`
        UPDATE user_referrals 
        SET total_referrals = total_referrals + 1,
            total_rewards_earned = total_rewards_earned + $1,
            last_referral_at = CURRENT_TIMESTAMP
        WHERE user_id = $2
      `, [referrerCredit, referral.referrer_id]);

      // Create referral reward record
      await client.query(`
        INSERT INTO referral_rewards (
          referral_signup_id, user_id, reward_type, reward_amount,
          description, created_at
        ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `, [
        referral.id,
        referral.referrer_id,
        this.rewards.referrer.type,
        referrerCredit,
        this.rewards.referrer.description
      ]);

      // Send reward notification to referrer
      await this.sendReferrerRewardEmail(referral, referrerCredit, orderValue);

      // Track completion event
      await this.trackReferralEvent(referral.referrer_id, 'referral_completed', {
        referred_user_id: userId,
        order_id: orderId,
        order_value: orderValue,
        reward_amount: referrerCredit
      });

      client.release();
      
      console.log(`✅ Referral completed: ${referral.referrer_id} earned $${referrerCredit}`);
      
      return { success: true, rewardAmount: referrerCredit };

    } catch (error) {
      console.error('❌ Failed to process referral completion:', error);
      throw error;
    }
  }

  /**
   * Get referral statistics for user
   */
  async getReferralStats(userId) {
    try {
      const client = await pool.connect();
      
      // Get referral overview
      const overviewResult = await client.query(`
        SELECT referral_code, total_referrals, total_rewards_earned
        FROM user_referrals
        WHERE user_id = $1
      `, [userId]);

      if (overviewResult.rows.length === 0) {
        // Generate referral code if doesn't exist
        const referralCode = await this.generateReferralCode(userId);
        client.release();
        return {
          referralCode,
          totalReferrals: 0,
          totalRewardsEarned: 0,
          pendingReferrals: 0,
          recentReferrals: []
        };
      }

      const overview = overviewResult.rows[0];

      // Get pending referrals count
      const pendingResult = await client.query(`
        SELECT COUNT(*) as count
        FROM referral_signups
        WHERE referrer_user_id = $1 AND status = 'pending'
      `, [userId]);

      const pendingCount = parseInt(pendingResult.rows[0].count);

      // Get recent referrals
      const recentResult = await client.query(`
        SELECT rs.*, u.first_name, u.email, rs.created_at, rs.completed_at
        FROM referral_signups rs
        JOIN users u ON rs.referred_user_id = u.id
        WHERE rs.referrer_user_id = $1
        ORDER BY rs.created_at DESC
        LIMIT 10
      `, [userId]);

      client.release();

      return {
        referralCode: overview.referral_code,
        totalReferrals: overview.total_referrals,
        totalRewardsEarned: parseFloat(overview.total_rewards_earned),
        pendingReferrals: pendingCount,
        recentReferrals: recentResult.rows.map(referral => ({
          id: referral.id,
          referredUserName: referral.first_name,
          referredUserEmail: referral.email,
          status: referral.status,
          signupDate: referral.created_at,
          completedDate: referral.completed_at
        }))
      };

    } catch (error) {
      console.error('❌ Failed to get referral stats:', error);
      throw error;
    }
  }

  /**
   * Create referral code
   */
  createReferralCode(userId) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = 'REF';
    
    // Add user ID component
    code += userId.toString().padStart(4, '0');
    
    // Add random component
    for (let i = 0; i < 4; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return code;
  }

  /**
   * Create discount code for referee
   */
  async createRefereeDiscount(userId) {
    try {
      const client = await pool.connect();
      
      const discountCode = `WELCOME${userId}${Date.now().toString().slice(-4)}`;
      
      await client.query(`
        INSERT INTO discount_codes (
          code, type, value, user_id, usage_type,
          max_uses, expires_at, created_at
        ) VALUES ($1, 'percentage', $2, $3, 'referral_welcome', 1,
                 CURRENT_TIMESTAMP + INTERVAL '30 days', CURRENT_TIMESTAMP)
      `, [discountCode, this.rewards.referee.amount, userId]);

      client.release();
      return discountCode;

    } catch (error) {
      console.error('❌ Failed to create referee discount:', error);
      return null;
    }
  }

  /**
   * Send welcome email to referee
   */
  async sendRefereeWelcomeEmail(userId, discountCode, referrer) {
    try {
      const client = await pool.connect();
      
      const userResult = await client.query(`
        SELECT email, first_name FROM users WHERE id = $1
      `, [userId]);

      if (userResult.rows.length === 0) {
        client.release();
        return;
      }

      const user = userResult.rows[0];

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb;">Welcome to Midas Technical!</h1>
          <p>Hi ${user.first_name},</p>
          <p>Great news! ${referrer.first_name} ${referrer.last_name} referred you to Midas Technical, and we're excited to welcome you to our community.</p>
          
          <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
            <h3 style="color: #dc2626;">Special Welcome Offer</h3>
            <p style="font-size: 18px; margin: 10px 0;">Get <strong>${this.rewards.referee.amount}% OFF</strong> your first order!</p>
            <p>Use code: <strong style="font-size: 20px; color: #2563eb;">${discountCode}</strong></p>
            <a href="${process.env.NEXT_PUBLIC_SITE_URL}/products" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin-top: 10px;">
              Start Shopping
            </a>
          </div>
          
          <p>This exclusive discount is valid for 30 days and can be used on any order over $${this.minimumOrderValue}.</p>
          
          <p style="margin-top: 30px;">
            Welcome to the Midas Technical family!<br>
            The Midas Technical Team
          </p>
        </div>
      `;

      const textContent = `
Hi ${user.first_name},

Great news! ${referrer.first_name} ${referrer.last_name} referred you to Midas Technical, and we're excited to welcome you to our community.

Special Welcome Offer: Get ${this.rewards.referee.amount}% OFF your first order!
Use code: ${discountCode}

Start shopping: ${process.env.NEXT_PUBLIC_SITE_URL}/products

This exclusive discount is valid for 30 days and can be used on any order over $${this.minimumOrderValue}.

Welcome to the Midas Technical family!
The Midas Technical Team
      `;

      await productionEmailService.sendEmail(
        user.email,
        'Welcome to Midas Technical - Special Offer Inside!',
        htmlContent,
        textContent
      );

      client.release();
      
      console.log(`📧 Referee welcome email sent to ${user.email}`);

    } catch (error) {
      console.error('❌ Failed to send referee welcome email:', error);
    }
  }

  /**
   * Send reward notification to referrer
   */
  async sendReferrerRewardEmail(referral, rewardAmount, orderValue) {
    try {
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb;">Congratulations! You've Earned a Reward!</h1>
          <p>Hi ${referral.referrer_first_name},</p>
          <p>Great news! Your referral just made their first purchase, and you've earned a reward!</p>
          
          <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
            <h3 style="color: #16a34a;">You've Earned $${rewardAmount} Store Credit!</h3>
            <p>Your friend's order value: $${orderValue.toFixed(2)}</p>
            <p>Your reward has been automatically added to your account.</p>
          </div>
          
          <p>Keep referring friends and earn more rewards! Share your referral code and help others discover our premium electronic components.</p>
          
          <div style="text-align: center; margin: 20px 0;">
            <a href="${process.env.NEXT_PUBLIC_SITE_URL}/account/referrals" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              View Referral Dashboard
            </a>
          </div>
          
          <p>Thank you for spreading the word about Midas Technical!</p>
        </div>
      `;

      const textContent = `
Hi ${referral.referrer_first_name},

Great news! Your referral just made their first purchase, and you've earned a reward!

You've Earned $${rewardAmount} Store Credit!
Your friend's order value: $${orderValue.toFixed(2)}
Your reward has been automatically added to your account.

Keep referring friends and earn more rewards! Share your referral code and help others discover our premium electronic components.

View your referral dashboard: ${process.env.NEXT_PUBLIC_SITE_URL}/account/referrals

Thank you for spreading the word about Midas Technical!
      `;

      await productionEmailService.sendEmail(
        referral.referrer_email,
        'Congratulations! You\'ve Earned a Referral Reward',
        htmlContent,
        textContent
      );
      
      console.log(`📧 Referrer reward email sent to ${referral.referrer_email}`);

    } catch (error) {
      console.error('❌ Failed to send referrer reward email:', error);
    }
  }

  /**
   * Track referral events
   */
  async trackReferralEvent(userId, eventName, properties = {}) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO analytics_events (
          event_name, user_id, properties, created_at
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
      `, [eventName, userId, JSON.stringify(properties)]);

      client.release();

      // Track with monitoring service
      monitoringService.trackEvent(eventName, {
        userId,
        category: 'referral_program',
        ...properties
      });

    } catch (error) {
      console.error('❌ Failed to track referral event:', error);
    }
  }
}

// Export singleton instance
export const referralProgramService = new ReferralProgramService();

export default ReferralProgramService;

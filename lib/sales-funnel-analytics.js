/**
 * Sales Funnel Analytics System
 * Tracks conversion rates and customer journey analytics
 */

import { Pool } from 'pg';
import { monitoringService } from './monitoring.js';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class SalesFunnelAnalytics {
  constructor() {
    this.funnelStages = [
      'visitor',
      'product_view',
      'add_to_cart',
      'begin_checkout',
      'complete_purchase',
      'repeat_customer'
    ];
  }

  /**
   * Track funnel event
   */
  async trackFunnelEvent(userId, sessionId, eventName, properties = {}) {
    try {
      const client = await pool.connect();
      
      // Record the funnel event
      await client.query(`
        INSERT INTO sales_funnel_events (
          user_id, session_id, event_name, properties, 
          ip_address, user_agent, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
      `, [
        userId,
        sessionId,
        eventName,
        JSON.stringify(properties),
        properties.ipAddress || null,
        properties.userAgent || null
      ]);

      // Update user journey
      await this.updateUserJourney(userId, sessionId, eventName, properties);

      client.release();

      // Track with monitoring service
      monitoringService.trackEvent(eventName, {
        userId,
        sessionId,
        category: 'sales_funnel',
        ...properties
      });

      console.log(`📊 Funnel event tracked: ${eventName} for user ${userId || 'anonymous'}`);

    } catch (error) {
      console.error('❌ Failed to track funnel event:', error);
    }
  }

  /**
   * Update user journey tracking
   */
  async updateUserJourney(userId, sessionId, eventName, properties) {
    const client = await pool.connect();
    
    try {
      // Get or create user journey
      const journeyResult = await client.query(`
        SELECT * FROM user_journeys 
        WHERE (user_id = $1 OR session_id = $2)
        AND created_at >= CURRENT_DATE - INTERVAL '30 days'
        ORDER BY created_at DESC
        LIMIT 1
      `, [userId, sessionId]);

      let journeyId;
      
      if (journeyResult.rows.length === 0) {
        // Create new journey
        const newJourneyResult = await client.query(`
          INSERT INTO user_journeys (
            user_id, session_id, current_stage, stages_completed,
            first_visit, last_activity, created_at
          ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          RETURNING id
        `, [userId, sessionId, eventName, JSON.stringify([eventName])]);
        
        journeyId = newJourneyResult.rows[0].id;
      } else {
        // Update existing journey
        const journey = journeyResult.rows[0];
        const stagesCompleted = JSON.parse(journey.stages_completed || '[]');
        
        if (!stagesCompleted.includes(eventName)) {
          stagesCompleted.push(eventName);
        }

        await client.query(`
          UPDATE user_journeys 
          SET current_stage = $1, 
              stages_completed = $2,
              last_activity = CURRENT_TIMESTAMP,
              user_id = COALESCE(user_id, $3)
          WHERE id = $4
        `, [eventName, JSON.stringify(stagesCompleted), userId, journey.id]);
        
        journeyId = journey.id;
      }

      // Record stage transition
      await client.query(`
        INSERT INTO journey_stage_transitions (
          journey_id, from_stage, to_stage, properties, created_at
        ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
      `, [journeyId, properties.previousStage || null, eventName, JSON.stringify(properties)]);

    } finally {
      client.release();
    }
  }

  /**
   * Get conversion funnel data
   */
  async getConversionFunnel(dateFrom, dateTo, segmentBy = null) {
    try {
      const client = await pool.connect();
      
      // Base query for funnel metrics
      let query = `
        WITH funnel_data AS (
          SELECT 
            event_name,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_users,
            COUNT(*) as total_events,
            DATE(created_at) as event_date
          FROM sales_funnel_events 
          WHERE created_at >= $1 AND created_at <= $2
      `;

      const params = [dateFrom, dateTo];

      // Add segmentation if specified
      if (segmentBy) {
        query += ` AND properties->>'${segmentBy}' IS NOT NULL`;
      }

      query += `
          GROUP BY event_name, DATE(created_at)
        ),
        stage_order AS (
          SELECT unnest(ARRAY['visitor', 'product_view', 'add_to_cart', 'begin_checkout', 'complete_purchase', 'repeat_customer']) as stage,
                 generate_series(1, 6) as order_index
        )
        SELECT 
          so.stage,
          so.order_index,
          COALESCE(SUM(fd.unique_users), 0) as unique_users,
          COALESCE(SUM(fd.total_events), 0) as total_events,
          CASE 
            WHEN LAG(SUM(fd.unique_users)) OVER (ORDER BY so.order_index) > 0 
            THEN ROUND((SUM(fd.unique_users)::decimal / LAG(SUM(fd.unique_users)) OVER (ORDER BY so.order_index)) * 100, 2)
            ELSE 100
          END as conversion_rate
        FROM stage_order so
        LEFT JOIN funnel_data fd ON so.stage = fd.event_name
        GROUP BY so.stage, so.order_index
        ORDER BY so.order_index
      `;

      const result = await client.query(query, params);
      
      // Calculate overall metrics
      const overallResult = await client.query(`
        SELECT 
          COUNT(DISTINCT COALESCE(user_id::text, session_id)) as total_visitors,
          COUNT(DISTINCT CASE WHEN event_name = 'complete_purchase' THEN COALESCE(user_id::text, session_id) END) as total_customers,
          AVG(CASE WHEN event_name = 'complete_purchase' THEN (properties->>'orderValue')::decimal END) as avg_order_value
        FROM sales_funnel_events 
        WHERE created_at >= $1 AND created_at <= $2
      `, params);

      const overall = overallResult.rows[0];
      const overallConversionRate = overall.total_visitors > 0 
        ? (overall.total_customers / overall.total_visitors * 100).toFixed(2)
        : 0;

      client.release();

      return {
        funnel: result.rows,
        overall: {
          totalVisitors: parseInt(overall.total_visitors),
          totalCustomers: parseInt(overall.total_customers),
          conversionRate: parseFloat(overallConversionRate),
          averageOrderValue: parseFloat(overall.avg_order_value || 0)
        },
        dateRange: { from: dateFrom, to: dateTo }
      };

    } catch (error) {
      console.error('❌ Failed to get conversion funnel:', error);
      throw error;
    }
  }

  /**
   * Get customer lifetime value analytics
   */
  async getCustomerLifetimeValue(dateFrom, dateTo) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        WITH customer_metrics AS (
          SELECT 
            o.customer_email,
            MIN(o.created_at) as first_order_date,
            MAX(o.created_at) as last_order_date,
            COUNT(*) as total_orders,
            SUM(o.total_amount) as total_spent,
            AVG(o.total_amount) as avg_order_value,
            EXTRACT(DAYS FROM MAX(o.created_at) - MIN(o.created_at)) as customer_lifespan_days
          FROM orders o
          WHERE o.status NOT IN ('cancelled', 'refunded')
          AND o.created_at >= $1 AND o.created_at <= $2
          GROUP BY o.customer_email
        )
        SELECT 
          COUNT(*) as total_customers,
          AVG(total_orders) as avg_orders_per_customer,
          AVG(total_spent) as avg_customer_lifetime_value,
          AVG(avg_order_value) as avg_order_value,
          AVG(customer_lifespan_days) as avg_customer_lifespan_days,
          PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY total_spent) as median_clv,
          COUNT(CASE WHEN total_orders > 1 THEN 1 END) as repeat_customers,
          ROUND(COUNT(CASE WHEN total_orders > 1 THEN 1 END)::decimal / COUNT(*) * 100, 2) as repeat_customer_rate
        FROM customer_metrics
      `, [dateFrom, dateTo]);

      const clvData = result.rows[0];

      // Get cohort analysis
      const cohortResult = await client.query(`
        WITH first_orders AS (
          SELECT 
            customer_email,
            DATE_TRUNC('month', MIN(created_at)) as cohort_month,
            MIN(created_at) as first_order_date
          FROM orders
          WHERE status NOT IN ('cancelled', 'refunded')
          GROUP BY customer_email
        ),
        cohort_data AS (
          SELECT 
            fo.cohort_month,
            DATE_TRUNC('month', o.created_at) as order_month,
            COUNT(DISTINCT o.customer_email) as customers,
            SUM(o.total_amount) as revenue
          FROM first_orders fo
          JOIN orders o ON fo.customer_email = o.customer_email
          WHERE o.status NOT IN ('cancelled', 'refunded')
          AND fo.cohort_month >= $1 AND fo.cohort_month <= $2
          GROUP BY fo.cohort_month, DATE_TRUNC('month', o.created_at)
        )
        SELECT 
          cohort_month,
          order_month,
          customers,
          revenue,
          EXTRACT(MONTH FROM AGE(order_month, cohort_month)) as months_since_first_order
        FROM cohort_data
        ORDER BY cohort_month, order_month
      `, [dateFrom, dateTo]);

      client.release();

      return {
        customerLifetimeValue: {
          totalCustomers: parseInt(clvData.total_customers),
          avgOrdersPerCustomer: parseFloat(clvData.avg_orders_per_customer || 0),
          avgCustomerLifetimeValue: parseFloat(clvData.avg_customer_lifetime_value || 0),
          avgOrderValue: parseFloat(clvData.avg_order_value || 0),
          avgCustomerLifespanDays: parseFloat(clvData.avg_customer_lifespan_days || 0),
          medianCLV: parseFloat(clvData.median_clv || 0),
          repeatCustomers: parseInt(clvData.repeat_customers),
          repeatCustomerRate: parseFloat(clvData.repeat_customer_rate || 0)
        },
        cohortAnalysis: cohortResult.rows
      };

    } catch (error) {
      console.error('❌ Failed to get customer lifetime value:', error);
      throw error;
    }
  }

  /**
   * Get cart abandonment analytics
   */
  async getCartAbandonmentAnalytics(dateFrom, dateTo) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        WITH cart_metrics AS (
          SELECT 
            COUNT(*) as total_abandoned_carts,
            SUM(cart_value) as total_abandoned_value,
            AVG(cart_value) as avg_abandoned_value,
            COUNT(CASE WHEN recovered = true THEN 1 END) as recovered_carts,
            SUM(CASE WHEN recovered = true THEN cart_value ELSE 0 END) as recovered_value,
            AVG(recovery_emails_sent) as avg_recovery_emails_sent
          FROM abandoned_carts
          WHERE created_at >= $1 AND created_at <= $2
        ),
        recovery_by_email AS (
          SELECT 
            recovery_emails_sent,
            COUNT(*) as carts_count,
            COUNT(CASE WHEN recovered = true THEN 1 END) as recovered_count
          FROM abandoned_carts
          WHERE created_at >= $1 AND created_at <= $2
          GROUP BY recovery_emails_sent
        )
        SELECT 
          cm.*,
          CASE 
            WHEN cm.total_abandoned_carts > 0 
            THEN ROUND((cm.recovered_carts::decimal / cm.total_abandoned_carts) * 100, 2)
            ELSE 0
          END as recovery_rate,
          CASE 
            WHEN cm.total_abandoned_value > 0 
            THEN ROUND((cm.recovered_value::decimal / cm.total_abandoned_value) * 100, 2)
            ELSE 0
          END as value_recovery_rate
        FROM cart_metrics cm
      `, [dateFrom, dateTo]);

      const recoveryByEmailResult = await client.query(`
        SELECT 
          recovery_emails_sent,
          COUNT(*) as carts_count,
          COUNT(CASE WHEN recovered = true THEN 1 END) as recovered_count,
          CASE 
            WHEN COUNT(*) > 0 
            THEN ROUND((COUNT(CASE WHEN recovered = true THEN 1 END)::decimal / COUNT(*)) * 100, 2)
            ELSE 0
          END as recovery_rate_by_email
        FROM abandoned_carts
        WHERE created_at >= $1 AND created_at <= $2
        GROUP BY recovery_emails_sent
        ORDER BY recovery_emails_sent
      `, [dateFrom, dateTo]);

      client.release();

      const metrics = result.rows[0];

      return {
        totalAbandonedCarts: parseInt(metrics.total_abandoned_carts),
        totalAbandonedValue: parseFloat(metrics.total_abandoned_value || 0),
        avgAbandonedValue: parseFloat(metrics.avg_abandoned_value || 0),
        recoveredCarts: parseInt(metrics.recovered_carts),
        recoveredValue: parseFloat(metrics.recovered_value || 0),
        recoveryRate: parseFloat(metrics.recovery_rate || 0),
        valueRecoveryRate: parseFloat(metrics.value_recovery_rate || 0),
        avgRecoveryEmailsSent: parseFloat(metrics.avg_recovery_emails_sent || 0),
        recoveryByEmail: recoveryByEmailResult.rows
      };

    } catch (error) {
      console.error('❌ Failed to get cart abandonment analytics:', error);
      throw error;
    }
  }

  /**
   * Get referral program analytics
   */
  async getReferralAnalytics(dateFrom, dateTo) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        WITH referral_metrics AS (
          SELECT 
            COUNT(DISTINCT ur.user_id) as total_referrers,
            COUNT(rs.id) as total_referrals,
            COUNT(CASE WHEN rs.status = 'completed' THEN 1 END) as completed_referrals,
            SUM(CASE WHEN rs.status = 'completed' THEN rs.first_order_value ELSE 0 END) as referral_revenue,
            SUM(ur.total_rewards_earned) as total_rewards_paid,
            AVG(rs.first_order_value) as avg_referral_order_value
          FROM user_referrals ur
          LEFT JOIN referral_signups rs ON ur.user_id = rs.referrer_user_id
          WHERE rs.created_at >= $1 AND rs.created_at <= $2
        )
        SELECT 
          rm.*,
          CASE 
            WHEN rm.total_referrals > 0 
            THEN ROUND((rm.completed_referrals::decimal / rm.total_referrals) * 100, 2)
            ELSE 0
          END as referral_conversion_rate,
          CASE 
            WHEN rm.total_rewards_paid > 0 
            THEN ROUND(rm.referral_revenue::decimal / rm.total_rewards_paid, 2)
            ELSE 0
          END as roi_ratio
        FROM referral_metrics rm
      `, [dateFrom, dateTo]);

      const topReferrersResult = await client.query(`
        SELECT 
          u.first_name, u.last_name, u.email,
          ur.total_referrals, ur.total_rewards_earned,
          COUNT(CASE WHEN rs.status = 'completed' THEN 1 END) as successful_referrals
        FROM user_referrals ur
        JOIN users u ON ur.user_id = u.id
        LEFT JOIN referral_signups rs ON ur.user_id = rs.referrer_user_id
        WHERE rs.created_at >= $1 AND rs.created_at <= $2
        GROUP BY u.id, ur.user_id, ur.total_referrals, ur.total_rewards_earned
        ORDER BY ur.total_referrals DESC, ur.total_rewards_earned DESC
        LIMIT 10
      `, [dateFrom, dateTo]);

      client.release();

      const metrics = result.rows[0];

      return {
        totalReferrers: parseInt(metrics.total_referrers),
        totalReferrals: parseInt(metrics.total_referrals),
        completedReferrals: parseInt(metrics.completed_referrals),
        referralRevenue: parseFloat(metrics.referral_revenue || 0),
        totalRewardsPaid: parseFloat(metrics.total_rewards_paid || 0),
        avgReferralOrderValue: parseFloat(metrics.avg_referral_order_value || 0),
        referralConversionRate: parseFloat(metrics.referral_conversion_rate || 0),
        roiRatio: parseFloat(metrics.roi_ratio || 0),
        topReferrers: topReferrersResult.rows
      };

    } catch (error) {
      console.error('❌ Failed to get referral analytics:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const salesFunnelAnalytics = new SalesFunnelAnalytics();

export default SalesFunnelAnalytics;

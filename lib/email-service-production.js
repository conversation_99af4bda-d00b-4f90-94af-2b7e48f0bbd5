/**
 * Production Email Service with SendGrid Integration
 * Replaces SMTP with production-grade email delivery
 */

import sgMail from '@sendgrid/mail';
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class ProductionEmailService {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.emailProvider = process.env.EMAIL_PROVIDER || 'sendgrid'; // 'sendgrid' or 'mailgun'
    
    // Initialize SendGrid
    if (process.env.SENDGRID_API_KEY) {
      sgMail.setApiKey(process.env.SENDGRID_API_KEY);
      console.log('✅ SendGrid initialized');
    }

    // Email configuration
    this.fromEmail = process.env.SMTP_FROM || '<EMAIL>';
    this.fromName = 'Midas Technical Store';
    this.replyTo = process.env.REPLY_TO_EMAIL || '<EMAIL>';
    
    // Template IDs for SendGrid dynamic templates
    this.templateIds = {
      orderConfirmation: process.env.SENDGRID_ORDER_CONFIRMATION_TEMPLATE,
      shippingNotification: process.env.SENDGRID_SHIPPING_NOTIFICATION_TEMPLATE,
      deliveryConfirmation: process.env.SENDGRID_DELIVERY_CONFIRMATION_TEMPLATE,
      orderStatusUpdate: process.env.SENDGRID_ORDER_STATUS_TEMPLATE,
      lowStockAlert: process.env.SENDGRID_LOW_STOCK_TEMPLATE,
      repairStatusUpdate: process.env.SENDGRID_REPAIR_STATUS_TEMPLATE
    };
  }

  /**
   * Send email using production service
   */
  async sendEmail(to, subject, htmlContent, textContent, templateData = null, templateId = null) {
    try {
      let result;

      if (this.emailProvider === 'sendgrid' && process.env.SENDGRID_API_KEY) {
        result = await this.sendWithSendGrid(to, subject, htmlContent, textContent, templateData, templateId);
      } else if (this.emailProvider === 'mailgun' && process.env.MAILGUN_API_KEY) {
        result = await this.sendWithMailgun(to, subject, htmlContent, textContent);
      } else {
        // Fallback to SMTP for development
        result = await this.sendWithSMTP(to, subject, htmlContent, textContent);
      }

      // Log email delivery
      await this.logEmailDelivery(to, subject, result.messageId, 'sent');
      
      return result;

    } catch (error) {
      console.error('❌ Email sending failed:', error);
      
      // Log email failure
      await this.logEmailDelivery(to, subject, null, 'failed', error.message);
      
      throw error;
    }
  }

  /**
   * Send email with SendGrid
   */
  async sendWithSendGrid(to, subject, htmlContent, textContent, templateData = null, templateId = null) {
    try {
      const msg = {
        to: Array.isArray(to) ? to : [to],
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        replyTo: this.replyTo,
        subject,
        text: textContent,
        html: htmlContent,
        trackingSettings: {
          clickTracking: {
            enable: true,
            enableText: false
          },
          openTracking: {
            enable: true
          },
          subscriptionTracking: {
            enable: false
          }
        },
        mailSettings: {
          sandboxMode: {
            enable: !this.isProduction
          }
        }
      };

      // Use dynamic template if provided
      if (templateId && templateData) {
        msg.templateId = templateId;
        msg.dynamicTemplateData = templateData;
        delete msg.html;
        delete msg.text;
      }

      const response = await sgMail.send(msg);
      
      console.log(`✅ Email sent via SendGrid to ${to}`);
      
      return {
        messageId: response[0].headers['x-message-id'],
        provider: 'sendgrid',
        status: 'sent'
      };

    } catch (error) {
      console.error('❌ SendGrid error:', error.response?.body || error.message);
      throw error;
    }
  }

  /**
   * Send email with Mailgun
   */
  async sendWithMailgun(to, subject, htmlContent, textContent) {
    try {
      const mailgun = require('mailgun-js')({
        apiKey: process.env.MAILGUN_API_KEY,
        domain: process.env.MAILGUN_DOMAIN
      });

      const data = {
        from: `${this.fromName} <${this.fromEmail}>`,
        to: Array.isArray(to) ? to.join(',') : to,
        subject,
        text: textContent,
        html: htmlContent,
        'o:tracking': 'yes',
        'o:tracking-clicks': 'yes',
        'o:tracking-opens': 'yes'
      };

      const response = await mailgun.messages().send(data);
      
      console.log(`✅ Email sent via Mailgun to ${to}`);
      
      return {
        messageId: response.id,
        provider: 'mailgun',
        status: 'sent'
      };

    } catch (error) {
      console.error('❌ Mailgun error:', error);
      throw error;
    }
  }

  /**
   * Fallback SMTP sending for development
   */
  async sendWithSMTP(to, subject, htmlContent, textContent) {
    try {
      const nodemailer = require('nodemailer');
      
      const transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT) || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      });

      const mailOptions = {
        from: `${this.fromName} <${this.fromEmail}>`,
        to: Array.isArray(to) ? to.join(',') : to,
        subject,
        text: textContent,
        html: htmlContent
      };

      const result = await transporter.sendMail(mailOptions);
      
      console.log(`✅ Email sent via SMTP to ${to}`);
      
      return {
        messageId: result.messageId,
        provider: 'smtp',
        status: 'sent'
      };

    } catch (error) {
      console.error('❌ SMTP error:', error);
      throw error;
    }
  }

  /**
   * Log email delivery for tracking
   */
  async logEmailDelivery(to, subject, messageId, status, errorMessage = null) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO email_logs (
          recipient, subject, message_id, status, provider, 
          error_message, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
      `, [
        Array.isArray(to) ? to.join(',') : to,
        subject,
        messageId,
        status,
        this.emailProvider,
        errorMessage
      ]);
      
      client.release();
    } catch (error) {
      console.error('❌ Failed to log email delivery:', error);
    }
  }

  /**
   * Handle email webhooks for delivery tracking
   */
  async handleEmailWebhook(provider, eventData) {
    try {
      const client = await pool.connect();
      
      let messageId, eventType, timestamp;
      
      if (provider === 'sendgrid') {
        messageId = eventData.sg_message_id;
        eventType = eventData.event;
        timestamp = new Date(eventData.timestamp * 1000);
      } else if (provider === 'mailgun') {
        messageId = eventData['message-id'];
        eventType = eventData.event;
        timestamp = new Date(eventData.timestamp * 1000);
      }

      // Update email log with delivery status
      await client.query(`
        UPDATE email_logs 
        SET status = $1, delivered_at = $2, updated_at = CURRENT_TIMESTAMP
        WHERE message_id = $3
      `, [eventType, timestamp, messageId]);

      // Log webhook event
      await client.query(`
        INSERT INTO email_webhook_events (
          provider, message_id, event_type, event_data, created_at
        ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
      `, [provider, messageId, eventType, JSON.stringify(eventData)]);
      
      client.release();
      
      console.log(`✅ Email webhook processed: ${eventType} for ${messageId}`);
      
    } catch (error) {
      console.error('❌ Email webhook processing failed:', error);
      throw error;
    }
  }

  /**
   * Get email delivery statistics
   */
  async getEmailStats(dateFrom, dateTo) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          COUNT(*) as total_sent,
          COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered,
          COUNT(CASE WHEN status = 'bounced' THEN 1 END) as bounced,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN status = 'opened' THEN 1 END) as opened,
          COUNT(CASE WHEN status = 'clicked' THEN 1 END) as clicked,
          AVG(EXTRACT(EPOCH FROM (delivered_at - created_at))/60) as avg_delivery_time_minutes
        FROM email_logs 
        WHERE created_at >= $1 AND created_at <= $2
      `, [dateFrom, dateTo]);
      
      client.release();
      
      return result.rows[0];
    } catch (error) {
      console.error('❌ Failed to get email stats:', error);
      throw error;
    }
  }

  /**
   * Test email configuration
   */
  async testEmailConfiguration() {
    try {
      const testEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
      
      await this.sendEmail(
        testEmail,
        'Email Configuration Test - Midas Technical',
        '<h2>Email Configuration Test</h2><p>This is a test email to verify that the production email service is working correctly.</p>',
        'Email Configuration Test\n\nThis is a test email to verify that the production email service is working correctly.'
      );
      
      console.log('✅ Email configuration test successful');
      return true;
    } catch (error) {
      console.error('❌ Email configuration test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const productionEmailService = new ProductionEmailService();

// Export class for testing
export default ProductionEmailService;

/**
 * Email Service for Order and Customer Communications
 * Handles transactional emails with professional templates
 */

import nodemailer from 'nodemailer';

// Email configuration
const emailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT) || 587,
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
};

// Create transporter
let transporter;
try {
  transporter = nodemailer.createTransporter(emailConfig);
  console.log('✅ Email service initialized');
} catch (error) {
  console.error('❌ Email service initialization failed:', error);
  // Create mock transporter for development
  transporter = {
    sendMail: async (options) => {
      console.log('📧 Mock email sent:', options.subject);
      return { messageId: 'mock-message-id' };
    }
  };
}

// Email templates
const emailTemplates = {
  orderConfirmation: (data) => ({
    subject: `Order Confirmation - ${data.orderNumber} | Midas Technical`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Order Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; background: #f9f9f9; }
          .order-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee; }
          .total { font-weight: bold; font-size: 1.2em; color: #d4af37; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 0.9em; }
          .button { display: inline-block; background: #d4af37; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Order Confirmed!</h1>
            <p>Thank you for your purchase, ${data.customerName}</p>
          </div>
          
          <div class="content">
            <h2>Order Details</h2>
            <div class="order-details">
              <p><strong>Order Number:</strong> ${data.orderNumber}</p>
              <p><strong>Order Date:</strong> ${new Date().toLocaleDateString()}</p>
              
              <h3>Items Ordered:</h3>
              ${data.orderItems.map(item => `
                <div class="item">
                  <div>
                    <strong>${item.product_name}</strong><br>
                    <small>SKU: ${item.product_sku}</small><br>
                    <small>Quantity: ${item.quantity}</small>
                  </div>
                  <div>$${item.total_price.toFixed(2)}</div>
                </div>
              `).join('')}
              
              <div class="item">
                <div><strong>Subtotal:</strong></div>
                <div>$${data.subtotal.toFixed(2)}</div>
              </div>
              
              <div class="item">
                <div><strong>Shipping:</strong></div>
                <div>$${data.shippingCost.toFixed(2)}</div>
              </div>
              
              <div class="item total">
                <div><strong>Total:</strong></div>
                <div>$${data.totalAmount.toFixed(2)}</div>
              </div>
            </div>
            
            ${data.shippingAddress ? `
              <h3>Shipping Address:</h3>
              <div class="order-details">
                <p>${data.shippingAddress.name}</p>
                <p>${data.shippingAddress.address.line1}</p>
                ${data.shippingAddress.address.line2 ? `<p>${data.shippingAddress.address.line2}</p>` : ''}
                <p>${data.shippingAddress.address.city}, ${data.shippingAddress.address.state} ${data.shippingAddress.address.postal_code}</p>
                <p>${data.shippingAddress.address.country}</p>
              </div>
            ` : ''}
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="https://midastechnical.com/orders/${data.orderNumber}" class="button">Track Your Order</a>
            </div>
            
            <p>We'll send you another email when your order ships. If you have any questions, please contact our support team.</p>
          </div>
          
          <div class="footer">
            <p>Midas Technical Store<br>
            Email: <EMAIL><br>
            Phone: 1-555-MIDAS-TECH</p>
            <p><a href="https://midastechnical.com">Visit our website</a></p>
          </div>
        </div>
      </body>
      </html>
    `
  }),

  shippingNotification: (data) => ({
    subject: `Your Order Has Shipped - ${data.orderNumber} | Midas Technical`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Order Shipped</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; background: #f9f9f9; }
          .tracking-box { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
          .tracking-number { font-size: 1.5em; font-weight: bold; color: #10b981; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 0.9em; }
          .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📦 Your Order Has Shipped!</h1>
            <p>Hi ${data.customerName}, your order is on its way</p>
          </div>
          
          <div class="content">
            <p>Great news! Your order <strong>${data.orderNumber}</strong> has been shipped and is on its way to you.</p>
            
            <div class="tracking-box">
              <h3>Tracking Information</h3>
              <div class="tracking-number">${data.trackingNumber}</div>
              <a href="https://www.ups.com/track?tracknum=${data.trackingNumber}" class="button">Track Your Package</a>
            </div>
            
            <p><strong>Estimated Delivery:</strong> 3-5 business days</p>
            
            <p>You can track your package using the tracking number above. You'll receive email updates as your package moves through our shipping network.</p>
            
            <p>If you have any questions about your order, please don't hesitate to contact our customer support team.</p>
          </div>
          
          <div class="footer">
            <p>Midas Technical Store<br>
            Email: <EMAIL><br>
            Phone: 1-555-MIDAS-TECH</p>
          </div>
        </div>
      </body>
      </html>
    `
  }),

  lowStockAlert: (data) => ({
    subject: `Low Stock Alert - ${data.productName} | Midas Technical`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Low Stock Alert</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .alert { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 20px; margin: 20px 0; }
          .critical { background: #fee2e2; border-left-color: #ef4444; }
        </style>
      </head>
      <body>
        <div class="container">
          <h2>⚠️ Stock Alert</h2>
          <div class="alert ${data.alertType === 'critical' ? 'critical' : ''}">
            <h3>${data.alertType.toUpperCase()} Stock Level</h3>
            <p><strong>Product:</strong> ${data.productName} (${data.productSku})</p>
            <p><strong>Current Stock:</strong> ${data.currentStock} units</p>
            <p><strong>Threshold:</strong> ${data.threshold} units</p>
            ${data.alertType === 'critical' ? '<p><strong>Action Required:</strong> Immediate restocking needed</p>' : ''}
          </div>
          <p>Please review inventory levels and consider restocking this product.</p>
        </div>
      </body>
      </html>
    `
  }),

  repairStatusUpdate: (data) => ({
    subject: `Repair Update - ${data.ticketNumber} | Midas Technical`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Repair Status Update</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .status-update { background: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 20px; margin: 20px 0; }
          .button { display: inline-block; background: #d4af37; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h2>🔧 Repair Status Update</h2>
          <div class="status-update">
            <h3>Ticket: ${data.ticketNumber}</h3>
            <p><strong>Device:</strong> ${data.deviceInfo}</p>
            <p><strong>Status:</strong> ${data.newStatus}</p>
            <p><strong>Update:</strong> ${data.message}</p>
            ${data.estimatedCompletion ? `<p><strong>Estimated Completion:</strong> ${data.estimatedCompletion}</p>` : ''}
          </div>
          <div style="text-align: center;">
            <a href="https://midastechnical.com/repair/track?ticket=${data.ticketNumber}" class="button">Track Repair</a>
          </div>
        </div>
      </body>
      </html>
    `
  }),

  deliveryConfirmation: (data) => ({
    subject: `Order ${data.orderNumber} Delivered | Midas Technical`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Order Delivered</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .delivery-info { background: #f0fdf4; border-left: 4px solid #10b981; padding: 20px; margin: 20px 0; }
          .review-section { background: #fef3c7; border-radius: 8px; padding: 20px; margin: 20px 0; }
          .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px; }
          .review-button { background: #f59e0b; }
        </style>
      </head>
      <body>
        <div class="container">
          <h2>✅ Order Delivered!</h2>
          <p>Hi ${data.customerName},</p>
          <p>Your order <strong>${data.orderNumber}</strong> has been successfully delivered!</p>

          <div class="delivery-info">
            <h3>Delivery Details</h3>
            <p><strong>Order Number:</strong> ${data.orderNumber}</p>
            <p><strong>Tracking Number:</strong> ${data.trackingNumber}</p>
            <p><strong>Delivered At:</strong> ${new Date(data.deliveredAt).toLocaleString()}</p>
          </div>

          <div class="review-section">
            <h3>How was your experience?</h3>
            <p>We'd love to hear about your experience with Midas Technical. Your feedback helps us improve our service.</p>
            <div style="text-align: center;">
              <a href="https://midastechnical.com/review?order=${data.orderNumber}" class="button review-button">Leave a Review</a>
            </div>
          </div>

          <div style="text-align: center;">
            <a href="https://midastechnical.com/orders/${data.orderNumber}" class="button">View Order Details</a>
          </div>

          <p style="margin-top: 30px; font-size: 14px; color: #666;">
            Need support? Contact <NAME_EMAIL>
          </p>
        </div>
      </body>
      </html>
    `
  }),

  orderStatusUpdate: (data) => ({
    subject: `Order ${data.orderNumber} Status Update | Midas Technical`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Order Status Update</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .status-update { background: #f0f9ff; border-left: 4px solid #1e40af; padding: 20px; margin: 20px 0; }
          .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h2>📋 Order Status Update</h2>
          <p>Hi ${data.customerName},</p>
          <p>Your order <strong>${data.orderNumber}</strong> status has been updated.</p>

          <div class="status-update">
            <h3>Status Update</h3>
            <p><strong>Previous Status:</strong> ${data.oldStatus}</p>
            <p><strong>Current Status:</strong> ${data.newStatus}</p>
            ${data.notes ? `<p><strong>Notes:</strong> ${data.notes}</p>` : ''}
          </div>

          <div style="text-align: center;">
            <a href="https://midastechnical.com/orders/${data.orderNumber}" class="button">View Order Details</a>
          </div>

          <p style="margin-top: 30px; font-size: 14px; color: #666;">
            Questions? Contact <NAME_EMAIL>
          </p>
        </div>
      </body>
      </html>
    `
  })
};

// Email sending functions
export async function sendOrderConfirmationEmail(data) {
  try {
    const template = emailTemplates.orderConfirmation(data);
    
    const mailOptions = {
      from: `"Midas Technical Store" <${process.env.SMTP_FROM || '<EMAIL>'}>`,
      to: data.customerEmail,
      subject: template.subject,
      html: template.html
    };

    const result = await transporter.sendMail(mailOptions);
    console.log(`✅ Order confirmation email sent to ${data.customerEmail}`);
    return result;
  } catch (error) {
    console.error('❌ Failed to send order confirmation email:', error);
    throw error;
  }
}

export async function sendShippingNotificationEmail(data) {
  try {
    const template = emailTemplates.shippingNotification(data);
    
    const mailOptions = {
      from: `"Midas Technical Store" <${process.env.SMTP_FROM || '<EMAIL>'}>`,
      to: data.customerEmail,
      subject: template.subject,
      html: template.html
    };

    const result = await transporter.sendMail(mailOptions);
    console.log(`✅ Shipping notification email sent to ${data.customerEmail}`);
    return result;
  } catch (error) {
    console.error('❌ Failed to send shipping notification email:', error);
    throw error;
  }
}

export async function sendLowStockAlert(data) {
  try {
    const template = emailTemplates.lowStockAlert(data);
    
    const mailOptions = {
      from: `"Midas Technical Store" <${process.env.SMTP_FROM || '<EMAIL>'}>`,
      to: process.env.ADMIN_EMAIL || '<EMAIL>',
      subject: template.subject,
      html: template.html
    };

    const result = await transporter.sendMail(mailOptions);
    console.log(`✅ Low stock alert sent for ${data.productName}`);
    return result;
  } catch (error) {
    console.error('❌ Failed to send low stock alert:', error);
    throw error;
  }
}

export async function sendRepairStatusUpdate(data) {
  try {
    const template = emailTemplates.repairStatusUpdate(data);

    const mailOptions = {
      from: `"Midas Technical Store" <${process.env.SMTP_FROM || '<EMAIL>'}>`,
      to: data.customerEmail,
      subject: template.subject,
      html: template.html
    };

    const result = await transporter.sendMail(mailOptions);
    console.log(`✅ Repair status update sent to ${data.customerEmail}`);
    return result;
  } catch (error) {
    console.error('❌ Failed to send repair status update:', error);
    throw error;
  }
}

export async function sendDeliveryConfirmationEmail(data) {
  try {
    const template = emailTemplates.deliveryConfirmation(data);

    const mailOptions = {
      from: `"Midas Technical Store" <${process.env.SMTP_FROM || '<EMAIL>'}>`,
      to: data.customerEmail,
      subject: template.subject,
      html: template.html
    };

    const result = await transporter.sendMail(mailOptions);
    console.log(`✅ Delivery confirmation email sent to ${data.customerEmail}`);
    return result;
  } catch (error) {
    console.error('❌ Failed to send delivery confirmation email:', error);
    throw error;
  }
}

export async function sendOrderStatusUpdateEmail(data) {
  try {
    const template = emailTemplates.orderStatusUpdate(data);

    const mailOptions = {
      from: `"Midas Technical Store" <${process.env.SMTP_FROM || '<EMAIL>'}>`,
      to: data.customerEmail,
      subject: template.subject,
      html: template.html
    };

    const result = await transporter.sendMail(mailOptions);
    console.log(`✅ Order status update email sent to ${data.customerEmail}`);
    return result;
  } catch (error) {
    console.error('❌ Failed to send order status update email:', error);
    throw error;
  }
}

// Test email configuration
export async function testEmailConfiguration() {
  try {
    await transporter.verify();
    console.log('✅ Email configuration is valid');
    return true;
  } catch (error) {
    console.error('❌ Email configuration test failed:', error);
    return false;
  }
}

// Send test email
export async function sendTestEmail(toEmail) {
  try {
    const mailOptions = {
      from: `"Midas Technical Store" <${process.env.SMTP_FROM || '<EMAIL>'}>`,
      to: toEmail,
      subject: 'Test Email - Midas Technical Store',
      html: `
        <h2>Email Configuration Test</h2>
        <p>This is a test email to verify that the email service is working correctly.</p>
        <p>Sent at: ${new Date().toISOString()}</p>
        <p>If you received this email, the configuration is working properly.</p>
      `
    };

    const result = await transporter.sendMail(mailOptions);
    console.log(`✅ Test email sent to ${toEmail}`);
    return result;
  } catch (error) {
    console.error('❌ Failed to send test email:', error);
    throw error;
  }
}

export default {
  sendOrderConfirmationEmail,
  sendShippingNotificationEmail,
  sendDeliveryConfirmationEmail,
  sendOrderStatusUpdateEmail,
  sendLowStockAlert,
  sendRepairStatusUpdate,
  testEmailConfiguration,
  sendTestEmail
};

/**
 * Authentication Middleware for API Routes
 * Provides role-based access control for admin and repair admin routes
 */

import { getToken } from 'next-auth/jwt';
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

/**
 * Middleware to require authentication
 */
export async function requireAuth(req, res, next) {
  try {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'UNAUTHORIZED'
      });
    }

    // Add user info to request
    req.user = {
      id: token.id,
      email: token.email,
      name: token.name,
      isAdmin: token.isAdmin,
      isRepairAdmin: token.isRepairAdmin,
      emailVerified: token.emailVerified
    };

    if (next) {
      return next();
    }

    return true;

  } catch (error) {
    console.error('❌ Auth middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error',
      code: 'AUTH_ERROR'
    });
  }
}

/**
 * Middleware to require admin access
 */
export async function requireAdmin(req, res, next) {
  try {
    const authResult = await requireAuth(req, res);
    if (authResult !== true) {
      return authResult; // Return the error response
    }

    if (!req.user.isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required',
        code: 'FORBIDDEN'
      });
    }

    // Log admin access
    await logAdminAccess(req.user.id, req.method, req.url);

    if (next) {
      return next();
    }

    return true;

  } catch (error) {
    console.error('❌ Admin middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authorization error',
      code: 'AUTH_ERROR'
    });
  }
}

/**
 * Middleware to require repair admin access
 */
export async function requireRepairAdmin(req, res, next) {
  try {
    const authResult = await requireAuth(req, res);
    if (authResult !== true) {
      return authResult;
    }

    if (!req.user.isRepairAdmin && !req.user.isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Repair admin access required',
        code: 'FORBIDDEN'
      });
    }

    // Log repair admin access
    await logAdminAccess(req.user.id, req.method, req.url, 'repair_admin');

    if (next) {
      return next();
    }

    return true;

  } catch (error) {
    console.error('❌ Repair admin middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authorization error',
      code: 'AUTH_ERROR'
    });
  }
}

/**
 * Middleware to require email verification
 */
export async function requireEmailVerification(req, res, next) {
  try {
    const authResult = await requireAuth(req, res);
    if (authResult !== true) {
      return authResult;
    }

    if (!req.user.emailVerified) {
      return res.status(403).json({
        success: false,
        message: 'Email verification required',
        code: 'EMAIL_NOT_VERIFIED'
      });
    }

    if (next) {
      return next();
    }

    return true;

  } catch (error) {
    console.error('❌ Email verification middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Verification error',
      code: 'AUTH_ERROR'
    });
  }
}

/**
 * API key authentication for external integrations
 */
export async function requireApiKey(req, res, next) {
  try {
    const apiKey = req.headers['x-api-key'] || req.query.api_key;
    
    if (!apiKey) {
      return res.status(401).json({
        success: false,
        message: 'API key required',
        code: 'API_KEY_REQUIRED'
      });
    }

    // Validate API key
    const client = await pool.connect();
    const result = await client.query(
      'SELECT id, name, permissions, is_active FROM api_keys WHERE key_hash = $1',
      [apiKey] // In production, hash the API key
    );
    client.release();

    if (result.rows.length === 0 || !result.rows[0].is_active) {
      return res.status(401).json({
        success: false,
        message: 'Invalid API key',
        code: 'INVALID_API_KEY'
      });
    }

    req.apiKey = result.rows[0];

    // Log API key usage
    await logApiKeyUsage(result.rows[0].id, req.method, req.url);

    if (next) {
      return next();
    }

    return true;

  } catch (error) {
    console.error('❌ API key middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'API authentication error',
      code: 'AUTH_ERROR'
    });
  }
}

/**
 * Rate limiting middleware
 */
export async function rateLimit(req, res, next, options = {}) {
  try {
    const {
      windowMs = 15 * 60 * 1000, // 15 minutes
      maxRequests = 100,
      keyGenerator = (req) => req.ip || 'unknown'
    } = options;

    const key = keyGenerator(req);
    const now = Date.now();
    const windowStart = now - windowMs;

    const client = await pool.connect();
    
    // Clean old entries
    await client.query(
      'DELETE FROM rate_limit_logs WHERE created_at < $1',
      [new Date(windowStart)]
    );

    // Count requests in current window
    const result = await client.query(
      'SELECT COUNT(*) as count FROM rate_limit_logs WHERE key = $1 AND created_at >= $2',
      [key, new Date(windowStart)]
    );

    const requestCount = parseInt(result.rows[0].count);

    if (requestCount >= maxRequests) {
      client.release();
      return res.status(429).json({
        success: false,
        message: 'Too many requests',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }

    // Log this request
    await client.query(
      'INSERT INTO rate_limit_logs (key, created_at) VALUES ($1, CURRENT_TIMESTAMP)',
      [key]
    );

    client.release();

    if (next) {
      return next();
    }

    return true;

  } catch (error) {
    console.error('❌ Rate limit middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Rate limiting error',
      code: 'RATE_LIMIT_ERROR'
    });
  }
}

/**
 * Log admin access for audit trail
 */
async function logAdminAccess(userId, method, url, accessType = 'admin') {
  try {
    const client = await pool.connect();
    await client.query(`
      INSERT INTO admin_access_logs (
        user_id, access_type, method, url, created_at
      ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
    `, [userId, accessType, method, url]);
    client.release();
  } catch (error) {
    console.error('❌ Failed to log admin access:', error);
  }
}

/**
 * Log API key usage
 */
async function logApiKeyUsage(apiKeyId, method, url) {
  try {
    const client = await pool.connect();
    await client.query(`
      INSERT INTO api_key_usage_logs (
        api_key_id, method, url, created_at
      ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
    `, [apiKeyId, method, url]);
    client.release();
  } catch (error) {
    console.error('❌ Failed to log API key usage:', error);
  }
}

/**
 * Higher-order function to wrap API routes with authentication
 */
export function withAuth(handler, options = {}) {
  return async (req, res) => {
    try {
      const {
        requireAdmin: needsAdmin = false,
        requireRepairAdmin: needsRepairAdmin = false,
        requireEmailVerification: needsEmailVerification = false,
        rateLimit: rateLimitOptions = null
      } = options;

      // Apply rate limiting if configured
      if (rateLimitOptions) {
        const rateLimitResult = await rateLimit(req, res, null, rateLimitOptions);
        if (rateLimitResult !== true) {
          return rateLimitResult;
        }
      }

      // Apply authentication
      if (needsAdmin) {
        const adminResult = await requireAdmin(req, res);
        if (adminResult !== true) {
          return adminResult;
        }
      } else if (needsRepairAdmin) {
        const repairAdminResult = await requireRepairAdmin(req, res);
        if (repairAdminResult !== true) {
          return repairAdminResult;
        }
      } else {
        const authResult = await requireAuth(req, res);
        if (authResult !== true) {
          return authResult;
        }
      }

      // Apply email verification if required
      if (needsEmailVerification) {
        const emailVerificationResult = await requireEmailVerification(req, res);
        if (emailVerificationResult !== true) {
          return emailVerificationResult;
        }
      }

      // Call the actual handler
      return await handler(req, res);

    } catch (error) {
      console.error('❌ Auth wrapper error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  };
}

export default {
  requireAuth,
  requireAdmin,
  requireRepairAdmin,
  requireEmailVerification,
  requireApiKey,
  rateLimit,
  withAuth
};

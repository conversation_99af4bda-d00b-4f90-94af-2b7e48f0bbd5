/**
 * Production Monitoring and Analytics Setup
 * Integrates Sentry, Google Analytics, and custom performance monitoring
 */

import * as Sentry from '@sentry/nextjs';
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class MonitoringService {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.sentryInitialized = false;
    this.gaInitialized = false;
    
    this.initializeSentry();
    this.initializeGoogleAnalytics();
  }

  /**
   * Initialize Sentry for error tracking
   */
  initializeSentry() {
    if (process.env.SENTRY_DSN && !this.sentryInitialized) {
      Sentry.init({
        dsn: process.env.SENTRY_DSN,
        environment: process.env.NODE_ENV,
        tracesSampleRate: this.isProduction ? 0.1 : 1.0,
        debug: !this.isProduction,
        
        // Performance monitoring
        integrations: [
          new Sentry.Integrations.Http({ tracing: true }),
          new Sentry.Integrations.Express({ app: undefined }),
        ],

        // Error filtering
        beforeSend(event, hint) {
          // Filter out non-critical errors in production
          if (process.env.NODE_ENV === 'production') {
            if (event.exception) {
              const error = hint.originalException;
              
              // Skip common non-critical errors
              if (error?.message?.includes('Network Error') ||
                  error?.message?.includes('timeout') ||
                  error?.message?.includes('AbortError')) {
                return null;
              }
            }
          }
          
          return event;
        },

        // Set user context
        beforeSendTransaction(event) {
          return event;
        }
      });

      this.sentryInitialized = true;
      console.log('✅ Sentry monitoring initialized');
    }
  }

  /**
   * Initialize Google Analytics
   */
  initializeGoogleAnalytics() {
    if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_GA_ID && !this.gaInitialized) {
      // Load Google Analytics script
      const script = document.createElement('script');
      script.src = `https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`;
      script.async = true;
      document.head.appendChild(script);

      // Initialize gtag
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        window.dataLayer.push(arguments);
      }
      window.gtag = gtag;

      gtag('js', new Date());
      gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
        page_title: document.title,
        page_location: window.location.href,
        send_page_view: true
      });

      // Enhanced ecommerce tracking
      gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
        custom_map: {
          custom_parameter_1: 'order_id',
          custom_parameter_2: 'order_value'
        }
      });

      this.gaInitialized = true;
      console.log('✅ Google Analytics initialized');
    }
  }

  /**
   * Track custom events
   */
  trackEvent(eventName, properties = {}) {
    try {
      // Sentry breadcrumb
      if (this.sentryInitialized) {
        Sentry.addBreadcrumb({
          message: eventName,
          category: 'custom',
          data: properties,
          level: 'info'
        });
      }

      // Google Analytics event
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', eventName, {
          event_category: properties.category || 'general',
          event_label: properties.label,
          value: properties.value,
          ...properties
        });
      }

      // Custom event logging
      this.logCustomEvent(eventName, properties);

    } catch (error) {
      console.error('❌ Event tracking failed:', error);
    }
  }

  /**
   * Track e-commerce events
   */
  trackEcommerce(action, data) {
    try {
      if (typeof window !== 'undefined' && window.gtag) {
        switch (action) {
          case 'purchase':
            window.gtag('event', 'purchase', {
              transaction_id: data.orderId,
              value: data.value,
              currency: data.currency || 'USD',
              items: data.items?.map(item => ({
                item_id: item.sku,
                item_name: item.name,
                category: item.category,
                quantity: item.quantity,
                price: item.price
              }))
            });
            break;

          case 'add_to_cart':
            window.gtag('event', 'add_to_cart', {
              currency: data.currency || 'USD',
              value: data.value,
              items: data.items?.map(item => ({
                item_id: item.sku,
                item_name: item.name,
                category: item.category,
                quantity: item.quantity,
                price: item.price
              }))
            });
            break;

          case 'begin_checkout':
            window.gtag('event', 'begin_checkout', {
              currency: data.currency || 'USD',
              value: data.value,
              items: data.items?.map(item => ({
                item_id: item.sku,
                item_name: item.name,
                category: item.category,
                quantity: item.quantity,
                price: item.price
              }))
            });
            break;

          case 'view_item':
            window.gtag('event', 'view_item', {
              currency: data.currency || 'USD',
              value: data.value,
              items: [{
                item_id: data.sku,
                item_name: data.name,
                category: data.category,
                price: data.price
              }]
            });
            break;
        }
      }

      // Log to database
      this.logEcommerceEvent(action, data);

    } catch (error) {
      console.error('❌ E-commerce tracking failed:', error);
    }
  }

  /**
   * Track performance metrics
   */
  trackPerformance(metricName, value, tags = {}) {
    try {
      // Sentry performance
      if (this.sentryInitialized) {
        Sentry.metrics.gauge(metricName, value, {
          tags,
          timestamp: Date.now() / 1000
        });
      }

      // Custom performance logging
      this.logPerformanceMetric(metricName, value, tags);

    } catch (error) {
      console.error('❌ Performance tracking failed:', error);
    }
  }

  /**
   * Track API response times
   */
  trackApiPerformance(endpoint, method, duration, statusCode) {
    this.trackPerformance('api_response_time', duration, {
      endpoint,
      method,
      status_code: statusCode.toString()
    });

    // Track errors
    if (statusCode >= 400) {
      this.trackEvent('api_error', {
        category: 'api',
        label: `${method} ${endpoint}`,
        value: statusCode
      });
    }
  }

  /**
   * Track business metrics
   */
  trackBusinessMetric(metricName, value, metadata = {}) {
    try {
      this.logBusinessMetric(metricName, value, metadata);
      
      // Send to external analytics if configured
      if (process.env.ANALYTICS_WEBHOOK_URL) {
        this.sendToExternalAnalytics(metricName, value, metadata);
      }

    } catch (error) {
      console.error('❌ Business metric tracking failed:', error);
    }
  }

  /**
   * Log custom events to database
   */
  async logCustomEvent(eventName, properties) {
    try {
      const client = await pool.connect();
      await client.query(`
        INSERT INTO analytics_events (
          event_name, properties, user_id, session_id, 
          page_url, user_agent, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
      `, [
        eventName,
        JSON.stringify(properties),
        properties.userId || null,
        properties.sessionId || null,
        properties.pageUrl || null,
        properties.userAgent || null
      ]);
      client.release();
    } catch (error) {
      console.error('❌ Failed to log custom event:', error);
    }
  }

  /**
   * Log e-commerce events to database
   */
  async logEcommerceEvent(action, data) {
    try {
      const client = await pool.connect();
      await client.query(`
        INSERT INTO ecommerce_events (
          action, order_id, user_id, value, currency, 
          items, metadata, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
      `, [
        action,
        data.orderId || null,
        data.userId || null,
        data.value || 0,
        data.currency || 'USD',
        JSON.stringify(data.items || []),
        JSON.stringify(data)
      ]);
      client.release();
    } catch (error) {
      console.error('❌ Failed to log e-commerce event:', error);
    }
  }

  /**
   * Log performance metrics to database
   */
  async logPerformanceMetric(metricName, value, tags) {
    try {
      const client = await pool.connect();
      await client.query(`
        INSERT INTO performance_metrics (
          metric_name, value, tags, created_at
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
      `, [
        metricName,
        value,
        JSON.stringify(tags)
      ]);
      client.release();
    } catch (error) {
      console.error('❌ Failed to log performance metric:', error);
    }
  }

  /**
   * Log business metrics to database
   */
  async logBusinessMetric(metricName, value, metadata) {
    try {
      const client = await pool.connect();
      await client.query(`
        INSERT INTO business_metrics (
          metric_name, value, metadata, date, created_at
        ) VALUES ($1, $2, $3, CURRENT_DATE, CURRENT_TIMESTAMP)
        ON CONFLICT (metric_name, date) 
        DO UPDATE SET value = $2, metadata = $3, updated_at = CURRENT_TIMESTAMP
      `, [
        metricName,
        value,
        JSON.stringify(metadata)
      ]);
      client.release();
    } catch (error) {
      console.error('❌ Failed to log business metric:', error);
    }
  }

  /**
   * Send metrics to external analytics service
   */
  async sendToExternalAnalytics(metricName, value, metadata) {
    try {
      if (process.env.ANALYTICS_WEBHOOK_URL) {
        const response = await fetch(process.env.ANALYTICS_WEBHOOK_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.ANALYTICS_API_KEY}`
          },
          body: JSON.stringify({
            metric: metricName,
            value,
            metadata,
            timestamp: new Date().toISOString(),
            source: 'midastechnical.com'
          })
        });

        if (!response.ok) {
          throw new Error(`Analytics API error: ${response.status}`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to send to external analytics:', error);
    }
  }

  /**
   * Get analytics dashboard data
   */
  async getDashboardMetrics(dateFrom, dateTo) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          -- Page views
          COUNT(CASE WHEN event_name = 'page_view' THEN 1 END) as page_views,
          
          -- E-commerce metrics
          COUNT(CASE WHEN event_name = 'purchase' THEN 1 END) as purchases,
          SUM(CASE WHEN event_name = 'purchase' THEN (properties->>'value')::numeric ELSE 0 END) as revenue,
          
          -- User engagement
          COUNT(DISTINCT properties->>'sessionId') as sessions,
          COUNT(DISTINCT properties->>'userId') as unique_users,
          
          -- Performance
          AVG(CASE WHEN event_name = 'page_load_time' THEN (properties->>'value')::numeric END) as avg_page_load_time
          
        FROM analytics_events 
        WHERE created_at >= $1 AND created_at <= $2
      `, [dateFrom, dateTo]);
      
      client.release();
      
      return result.rows[0];
    } catch (error) {
      console.error('❌ Failed to get dashboard metrics:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const monitoringService = new MonitoringService();

// Export class for testing
export default MonitoringService;

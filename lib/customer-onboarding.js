/**
 * Customer Onboarding System
 * Manages welcome sequences, account setup, and initial customer experience
 */

import { Pool } from 'pg';
import { productionEmailService } from './email-service-production.js';
import { monitoringService } from './monitoring.js';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class CustomerOnboardingService {
  constructor() {
    this.welcomeSequenceSteps = [
      { step: 1, delay: 0, template: 'welcome', subject: 'Welcome to Midas Technical!' },
      { step: 2, delay: 24, template: 'catalog_highlights', subject: 'Discover Our Premium Electronic Components' },
      { step: 3, delay: 72, template: 'repair_services', subject: 'Professional Repair Services Available' },
      { step: 4, delay: 120, template: 'loyalty_program', subject: 'Join Our VIP Loyalty Program' },
      { step: 5, delay: 168, template: 'first_purchase', subject: 'Exclusive 15% Off Your First Order!' }
    ];
  }

  /**
   * Start onboarding process for new customer
   */
  async startOnboarding(userId, userEmail, userName) {
    try {
      const client = await pool.connect();
      
      // Create onboarding record
      await client.query(`
        INSERT INTO customer_onboarding (
          user_id, email, name, status, started_at, current_step
        ) VALUES ($1, $2, $3, 'active', CURRENT_TIMESTAMP, 1)
        ON CONFLICT (user_id) DO UPDATE SET
          status = 'active',
          started_at = CURRENT_TIMESTAMP,
          current_step = 1
      `, [userId, userEmail, userName]);

      // Schedule welcome email sequence
      await this.scheduleWelcomeSequence(userId, userEmail, userName);

      // Track onboarding start
      await this.trackOnboardingEvent(userId, 'onboarding_started', {
        email: userEmail,
        name: userName
      });

      client.release();
      
      console.log(`✅ Onboarding started for user ${userId} (${userEmail})`);
      
      return { success: true, message: 'Onboarding started successfully' };

    } catch (error) {
      console.error('❌ Onboarding start failed:', error);
      throw error;
    }
  }

  /**
   * Schedule welcome email sequence
   */
  async scheduleWelcomeSequence(userId, userEmail, userName) {
    const client = await pool.connect();
    
    try {
      for (const step of this.welcomeSequenceSteps) {
        const sendAt = new Date();
        sendAt.setHours(sendAt.getHours() + step.delay);

        await client.query(`
          INSERT INTO email_campaigns_queue (
            user_id, email, template_type, subject, 
            scheduled_at, onboarding_step, status
          ) VALUES ($1, $2, $3, $4, $5, $6, 'scheduled')
        `, [
          userId, userEmail, step.template, step.subject,
          sendAt, step.step
        ]);
      }

      console.log(`📧 Welcome sequence scheduled for ${userEmail}`);

    } finally {
      client.release();
    }
  }

  /**
   * Process scheduled onboarding emails
   */
  async processScheduledEmails() {
    try {
      const client = await pool.connect();
      
      // Get emails ready to send
      const result = await client.query(`
        SELECT ecq.*, co.name, co.preferences
        FROM email_campaigns_queue ecq
        JOIN customer_onboarding co ON ecq.user_id = co.user_id
        WHERE ecq.status = 'scheduled' 
        AND ecq.scheduled_at <= CURRENT_TIMESTAMP
        AND co.status = 'active'
        ORDER BY ecq.scheduled_at
        LIMIT 50
      `);

      for (const email of result.rows) {
        await this.sendOnboardingEmail(email);
      }

      client.release();

    } catch (error) {
      console.error('❌ Processing scheduled emails failed:', error);
    }
  }

  /**
   * Send individual onboarding email
   */
  async sendOnboardingEmail(emailData) {
    try {
      const client = await pool.connect();
      
      // Get personalized content
      const content = await this.generateEmailContent(emailData);
      
      // Send email
      await productionEmailService.sendEmail(
        emailData.email,
        emailData.subject,
        content.html,
        content.text,
        content.templateData,
        content.templateId
      );

      // Update email status
      await client.query(`
        UPDATE email_campaigns_queue 
        SET status = 'sent', sent_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [emailData.id]);

      // Update onboarding progress
      await client.query(`
        UPDATE customer_onboarding 
        SET current_step = $1, last_email_sent = CURRENT_TIMESTAMP
        WHERE user_id = $2
      `, [emailData.onboarding_step, emailData.user_id]);

      // Track email sent
      await this.trackOnboardingEvent(emailData.user_id, 'onboarding_email_sent', {
        step: emailData.onboarding_step,
        template: emailData.template_type,
        subject: emailData.subject
      });

      client.release();
      
      console.log(`📧 Onboarding email sent: ${emailData.template_type} to ${emailData.email}`);

    } catch (error) {
      console.error(`❌ Failed to send onboarding email to ${emailData.email}:`, error);
      
      // Mark as failed
      const client = await pool.connect();
      await client.query(`
        UPDATE email_campaigns_queue 
        SET status = 'failed', error_message = $1
        WHERE id = $2
      `, [error.message, emailData.id]);
      client.release();
    }
  }

  /**
   * Generate personalized email content
   */
  async generateEmailContent(emailData) {
    const client = await pool.connect();
    
    try {
      // Get user preferences and data
      const userResult = await client.query(`
        SELECT u.*, co.preferences, co.device_interests
        FROM users u
        JOIN customer_onboarding co ON u.id = co.user_id
        WHERE u.id = $1
      `, [emailData.user_id]);

      const user = userResult.rows[0];
      
      // Get personalized product recommendations
      const recommendations = await this.getPersonalizedRecommendations(emailData.user_id);
      
      const templateData = {
        firstName: user.first_name || 'Valued Customer',
        email: user.email,
        recommendations,
        loyaltyPoints: user.loyalty_points || 0,
        discountCode: await this.generateDiscountCode(emailData.user_id, emailData.template_type),
        unsubscribeUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/unsubscribe?token=${user.id}`,
        preferencesUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/account/preferences`
      };

      // Get template configuration
      const templates = {
        welcome: {
          templateId: process.env.SENDGRID_WELCOME_TEMPLATE,
          html: this.generateWelcomeHTML(templateData),
          text: this.generateWelcomeText(templateData)
        },
        catalog_highlights: {
          templateId: process.env.SENDGRID_CATALOG_TEMPLATE,
          html: this.generateCatalogHTML(templateData),
          text: this.generateCatalogText(templateData)
        },
        repair_services: {
          templateId: process.env.SENDGRID_REPAIR_SERVICES_TEMPLATE,
          html: this.generateRepairServicesHTML(templateData),
          text: this.generateRepairServicesText(templateData)
        },
        loyalty_program: {
          templateId: process.env.SENDGRID_LOYALTY_TEMPLATE,
          html: this.generateLoyaltyHTML(templateData),
          text: this.generateLoyaltyText(templateData)
        },
        first_purchase: {
          templateId: process.env.SENDGRID_FIRST_PURCHASE_TEMPLATE,
          html: this.generateFirstPurchaseHTML(templateData),
          text: this.generateFirstPurchaseText(templateData)
        }
      };

      client.release();
      
      return {
        ...templates[emailData.template_type],
        templateData
      };

    } catch (error) {
      client.release();
      throw error;
    }
  }

  /**
   * Get personalized product recommendations
   */
  async getPersonalizedRecommendations(userId, limit = 6) {
    const client = await pool.connect();
    
    try {
      // Get user's browsing history and preferences
      const browsingResult = await client.query(`
        SELECT DISTINCT p.category_id, COUNT(*) as views
        FROM analytics_events ae
        JOIN products p ON (ae.properties->>'productId')::integer = p.id
        WHERE ae.user_id = $1 
        AND ae.event_name = 'product_view'
        AND ae.created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY p.category_id
        ORDER BY views DESC
        LIMIT 3
      `, [userId]);

      let categoryIds = browsingResult.rows.map(row => row.category_id);
      
      // If no browsing history, use popular categories
      if (categoryIds.length === 0) {
        const popularResult = await client.query(`
          SELECT category_id, COUNT(*) as sales
          FROM order_items oi
          JOIN products p ON oi.product_id = p.id
          JOIN orders o ON oi.order_id = o.id
          WHERE o.created_at >= CURRENT_DATE - INTERVAL '30 days'
          GROUP BY category_id
          ORDER BY sales DESC
          LIMIT 3
        `);
        categoryIds = popularResult.rows.map(row => row.category_id);
      }

      // Get recommended products
      const recommendationsResult = await client.query(`
        SELECT p.*, c.name as category_name,
               COALESCE(sales_stats.total_sold, 0) as popularity_score
        FROM products p
        JOIN categories c ON p.category_id = c.id
        LEFT JOIN (
          SELECT product_id, SUM(quantity) as total_sold
          FROM order_items oi
          JOIN orders o ON oi.order_id = o.id
          WHERE o.created_at >= CURRENT_DATE - INTERVAL '90 days'
          GROUP BY product_id
        ) sales_stats ON p.id = sales_stats.product_id
        WHERE p.is_active = true 
        AND p.stock_quantity > 0
        AND ($1::integer[] IS NULL OR p.category_id = ANY($1::integer[]))
        ORDER BY popularity_score DESC, p.created_at DESC
        LIMIT $2
      `, [categoryIds.length > 0 ? categoryIds : null, limit]);

      client.release();
      
      return recommendationsResult.rows.map(product => ({
        id: product.id,
        name: product.name,
        price: parseFloat(product.price),
        image: product.image_url,
        category: product.category_name,
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/products/${product.id}`
      }));

    } catch (error) {
      client.release();
      console.error('❌ Failed to get recommendations:', error);
      return [];
    }
  }

  /**
   * Generate discount code for onboarding
   */
  async generateDiscountCode(userId, templateType) {
    if (templateType !== 'first_purchase') return null;

    const client = await pool.connect();
    
    try {
      const code = `WELCOME${userId}${Date.now().toString().slice(-4)}`;
      
      await client.query(`
        INSERT INTO discount_codes (
          code, type, value, min_order_amount, max_uses, 
          user_id, expires_at, created_at
        ) VALUES ($1, 'percentage', 15, 50, 1, $2, 
                 CURRENT_TIMESTAMP + INTERVAL '30 days', CURRENT_TIMESTAMP)
      `, [code, userId]);

      client.release();
      return code;

    } catch (error) {
      client.release();
      console.error('❌ Failed to generate discount code:', error);
      return null;
    }
  }

  /**
   * Track onboarding events
   */
  async trackOnboardingEvent(userId, eventName, properties = {}) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO analytics_events (
          event_name, user_id, properties, created_at
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
      `, [eventName, userId, JSON.stringify(properties)]);

      client.release();

      // Track with monitoring service
      monitoringService.trackEvent(eventName, {
        userId,
        category: 'onboarding',
        ...properties
      });

    } catch (error) {
      console.error('❌ Failed to track onboarding event:', error);
    }
  }

  /**
   * Complete onboarding step
   */
  async completeOnboardingStep(userId, stepType, data = {}) {
    try {
      const client = await pool.connect();
      
      // Update onboarding progress
      await client.query(`
        UPDATE customer_onboarding 
        SET completed_steps = array_append(
          COALESCE(completed_steps, ARRAY[]::text[]), $2
        ),
        updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $1
      `, [userId, stepType]);

      // Track completion
      await this.trackOnboardingEvent(userId, 'onboarding_step_completed', {
        step: stepType,
        ...data
      });

      // Check if onboarding is complete
      const result = await client.query(`
        SELECT completed_steps
        FROM customer_onboarding
        WHERE user_id = $1
      `, [userId]);

      const completedSteps = result.rows[0]?.completed_steps || [];
      const requiredSteps = ['email_verified', 'profile_completed', 'first_browse'];
      
      if (requiredSteps.every(step => completedSteps.includes(step))) {
        await this.completeOnboarding(userId);
      }

      client.release();

    } catch (error) {
      console.error('❌ Failed to complete onboarding step:', error);
    }
  }

  /**
   * Complete full onboarding process
   */
  async completeOnboarding(userId) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        UPDATE customer_onboarding 
        SET status = 'completed', completed_at = CURRENT_TIMESTAMP
        WHERE user_id = $1
      `, [userId]);

      // Award welcome bonus
      await client.query(`
        UPDATE users 
        SET loyalty_points = COALESCE(loyalty_points, 0) + 100
        WHERE id = $1
      `, [userId]);

      // Track completion
      await this.trackOnboardingEvent(userId, 'onboarding_completed', {
        welcome_bonus: 100
      });

      client.release();
      
      console.log(`🎉 Onboarding completed for user ${userId}`);

    } catch (error) {
      console.error('❌ Failed to complete onboarding:', error);
    }
  }

  /**
   * Generate welcome email HTML
   */
  generateWelcomeHTML(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Welcome to Midas Technical, ${data.firstName}!</h1>
        <p>Thank you for joining our community of electronics professionals and enthusiasts.</p>
        <p>We're excited to help you find the perfect components for your projects and repairs.</p>
        
        <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>What's Next?</h3>
          <ul>
            <li>Browse our catalog of 565+ premium components</li>
            <li>Explore our professional repair services</li>
            <li>Join our VIP loyalty program for exclusive benefits</li>
          </ul>
        </div>
        
        <a href="${process.env.NEXT_PUBLIC_SITE_URL}/products" 
           style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
          Start Shopping
        </a>
        
        <p style="margin-top: 30px; font-size: 14px; color: #666;">
          Need help? Reply to this email or visit our <a href="${process.env.NEXT_PUBLIC_SITE_URL}/contact">support center</a>.
        </p>
      </div>
    `;
  }

  /**
   * Generate welcome email text
   */
  generateWelcomeText(data) {
    return `
Welcome to Midas Technical, ${data.firstName}!

Thank you for joining our community of electronics professionals and enthusiasts.

We're excited to help you find the perfect components for your projects and repairs.

What's Next?
- Browse our catalog of 565+ premium components
- Explore our professional repair services  
- Join our VIP loyalty program for exclusive benefits

Start shopping: ${process.env.NEXT_PUBLIC_SITE_URL}/products

Need help? Reply to this email or visit our support center.
    `;
  }

  // Additional email template generators would be implemented here...
  // (generateCatalogHTML, generateRepairServicesHTML, etc.)
}

// Export singleton instance
export const customerOnboardingService = new CustomerOnboardingService();

export default CustomerOnboardingService;

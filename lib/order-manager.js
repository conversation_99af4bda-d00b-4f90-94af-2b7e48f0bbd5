/**
 * Order Processing and Management System
 * Handles complete order lifecycle from creation to fulfillment
 */

import { Pool } from 'pg';
import { inventoryManager } from './inventory-manager.js';
import { sendOrderConfirmationEmail, sendShippingNotificationEmail, sendDeliveryConfirmationEmail } from './email-service.js';
import { shippingService } from './shipping-service.js';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export class OrderManager {
  constructor() {
    this.orderStatuses = {
      PENDING: 'pending',
      CONFIRMED: 'confirmed',
      PROCESSING: 'processing',
      READY_TO_SHIP: 'ready_to_ship',
      SHIPPED: 'shipped',
      OUT_FOR_DELIVERY: 'out_for_delivery',
      DELIVERED: 'delivered',
      CANCELLED: 'cancelled',
      REFUNDED: 'refunded',
      RETURNED: 'returned'
    };

    this.paymentStatuses = {
      PENDING: 'pending',
      PAID: 'paid',
      FAILED: 'failed',
      REFUNDED: 'refunded',
      PARTIALLY_REFUNDED: 'partially_refunded'
    };

    this.fulfillmentPriorities = {
      STANDARD: 'standard',
      EXPEDITED: 'expedited',
      URGENT: 'urgent'
    };
  }

  /**
   * Generate unique order number
   */
  generateOrderNumber() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `MDT-${timestamp}-${random}`;
  }

  /**
   * Create order from Stripe checkout session
   */
  async createOrderFromStripeSession(session) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      const { cart_id, user_id } = session.metadata;
      const orderNumber = this.generateOrderNumber();
      
      // Get cart items
      const cartItemsResult = await client.query(`
        SELECT ci.product_id, ci.quantity, p.name, p.price, p.discount_percentage, p.sku, p.weight
        FROM cart_items ci
        JOIN products p ON ci.product_id = p.id
        WHERE ci.cart_id = $1
      `, [cart_id]);

      if (cartItemsResult.rows.length === 0) {
        throw new Error('Cart is empty');
      }

      // Calculate totals
      let subtotal = 0;
      const orderItems = [];
      
      for (const item of cartItemsResult.rows) {
        const price = parseFloat(item.price);
        const discountPercentage = parseFloat(item.discount_percentage || 0);
        const discountedPrice = discountPercentage > 0 
          ? price * (1 - discountPercentage / 100) 
          : price;
        const itemTotal = discountedPrice * item.quantity;
        
        subtotal += itemTotal;
        
        orderItems.push({
          product_id: item.product_id,
          quantity: item.quantity,
          unit_price: discountedPrice,
          total_price: itemTotal,
          product_name: item.name,
          product_sku: item.sku
        });
      }

      // Extract customer and shipping info from Stripe session
      const customer = {
        name: session.customer_details.name,
        email: session.customer_details.email,
        phone: session.customer_details.phone || ''
      };

      const shippingAddress = session.shipping_details ? {
        name: session.shipping_details.name,
        address: session.shipping_details.address
      } : null;

      const billingAddress = session.customer_details.address;
      const shippingCost = session.shipping_cost ? session.shipping_cost.amount_total / 100 : 0;
      const totalAmount = subtotal + shippingCost;

      // Create order
      const orderResult = await client.query(`
        INSERT INTO orders (
          user_id, order_number, status, payment_status, subtotal, shipping_cost, total_amount,
          customer_name, customer_email, customer_phone,
          shipping_address, billing_address, payment_method,
          stripe_session_id, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `, [
        user_id || null,
        orderNumber,
        this.orderStatuses.PROCESSING,
        this.paymentStatuses.PAID,
        subtotal,
        shippingCost,
        totalAmount,
        customer.name,
        customer.email,
        customer.phone,
        JSON.stringify(shippingAddress),
        JSON.stringify(billingAddress),
        JSON.stringify({ type: 'stripe', session_id: session.id }),
        session.id
      ]);

      const orderId = orderResult.rows[0].id;

      // Create order items and process inventory
      for (const item of orderItems) {
        // Create order item
        await client.query(`
          INSERT INTO order_items (
            order_id, product_id, quantity, unit_price, total_price,
            product_name, product_sku
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          orderId,
          item.product_id,
          item.quantity,
          item.unit_price,
          item.total_price,
          item.product_name,
          item.product_sku
        ]);

        // Process inventory sale
        await inventoryManager.processSale(item.product_id, item.quantity, orderId);
      }

      // Clear cart
      await client.query('DELETE FROM cart_items WHERE cart_id = $1', [cart_id]);

      // Create order status history
      await client.query(`
        INSERT INTO order_status_history (
          order_id, old_status, new_status, notes, created_at
        ) VALUES ($1, NULL, $2, $3, CURRENT_TIMESTAMP)
      `, [orderId, this.orderStatuses.PROCESSING, 'Order created from successful payment']);

      await client.query('COMMIT');

      // Send confirmation email
      try {
        await sendOrderConfirmationEmail({
          orderNumber,
          customerEmail: customer.email,
          customerName: customer.name,
          orderItems,
          subtotal,
          shippingCost,
          totalAmount,
          shippingAddress
        });
      } catch (emailError) {
        console.error('❌ Failed to send order confirmation email:', emailError);
        // Don't fail the order creation for email issues
      }

      console.log(`✅ Order ${orderNumber} created successfully`);
      
      return {
        orderId,
        orderNumber,
        totalAmount,
        itemCount: orderItems.length
      };

    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Order creation failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(orderId, newStatus, notes = '', trackingNumber = null) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Get current order
      const orderResult = await client.query(
        'SELECT status, order_number, customer_email, customer_name FROM orders WHERE id = $1',
        [orderId]
      );
      
      if (orderResult.rows.length === 0) {
        throw new Error(`Order not found: ${orderId}`);
      }
      
      const order = orderResult.rows[0];
      const oldStatus = order.status;
      
      // Update order status
      const updateFields = ['status = $1', 'updated_at = CURRENT_TIMESTAMP'];
      const updateValues = [newStatus];
      let paramCount = 1;
      
      if (trackingNumber) {
        paramCount++;
        updateFields.push(`tracking_number = $${paramCount}`);
        updateValues.push(trackingNumber);
      }
      
      if (newStatus === this.orderStatuses.SHIPPED) {
        paramCount++;
        updateFields.push(`shipped_at = $${paramCount}`);
        updateValues.push(new Date());
      }
      
      if (newStatus === this.orderStatuses.DELIVERED) {
        paramCount++;
        updateFields.push(`delivered_at = $${paramCount}`);
        updateValues.push(new Date());
      }
      
      paramCount++;
      updateValues.push(orderId);
      
      await client.query(
        `UPDATE orders SET ${updateFields.join(', ')} WHERE id = $${paramCount}`,
        updateValues
      );
      
      // Add status history
      await client.query(`
        INSERT INTO order_status_history (
          order_id, old_status, new_status, notes, tracking_number, created_at
        ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `, [orderId, oldStatus, newStatus, notes, trackingNumber]);
      
      await client.query('COMMIT');
      
      // Send notification emails
      try {
        if (newStatus === this.orderStatuses.SHIPPED && trackingNumber) {
          await sendShippingNotificationEmail({
            orderNumber: order.order_number,
            customerEmail: order.customer_email,
            customerName: order.customer_name,
            trackingNumber
          });
        }
      } catch (emailError) {
        console.error('❌ Failed to send status notification email:', emailError);
      }
      
      console.log(`✅ Order ${order.order_number} status updated: ${oldStatus} → ${newStatus}`);
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Order status update failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Get order details
   */
  async getOrderDetails(orderId) {
    try {
      const orderResult = await pool.query(`
        SELECT 
          o.*,
          array_agg(
            json_build_object(
              'id', oi.id,
              'product_id', oi.product_id,
              'product_name', oi.product_name,
              'product_sku', oi.product_sku,
              'quantity', oi.quantity,
              'unit_price', oi.unit_price,
              'total_price', oi.total_price
            )
          ) as items
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.id = $1
        GROUP BY o.id
      `, [orderId]);
      
      if (orderResult.rows.length === 0) {
        return null;
      }
      
      const order = orderResult.rows[0];
      
      // Get status history
      const historyResult = await pool.query(`
        SELECT * FROM order_status_history 
        WHERE order_id = $1 
        ORDER BY created_at DESC
      `, [orderId]);
      
      order.status_history = historyResult.rows;
      
      return order;
    } catch (error) {
      console.error('❌ Failed to get order details:', error);
      throw error;
    }
  }

  /**
   * Get orders with filters
   */
  async getOrders(filters = {}) {
    try {
      const {
        status,
        payment_status,
        customer_email,
        order_number,
        date_from,
        date_to,
        limit = 50,
        offset = 0
      } = filters;
      
      let query = `
        SELECT 
          o.id, o.order_number, o.status, o.payment_status,
          o.total_amount, o.customer_name, o.customer_email,
          o.created_at, o.updated_at,
          COUNT(oi.id) as item_count
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE 1=1
      `;
      
      const params = [];
      let paramCount = 0;
      
      if (status) {
        paramCount++;
        query += ` AND o.status = $${paramCount}`;
        params.push(status);
      }
      
      if (payment_status) {
        paramCount++;
        query += ` AND o.payment_status = $${paramCount}`;
        params.push(payment_status);
      }
      
      if (customer_email) {
        paramCount++;
        query += ` AND o.customer_email ILIKE $${paramCount}`;
        params.push(`%${customer_email}%`);
      }
      
      if (order_number) {
        paramCount++;
        query += ` AND o.order_number ILIKE $${paramCount}`;
        params.push(`%${order_number}%`);
      }
      
      if (date_from) {
        paramCount++;
        query += ` AND o.created_at >= $${paramCount}`;
        params.push(date_from);
      }
      
      if (date_to) {
        paramCount++;
        query += ` AND o.created_at <= $${paramCount}`;
        params.push(date_to);
      }
      
      query += `
        GROUP BY o.id
        ORDER BY o.created_at DESC
        LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
      `;
      
      params.push(limit, offset);
      
      const result = await pool.query(query, params);
      return result.rows;
    } catch (error) {
      console.error('❌ Failed to get orders:', error);
      throw error;
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(orderId, reason = 'Customer request') {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Get order items to restore inventory
      const itemsResult = await client.query(
        'SELECT product_id, quantity FROM order_items WHERE order_id = $1',
        [orderId]
      );
      
      // Restore inventory for each item
      for (const item of itemsResult.rows) {
        await client.query(
          'UPDATE products SET stock_quantity = stock_quantity + $1 WHERE id = $2',
          [item.quantity, item.product_id]
        );
        
        // Log inventory restoration
        await client.query(`
          INSERT INTO inventory_transactions (
            product_id, transaction_type, quantity, reference_type, reference_id,
            notes, created_at
          ) VALUES ($1, 'returned', $2, 'order_cancellation', $3, $4, CURRENT_TIMESTAMP)
        `, [
          item.product_id,
          item.quantity,
          orderId,
          `Order cancellation - ${reason}`
        ]);
      }
      
      // Update order status
      await this.updateOrderStatus(orderId, this.orderStatuses.CANCELLED, reason);
      
      await client.query('COMMIT');
      
      console.log(`✅ Order ${orderId} cancelled successfully`);

    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Order cancellation failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Process order for fulfillment
   */
  async processOrderForFulfillment(orderId) {
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // Get order details
      const order = await this.getOrderDetails(orderId);
      if (!order) {
        throw new Error(`Order not found: ${orderId}`);
      }

      // Verify payment status
      if (order.payment_status !== this.paymentStatuses.PAID) {
        throw new Error(`Order payment not confirmed: ${order.order_number}`);
      }

      // Check inventory availability
      for (const item of order.items) {
        const available = await inventoryManager.checkAvailability(item.product_id, item.quantity);
        if (!available) {
          throw new Error(`Insufficient inventory for ${item.product_name} (SKU: ${item.product_sku})`);
        }
      }

      // Reserve inventory
      for (const item of order.items) {
        await inventoryManager.reserveInventory(item.product_id, item.quantity, orderId);
      }

      // Update order status to ready for shipping
      await this.updateOrderStatus(orderId, this.orderStatuses.READY_TO_SHIP, 'Order processed and ready for fulfillment');

      await client.query('COMMIT');

      console.log(`✅ Order ${order.order_number} processed for fulfillment`);

      return {
        orderId,
        orderNumber: order.order_number,
        status: this.orderStatuses.READY_TO_SHIP,
        itemCount: order.items.length
      };

    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Order fulfillment processing failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Generate shipping label and mark order as shipped
   */
  async shipOrder(orderId, shippingMethod = 'STANDARD', carrier = null) {
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // Get order details
      const order = await this.getOrderDetails(orderId);
      if (!order) {
        throw new Error(`Order not found: ${orderId}`);
      }

      // Verify order is ready to ship
      if (order.status !== this.orderStatuses.READY_TO_SHIP) {
        throw new Error(`Order ${order.order_number} is not ready to ship. Current status: ${order.status}`);
      }

      // Parse addresses
      const shippingAddress = JSON.parse(order.shipping_address);
      const billingAddress = JSON.parse(order.billing_address);

      // Calculate shipping details
      const shippingDetails = await shippingService.calculateShippingCost(
        order.items,
        shippingAddress,
        shippingMethod
      );

      // Generate shipping label
      const labelResult = await shippingService.generateShippingLabel(orderId, {
        carrier: carrier || shippingDetails.carrier,
        shippingMethod,
        fromAddress: {
          name: 'Midas Technical Store',
          line1: process.env.BUSINESS_ADDRESS || '123 Tech Street',
          city: 'Silicon Valley',
          state: 'CA',
          postal_code: '94000',
          country: 'US'
        },
        toAddress: shippingAddress,
        packageDetails: {
          weight: this.calculateTotalWeight(order.items),
          dimensions: this.calculatePackageDimensions(order.items),
          shippingCost: shippingDetails.cost
        }
      });

      // Update order with tracking information
      await client.query(`
        UPDATE orders
        SET tracking_number = $1, carrier = $2, shipping_method = $3, shipped_at = CURRENT_TIMESTAMP
        WHERE id = $4
      `, [labelResult.trackingNumber, labelResult.carrier, shippingMethod, orderId]);

      // Update order status
      await this.updateOrderStatus(
        orderId,
        this.orderStatuses.SHIPPED,
        `Shipped via ${labelResult.carrier}`,
        labelResult.trackingNumber
      );

      // Release reserved inventory (convert to sold)
      for (const item of order.items) {
        await inventoryManager.processSale(item.product_id, item.quantity, orderId);
      }

      await client.query('COMMIT');

      console.log(`✅ Order ${order.order_number} shipped with tracking: ${labelResult.trackingNumber}`);

      return {
        orderId,
        orderNumber: order.order_number,
        trackingNumber: labelResult.trackingNumber,
        carrier: labelResult.carrier,
        estimatedDelivery: labelResult.estimatedDelivery,
        labelUrl: labelResult.labelUrl
      };

    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Order shipping failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Mark order as delivered
   */
  async markOrderDelivered(orderId, deliveryNotes = '') {
    try {
      const order = await this.getOrderDetails(orderId);
      if (!order) {
        throw new Error(`Order not found: ${orderId}`);
      }

      await this.updateOrderStatus(
        orderId,
        this.orderStatuses.DELIVERED,
        deliveryNotes || 'Package delivered successfully'
      );

      // Send delivery confirmation email
      try {
        await sendDeliveryConfirmationEmail({
          orderNumber: order.order_number,
          customerEmail: order.customer_email,
          customerName: order.customer_name,
          trackingNumber: order.tracking_number,
          deliveredAt: new Date()
        });
      } catch (emailError) {
        console.error('❌ Failed to send delivery confirmation email:', emailError);
      }

      console.log(`✅ Order ${order.order_number} marked as delivered`);

      return {
        orderId,
        orderNumber: order.order_number,
        status: this.orderStatuses.DELIVERED,
        deliveredAt: new Date()
      };

    } catch (error) {
      console.error('❌ Failed to mark order as delivered:', error);
      throw error;
    }
  }

  /**
   * Calculate total weight of order items
   */
  calculateTotalWeight(items) {
    return items.reduce((total, item) => {
      const weight = parseFloat(item.weight || 0.1); // Default 0.1 kg
      return total + (weight * item.quantity);
    }, 0);
  }

  /**
   * Calculate package dimensions (simplified)
   */
  calculatePackageDimensions(items) {
    // Simplified calculation - in production, use actual product dimensions
    const itemCount = items.reduce((total, item) => total + item.quantity, 0);

    // Base package size + scaling for multiple items
    return {
      length: Math.min(30, 15 + Math.ceil(itemCount / 3) * 5), // cm
      width: Math.min(25, 12 + Math.ceil(itemCount / 4) * 3),  // cm
      height: Math.min(20, 8 + Math.ceil(itemCount / 5) * 2)   // cm
    };
  }

  /**
   * Get orders ready for fulfillment
   */
  async getOrdersReadyForFulfillment(limit = 50) {
    try {
      const result = await pool.query(`
        SELECT
          o.id, o.order_number, o.status, o.payment_status,
          o.total_amount, o.customer_name, o.customer_email,
          o.created_at, o.shipping_address,
          COUNT(oi.id) as item_count,
          SUM(oi.quantity) as total_items
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.status IN ($1, $2) AND o.payment_status = $3
        GROUP BY o.id
        ORDER BY
          CASE WHEN o.status = $1 THEN 1 ELSE 2 END,
          o.created_at ASC
        LIMIT $4
      `, [
        this.orderStatuses.CONFIRMED,
        this.orderStatuses.READY_TO_SHIP,
        this.paymentStatuses.PAID,
        limit
      ]);

      return result.rows;
    } catch (error) {
      console.error('❌ Failed to get orders ready for fulfillment:', error);
      throw error;
    }
  }

  /**
   * Get fulfillment metrics
   */
  async getFulfillmentMetrics(dateFrom, dateTo) {
    try {
      const result = await pool.query(`
        SELECT
          COUNT(*) as total_orders,
          COUNT(CASE WHEN status = $1 THEN 1 END) as pending_orders,
          COUNT(CASE WHEN status = $2 THEN 1 END) as processing_orders,
          COUNT(CASE WHEN status = $3 THEN 1 END) as ready_to_ship,
          COUNT(CASE WHEN status = $4 THEN 1 END) as shipped_orders,
          COUNT(CASE WHEN status = $5 THEN 1 END) as delivered_orders,
          AVG(EXTRACT(EPOCH FROM (shipped_at - created_at))/3600) as avg_processing_hours,
          AVG(EXTRACT(EPOCH FROM (delivered_at - shipped_at))/24/3600) as avg_delivery_days,
          SUM(total_amount) as total_revenue
        FROM orders
        WHERE created_at >= $6 AND created_at <= $7
      `, [
        this.orderStatuses.PENDING,
        this.orderStatuses.PROCESSING,
        this.orderStatuses.READY_TO_SHIP,
        this.orderStatuses.SHIPPED,
        this.orderStatuses.DELIVERED,
        dateFrom,
        dateTo
      ]);

      return result.rows[0];
    } catch (error) {
      console.error('❌ Failed to get fulfillment metrics:', error);
      throw error;
    }
  }

  /**
   * Bulk update order statuses
   */
  async bulkUpdateOrderStatus(orderIds, newStatus, notes = '') {
    const client = await pool.connect();
    const results = [];

    try {
      await client.query('BEGIN');

      for (const orderId of orderIds) {
        try {
          await this.updateOrderStatus(orderId, newStatus, notes);
          results.push({ orderId, success: true });
        } catch (error) {
          console.error(`❌ Failed to update order ${orderId}:`, error);
          results.push({ orderId, success: false, error: error.message });
        }
      }

      await client.query('COMMIT');

      const successCount = results.filter(r => r.success).length;
      console.log(`✅ Bulk update completed: ${successCount}/${orderIds.length} orders updated`);

      return results;

    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Bulk order update failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }
}

// Export singleton instance
export const orderManager = new OrderManager();

// Export class for testing
export default OrderManager;

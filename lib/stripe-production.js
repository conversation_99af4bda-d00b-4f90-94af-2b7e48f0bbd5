import { loadStripe } from '@stripe/stripe-js';
import Stripe from 'stripe';

// Production environment detection
const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';

// Environment-specific Stripe keys
const getStripeKeys = () => {
  if (isProduction) {
    return {
      publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_LIVE || process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
      secretKey: process.env.STRIPE_SECRET_KEY_LIVE || process.env.STRIPE_SECRET_KEY,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET_LIVE || process.env.STRIPE_WEBHOOK_SECRET
    };
  } else {
    return {
      publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_TEST || process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
      secretKey: process.env.STRIPE_SECRET_KEY_TEST || process.env.STRIPE_SECRET_KEY,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET_TEST || process.env.STRIPE_WEBHOOK_SECRET
    };
  }
};

const stripeKeys = getStripeKeys();

// Initialize Stripe on the client side with production configuration
let stripePromise;
export const getStripe = () => {
  if (!stripePromise) {
    if (!stripeKeys.publishableKey) {
      console.error('❌ Stripe publishable key not found');
      return null;
    }
    
    stripePromise = loadStripe(stripeKeys.publishableKey);
    console.log(`✅ Stripe client initialized in ${isProduction ? 'PRODUCTION' : 'TEST'} mode`);
  }
  return stripePromise;
};

// Production Stripe server configuration
let stripe;
try {
  if (stripeKeys.secretKey) {
    stripe = new Stripe(stripeKeys.secretKey, {
      apiVersion: '2023-10-16',
      typescript: true,
      telemetry: false,
      maxNetworkRetries: 3,
      timeout: 15000, // 15 seconds for production
      appInfo: {
        name: 'Midas Technical Store',
        version: '2.0.0',
        url: 'https://midastechnical.com',
        partner_id: 'pp_partner_midastechnical'
      }
    });
    
    console.log(`✅ Stripe server initialized in ${isProduction ? 'PRODUCTION' : 'TEST'} mode`);
    
    // Validate Stripe configuration in production
    if (isProduction) {
      validateProductionConfig();
    }
  } else {
    if (isProduction) {
      throw new Error('❌ STRIPE_SECRET_KEY is required in production environment');
    } else {
      console.warn('⚠️  STRIPE_SECRET_KEY not found. Using mock Stripe instance for development.');
      stripe = createMockStripe();
    }
  }
} catch (error) {
  console.error('❌ Failed to initialize Stripe:', error);
  
  if (isProduction) {
    throw new Error('Stripe configuration failed in production environment');
  } else {
    stripe = createMockStripe();
  }
}

// Validate production configuration
async function validateProductionConfig() {
  try {
    // Test Stripe connection
    await stripe.balance.retrieve();
    console.log('✅ Stripe production connection validated');
    
    // Validate webhook endpoint
    if (!stripeKeys.webhookSecret) {
      console.warn('⚠️  STRIPE_WEBHOOK_SECRET not configured - webhooks will not work');
    }
    
    // Check for required environment variables
    const requiredVars = [
      'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_LIVE',
      'STRIPE_SECRET_KEY_LIVE',
      'STRIPE_WEBHOOK_SECRET_LIVE'
    ];
    
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    if (missingVars.length > 0) {
      console.warn(`⚠️  Missing production environment variables: ${missingVars.join(', ')}`);
    }
    
  } catch (error) {
    console.error('❌ Stripe production validation failed:', error);
    throw error;
  }
}

// Mock Stripe for development
function createMockStripe() {
  return {
    checkout: {
      sessions: {
        create: async (params) => ({
          id: `cs_mock_${Date.now()}`,
          url: `/checkout/mock-session?amount=${params.line_items?.[0]?.price_data?.unit_amount || 0}`,
          payment_status: 'unpaid',
          metadata: params.metadata || {}
        }),
        retrieve: async (sessionId) => ({
          id: sessionId,
          payment_status: 'paid',
          customer_details: {
            name: 'Test Customer',
            email: '<EMAIL>'
          }
        })
      }
    },
    webhooks: {
      constructEvent: (payload, signature, secret) => ({
        type: 'checkout.session.completed',
        data: {
          object: {
            id: 'cs_mock_session',
            payment_status: 'paid',
            metadata: { test: true }
          }
        }
      })
    },
    balance: {
      retrieve: async () => ({
        available: [{ amount: 0, currency: 'usd' }],
        pending: [{ amount: 0, currency: 'usd' }]
      })
    },
    paymentIntents: {
      create: async (params) => ({
        id: `pi_mock_${Date.now()}`,
        client_secret: `pi_mock_${Date.now()}_secret`,
        status: 'requires_payment_method'
      })
    }
  };
}

// Enhanced checkout session creation for production
export const createProductionCheckoutSession = async (params) => {
  const {
    lineItems,
    customerEmail,
    metadata = {},
    successUrl,
    cancelUrl,
    shippingOptions = [],
    allowedCountries = ['US', 'CA', 'GB', 'AU'],
    collectPhoneNumber = true,
    collectBillingAddress = true,
    mode = 'payment'
  } = params;

  try {
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card', 'link'],
      billing_address_collection: collectBillingAddress ? 'required' : 'auto',
      shipping_address_collection: {
        allowed_countries: allowedCountries,
      },
      phone_number_collection: {
        enabled: collectPhoneNumber
      },
      line_items: lineItems,
      mode: mode,
      success_url: successUrl || `${process.env.NEXTAUTH_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: cancelUrl || `${process.env.NEXTAUTH_URL}/checkout/cancel`,
      metadata: {
        ...metadata,
        environment: isProduction ? 'production' : 'test',
        created_at: new Date().toISOString(),
        domain: 'midastechnical.com'
      },
      shipping_options: shippingOptions.length > 0 ? shippingOptions : [
        {
          shipping_rate_data: {
            type: 'fixed_amount',
            fixed_amount: {
              amount: 0,
              currency: 'usd',
            },
            display_name: 'Free shipping',
            delivery_estimate: {
              minimum: {
                unit: 'business_day',
                value: 5,
              },
              maximum: {
                unit: 'business_day',
                value: 7,
              },
            },
          },
        },
        {
          shipping_rate_data: {
            type: 'fixed_amount',
            fixed_amount: {
              amount: 1500, // $15.00
              currency: 'usd',
            },
            display_name: 'Express shipping',
            delivery_estimate: {
              minimum: {
                unit: 'business_day',
                value: 1,
              },
              maximum: {
                unit: 'business_day',
                value: 2,
              },
            },
          },
        },
      ],
      expires_at: Math.floor(Date.now() / 1000) + (30 * 60), // 30 minutes
      customer_email: customerEmail,
      automatic_tax: {
        enabled: true,
      },
      tax_id_collection: {
        enabled: false,
      },
      consent_collection: {
        terms_of_service: 'required',
      },
      custom_fields: [
        {
          key: 'order_notes',
          label: {
            type: 'custom',
            custom: 'Order Notes (Optional)'
          },
          type: 'text',
          optional: true
        }
      ]
    });

    console.log(`✅ Checkout session created: ${session.id}`);
    
    return {
      sessionId: session.id,
      url: session.url,
      success: true
    };

  } catch (error) {
    console.error('❌ Failed to create checkout session:', error);
    throw new Error(`Checkout session creation failed: ${error.message}`);
  }
};

// Webhook signature validation
export const validateWebhookSignature = (payload, signature, secret) => {
  try {
    const event = stripe.webhooks.constructEvent(payload, signature, secret || stripeKeys.webhookSecret);
    return { event, valid: true };
  } catch (error) {
    console.error('❌ Webhook signature validation failed:', error);
    return { event: null, valid: false, error: error.message };
  }
};

// Export configuration
export const stripeConfig = {
  isProduction,
  keys: stripeKeys,
  instance: stripe
};

export default stripe;

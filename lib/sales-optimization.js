/**
 * Sales Optimization System
 * Handles abandoned cart recovery, upselling, cross-selling, and dynamic pricing
 */

import { Pool } from 'pg';
import { productionEmailService } from './email-service-production.js';
import { monitoringService } from './monitoring.js';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class SalesOptimizationService {
  constructor() {
    this.abandonedCartThresholds = {
      first_reminder: 1, // 1 hour
      second_reminder: 24, // 24 hours
      final_reminder: 72 // 72 hours
    };

    this.discountTiers = {
      first_reminder: 5, // 5% discount
      second_reminder: 10, // 10% discount
      final_reminder: 15 // 15% discount
    };
  }

  /**
   * Track cart abandonment
   */
  async trackCartAbandonment(userId, cartData) {
    try {
      const client = await pool.connect();
      
      // Calculate cart value
      const cartValue = cartData.items.reduce((total, item) => 
        total + (parseFloat(item.price) * item.quantity), 0
      );

      // Save abandoned cart
      await client.query(`
        INSERT INTO abandoned_carts (
          user_id, cart_data, cart_value, items_count,
          created_at, last_updated
        ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id) DO UPDATE SET
          cart_data = $2,
          cart_value = $3,
          items_count = $4,
          last_updated = CURRENT_TIMESTAMP,
          recovery_emails_sent = 0
      `, [
        userId,
        JSON.stringify(cartData),
        cartValue,
        cartData.items.length
      ]);

      // Track abandonment event
      await this.trackSalesEvent(userId, 'cart_abandoned', {
        cart_value: cartValue,
        items_count: cartData.items.length,
        items: cartData.items.map(item => ({
          product_id: item.productId,
          quantity: item.quantity,
          price: item.price
        }))
      });

      client.release();
      
      console.log(`🛒 Cart abandonment tracked for user ${userId}, value: $${cartValue.toFixed(2)}`);

    } catch (error) {
      console.error('❌ Failed to track cart abandonment:', error);
    }
  }

  /**
   * Process abandoned cart recovery emails
   */
  async processAbandonedCartRecovery() {
    try {
      const client = await pool.connect();
      
      // Get abandoned carts ready for recovery emails
      const result = await client.query(`
        SELECT ac.*, u.email, u.first_name, u.last_name
        FROM abandoned_carts ac
        JOIN users u ON ac.user_id = u.id
        WHERE ac.cart_value >= 25 -- Minimum cart value for recovery
        AND ac.recovery_emails_sent < 3
        AND (
          (ac.recovery_emails_sent = 0 AND ac.last_updated <= CURRENT_TIMESTAMP - INTERVAL '1 hour') OR
          (ac.recovery_emails_sent = 1 AND ac.last_updated <= CURRENT_TIMESTAMP - INTERVAL '24 hours') OR
          (ac.recovery_emails_sent = 2 AND ac.last_updated <= CURRENT_TIMESTAMP - INTERVAL '72 hours')
        )
        ORDER BY ac.cart_value DESC
        LIMIT 50
      `);

      for (const cart of result.rows) {
        await this.sendAbandonedCartEmail(cart);
      }

      client.release();

    } catch (error) {
      console.error('❌ Failed to process abandoned cart recovery:', error);
    }
  }

  /**
   * Send abandoned cart recovery email
   */
  async sendAbandonedCartEmail(cartData) {
    try {
      const client = await pool.connect();
      
      const emailsSent = cartData.recovery_emails_sent;
      const discountPercentage = this.discountTiers[
        emailsSent === 0 ? 'first_reminder' :
        emailsSent === 1 ? 'second_reminder' : 'final_reminder'
      ];

      // Generate discount code
      const discountCode = await this.generateDiscountCode(
        cartData.user_id,
        discountPercentage,
        'cart_recovery'
      );

      // Parse cart data
      const cart = JSON.parse(cartData.cart_data);
      
      // Get updated product information
      const productIds = cart.items.map(item => item.productId);
      const productsResult = await client.query(`
        SELECT id, name, price, image_url, stock_quantity
        FROM products
        WHERE id = ANY($1::integer[])
      `, [productIds]);

      const products = productsResult.rows;
      const cartItems = cart.items.map(item => {
        const product = products.find(p => p.id === item.productId);
        return {
          ...item,
          name: product?.name || 'Product',
          currentPrice: product?.price || item.price,
          image: product?.image_url,
          inStock: product?.stock_quantity > 0
        };
      });

      // Send recovery email
      const emailContent = this.generateAbandonedCartEmail({
        firstName: cartData.first_name,
        cartItems,
        cartValue: cartData.cart_value,
        discountCode,
        discountPercentage,
        emailNumber: emailsSent + 1
      });

      await productionEmailService.sendEmail(
        cartData.email,
        emailContent.subject,
        emailContent.html,
        emailContent.text
      );

      // Update recovery email count
      await client.query(`
        UPDATE abandoned_carts 
        SET recovery_emails_sent = recovery_emails_sent + 1,
            last_recovery_email = CURRENT_TIMESTAMP
        WHERE user_id = $1
      `, [cartData.user_id]);

      // Track recovery email sent
      await this.trackSalesEvent(cartData.user_id, 'cart_recovery_email_sent', {
        email_number: emailsSent + 1,
        discount_percentage: discountPercentage,
        cart_value: cartData.cart_value
      });

      client.release();
      
      console.log(`📧 Cart recovery email sent to ${cartData.email} (${emailsSent + 1}/3)`);

    } catch (error) {
      console.error(`❌ Failed to send cart recovery email to ${cartData.email}:`, error);
    }
  }

  /**
   * Get product recommendations for upselling/cross-selling
   */
  async getProductRecommendations(productId, userId = null, type = 'related') {
    const client = await pool.connect();
    
    try {
      let recommendations = [];

      if (type === 'frequently_bought_together') {
        // Get products frequently bought together
        const result = await client.query(`
          SELECT p.*, COUNT(*) as frequency,
                 COALESCE(sales_stats.total_sold, 0) as popularity
          FROM products p
          JOIN order_items oi1 ON p.id = oi1.product_id
          JOIN order_items oi2 ON oi1.order_id = oi2.order_id
          LEFT JOIN (
            SELECT product_id, SUM(quantity) as total_sold
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.created_at >= CURRENT_DATE - INTERVAL '90 days'
            GROUP BY product_id
          ) sales_stats ON p.id = sales_stats.product_id
          WHERE oi2.product_id = $1 
          AND p.id != $1
          AND p.is_active = true
          AND p.stock_quantity > 0
          GROUP BY p.id, sales_stats.total_sold
          ORDER BY frequency DESC, popularity DESC
          LIMIT 6
        `, [productId]);
        
        recommendations = result.rows;

      } else if (type === 'customers_also_viewed') {
        // Get products viewed by users who viewed this product
        const result = await client.query(`
          SELECT p.*, COUNT(*) as view_frequency,
                 COALESCE(sales_stats.total_sold, 0) as popularity
          FROM products p
          JOIN analytics_events ae1 ON p.id = (ae1.properties->>'productId')::integer
          JOIN analytics_events ae2 ON ae1.user_id = ae2.user_id
          LEFT JOIN (
            SELECT product_id, SUM(quantity) as total_sold
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.created_at >= CURRENT_DATE - INTERVAL '90 days'
            GROUP BY product_id
          ) sales_stats ON p.id = sales_stats.product_id
          WHERE ae2.properties->>'productId' = $1::text
          AND ae1.event_name = 'product_view'
          AND ae2.event_name = 'product_view'
          AND p.id != $1
          AND p.is_active = true
          AND p.stock_quantity > 0
          AND ae1.created_at >= CURRENT_DATE - INTERVAL '30 days'
          GROUP BY p.id, sales_stats.total_sold
          ORDER BY view_frequency DESC, popularity DESC
          LIMIT 6
        `, [productId]);
        
        recommendations = result.rows;

      } else if (type === 'category_bestsellers') {
        // Get bestsellers from the same category
        const result = await client.query(`
          SELECT p.*, COALESCE(sales_stats.total_sold, 0) as popularity
          FROM products p
          LEFT JOIN (
            SELECT product_id, SUM(quantity) as total_sold
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.created_at >= CURRENT_DATE - INTERVAL '90 days'
            GROUP BY product_id
          ) sales_stats ON p.id = sales_stats.product_id
          WHERE p.category_id = (SELECT category_id FROM products WHERE id = $1)
          AND p.id != $1
          AND p.is_active = true
          AND p.stock_quantity > 0
          ORDER BY popularity DESC, p.created_at DESC
          LIMIT 6
        `, [productId]);
        
        recommendations = result.rows;

      } else if (type === 'personalized' && userId) {
        // Get personalized recommendations based on user behavior
        recommendations = await this.getPersonalizedRecommendations(userId);
      }

      client.release();
      
      return recommendations.map(product => ({
        id: product.id,
        name: product.name,
        price: parseFloat(product.price),
        image: product.image_url,
        category: product.category_name,
        popularity: product.popularity || 0,
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/products/${product.id}`
      }));

    } catch (error) {
      client.release();
      console.error('❌ Failed to get product recommendations:', error);
      return [];
    }
  }

  /**
   * Calculate dynamic pricing
   */
  async calculateDynamicPricing(productId, userId = null, quantity = 1) {
    const client = await pool.connect();
    
    try {
      // Get base product price
      const productResult = await client.query(`
        SELECT price, category_id, stock_quantity
        FROM products
        WHERE id = $1
      `, [productId]);

      if (productResult.rows.length === 0) {
        throw new Error('Product not found');
      }

      const product = productResult.rows[0];
      let finalPrice = parseFloat(product.price);
      const discounts = [];

      // Bulk discount tiers
      if (quantity >= 10) {
        const bulkDiscount = quantity >= 50 ? 15 : quantity >= 25 ? 10 : 5;
        finalPrice *= (1 - bulkDiscount / 100);
        discounts.push({
          type: 'bulk',
          percentage: bulkDiscount,
          description: `${bulkDiscount}% bulk discount for ${quantity}+ items`
        });
      }

      // Loyalty member pricing
      if (userId) {
        const userResult = await client.query(`
          SELECT loyalty_tier, loyalty_points
          FROM users
          WHERE id = $1
        `, [userId]);

        if (userResult.rows.length > 0) {
          const user = userResult.rows[0];
          const loyaltyDiscount = user.loyalty_tier === 'gold' ? 10 : 
                                 user.loyalty_tier === 'silver' ? 5 : 0;
          
          if (loyaltyDiscount > 0) {
            finalPrice *= (1 - loyaltyDiscount / 100);
            discounts.push({
              type: 'loyalty',
              percentage: loyaltyDiscount,
              description: `${loyaltyDiscount}% ${user.loyalty_tier} member discount`
            });
          }
        }
      }

      // Seasonal promotions
      const seasonalResult = await client.query(`
        SELECT discount_percentage, description
        FROM seasonal_promotions
        WHERE is_active = true
        AND start_date <= CURRENT_DATE
        AND end_date >= CURRENT_DATE
        AND (category_ids IS NULL OR $1 = ANY(category_ids))
        ORDER BY discount_percentage DESC
        LIMIT 1
      `, [product.category_id]);

      if (seasonalResult.rows.length > 0) {
        const promotion = seasonalResult.rows[0];
        finalPrice *= (1 - promotion.discount_percentage / 100);
        discounts.push({
          type: 'seasonal',
          percentage: promotion.discount_percentage,
          description: promotion.description
        });
      }

      // Low stock urgency pricing (slight increase)
      if (product.stock_quantity <= 5) {
        finalPrice *= 1.02; // 2% increase for low stock
        discounts.push({
          type: 'urgency',
          percentage: -2,
          description: 'Low stock - limited availability'
        });
      }

      client.release();

      return {
        originalPrice: parseFloat(product.price),
        finalPrice: Math.round(finalPrice * 100) / 100,
        savings: Math.round((parseFloat(product.price) - finalPrice) * 100) / 100,
        discounts,
        quantity
      };

    } catch (error) {
      client.release();
      console.error('❌ Failed to calculate dynamic pricing:', error);
      return {
        originalPrice: 0,
        finalPrice: 0,
        savings: 0,
        discounts: [],
        quantity
      };
    }
  }

  /**
   * Generate discount code
   */
  async generateDiscountCode(userId, percentage, type = 'general') {
    const client = await pool.connect();
    
    try {
      const code = `${type.toUpperCase()}${userId}${Date.now().toString().slice(-4)}`;
      
      await client.query(`
        INSERT INTO discount_codes (
          code, type, value, user_id, usage_type,
          expires_at, created_at
        ) VALUES ($1, 'percentage', $2, $3, $4,
                 CURRENT_TIMESTAMP + INTERVAL '7 days', CURRENT_TIMESTAMP)
      `, [code, percentage, userId, type]);

      client.release();
      return code;

    } catch (error) {
      client.release();
      console.error('❌ Failed to generate discount code:', error);
      return null;
    }
  }

  /**
   * Track sales events
   */
  async trackSalesEvent(userId, eventName, properties = {}) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO analytics_events (
          event_name, user_id, properties, created_at
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
      `, [eventName, userId, JSON.stringify(properties)]);

      client.release();

      // Track with monitoring service
      monitoringService.trackEvent(eventName, {
        userId,
        category: 'sales_optimization',
        ...properties
      });

    } catch (error) {
      console.error('❌ Failed to track sales event:', error);
    }
  }

  /**
   * Generate abandoned cart email content
   */
  generateAbandonedCartEmail(data) {
    const { firstName, cartItems, cartValue, discountCode, discountPercentage, emailNumber } = data;
    
    const subjects = [
      `${firstName}, you left something in your cart`,
      `Don't miss out! ${discountPercentage}% off your cart`,
      `Last chance: ${discountPercentage}% off expires soon`
    ];

    const itemsHtml = cartItems.map(item => `
      <tr>
        <td style="padding: 10px;">
          <img src="${item.image || '/placeholder.jpg'}" alt="${item.name}" style="width: 60px; height: 60px; object-fit: cover;">
        </td>
        <td style="padding: 10px;">
          <strong>${item.name}</strong><br>
          Quantity: ${item.quantity}<br>
          ${item.inStock ? '<span style="color: green;">✓ In Stock</span>' : '<span style="color: red;">⚠ Limited Stock</span>'}
        </td>
        <td style="padding: 10px; text-align: right;">
          $${(parseFloat(item.currentPrice) * item.quantity).toFixed(2)}
        </td>
      </tr>
    `).join('');

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Don't Let These Items Get Away!</h1>
        <p>Hi ${firstName},</p>
        <p>You left some great items in your cart. Complete your purchase now and save ${discountPercentage}% with code <strong>${discountCode}</strong>!</p>
        
        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
          ${itemsHtml}
          <tr style="border-top: 2px solid #ddd;">
            <td colspan="2" style="padding: 10px; text-align: right;"><strong>Total:</strong></td>
            <td style="padding: 10px; text-align: right;"><strong>$${cartValue.toFixed(2)}</strong></td>
          </tr>
        </table>
        
        <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
          <h3 style="color: #dc2626;">Limited Time: ${discountPercentage}% OFF</h3>
          <p>Use code: <strong style="font-size: 18px; color: #2563eb;">${discountCode}</strong></p>
          <a href="${process.env.NEXT_PUBLIC_SITE_URL}/cart?recovery=true" 
             style="background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin-top: 10px;">
            Complete Your Purchase
          </a>
        </div>
        
        <p style="font-size: 14px; color: #666;">
          This offer expires in 48 hours. Items in your cart are not reserved and may sell out.
        </p>
      </div>
    `;

    const text = `
Hi ${firstName},

You left some great items in your cart. Complete your purchase now and save ${discountPercentage}% with code ${discountCode}!

Your Cart:
${cartItems.map(item => `- ${item.name} (Qty: ${item.quantity}) - $${(parseFloat(item.currentPrice) * item.quantity).toFixed(2)}`).join('\n')}

Total: $${cartValue.toFixed(2)}

Complete your purchase: ${process.env.NEXT_PUBLIC_SITE_URL}/cart?recovery=true

This offer expires in 48 hours.
    `;

    return {
      subject: subjects[emailNumber - 1],
      html,
      text
    };
  }
}

// Export singleton instance
export const salesOptimizationService = new SalesOptimizationService();

export default SalesOptimizationService;

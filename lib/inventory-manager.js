/**
 * Real-time Inventory Management System
 * Handles stock tracking, updates, and low stock alerts
 */

import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export class InventoryManager {
  constructor() {
    this.lowStockThreshold = 10;
    this.criticalStockThreshold = 5;
    this.reorderQuantity = 50;
  }

  /**
   * Check product availability and reserve stock
   */
  async checkAndReserveStock(productId, quantity) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Get current stock with row lock
      const stockResult = await client.query(
        'SELECT stock_quantity, reserved_quantity, name, sku FROM products WHERE id = $1 FOR UPDATE',
        [productId]
      );
      
      if (stockResult.rows.length === 0) {
        throw new Error(`Product not found: ${productId}`);
      }
      
      const product = stockResult.rows[0];
      const availableStock = product.stock_quantity - (product.reserved_quantity || 0);
      
      if (availableStock < quantity) {
        throw new Error(`Insufficient stock. Available: ${availableStock}, Requested: ${quantity}`);
      }
      
      // Reserve the stock
      await client.query(
        'UPDATE products SET reserved_quantity = COALESCE(reserved_quantity, 0) + $1 WHERE id = $2',
        [quantity, productId]
      );
      
      // Log the reservation
      await client.query(`
        INSERT INTO inventory_transactions (
          product_id, transaction_type, quantity, reference_type, reference_id,
          notes, created_at
        ) VALUES ($1, 'reserved', $2, 'cart', $3, $4, CURRENT_TIMESTAMP)
      `, [
        productId,
        quantity,
        null, // Will be updated with cart/order ID
        `Reserved ${quantity} units of ${product.name} (${product.sku})`
      ]);
      
      await client.query('COMMIT');
      
      console.log(`✅ Reserved ${quantity} units of product ${productId}`);
      
      return {
        success: true,
        reserved: quantity,
        availableStock: availableStock - quantity,
        product: product
      };
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Stock reservation failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Release reserved stock (e.g., when cart is abandoned)
   */
  async releaseReservedStock(productId, quantity, reason = 'Cart abandoned') {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Release the reserved stock
      await client.query(
        'UPDATE products SET reserved_quantity = GREATEST(COALESCE(reserved_quantity, 0) - $1, 0) WHERE id = $2',
        [quantity, productId]
      );
      
      // Log the release
      await client.query(`
        INSERT INTO inventory_transactions (
          product_id, transaction_type, quantity, reference_type,
          notes, created_at
        ) VALUES ($1, 'released', $2, 'system', $3, CURRENT_TIMESTAMP)
      `, [productId, quantity, reason]);
      
      await client.query('COMMIT');
      
      console.log(`✅ Released ${quantity} reserved units of product ${productId}`);
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Stock release failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Process actual sale and update stock
   */
  async processSale(productId, quantity, orderId) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Get current stock
      const stockResult = await client.query(
        'SELECT stock_quantity, reserved_quantity, name, sku FROM products WHERE id = $1 FOR UPDATE',
        [productId]
      );
      
      if (stockResult.rows.length === 0) {
        throw new Error(`Product not found: ${productId}`);
      }
      
      const product = stockResult.rows[0];
      
      // Update stock quantities
      await client.query(`
        UPDATE products SET 
          stock_quantity = stock_quantity - $1,
          reserved_quantity = GREATEST(COALESCE(reserved_quantity, 0) - $1, 0),
          total_sold = COALESCE(total_sold, 0) + $1,
          last_sold_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [quantity, productId]);
      
      // Log the sale
      await client.query(`
        INSERT INTO inventory_transactions (
          product_id, transaction_type, quantity, reference_type, reference_id,
          notes, created_at
        ) VALUES ($1, 'sold', $2, 'order', $3, $4, CURRENT_TIMESTAMP)
      `, [
        productId,
        quantity,
        orderId,
        `Sold ${quantity} units of ${product.name} (${product.sku}) - Order #${orderId}`
      ]);
      
      // Check for low stock alerts
      const newStock = product.stock_quantity - quantity;
      if (newStock <= this.criticalStockThreshold) {
        await this.createStockAlert(productId, 'critical', newStock);
      } else if (newStock <= this.lowStockThreshold) {
        await this.createStockAlert(productId, 'low', newStock);
      }
      
      await client.query('COMMIT');
      
      console.log(`✅ Processed sale: ${quantity} units of product ${productId}`);
      
      return {
        success: true,
        newStock: newStock,
        alertLevel: newStock <= this.criticalStockThreshold ? 'critical' : 
                   newStock <= this.lowStockThreshold ? 'low' : 'normal'
      };
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Sale processing failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Add stock (restocking)
   */
  async addStock(productId, quantity, cost, supplier, notes = '') {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Update stock quantity
      await client.query(
        'UPDATE products SET stock_quantity = stock_quantity + $1, cost_price = $2 WHERE id = $3',
        [quantity, cost, productId]
      );
      
      // Log the restock
      await client.query(`
        INSERT INTO inventory_transactions (
          product_id, transaction_type, quantity, reference_type, reference_id,
          cost_per_unit, supplier, notes, created_at
        ) VALUES ($1, 'restocked', $2, 'purchase', $3, $4, $5, $6, CURRENT_TIMESTAMP)
      `, [productId, quantity, null, cost, supplier, notes]);
      
      // Clear any low stock alerts for this product
      await client.query(
        'UPDATE stock_alerts SET resolved_at = CURRENT_TIMESTAMP WHERE product_id = $1 AND resolved_at IS NULL',
        [productId]
      );
      
      await client.query('COMMIT');
      
      console.log(`✅ Added ${quantity} units to product ${productId}`);
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Stock addition failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Create stock alert
   */
  async createStockAlert(productId, alertType, currentStock) {
    try {
      // Check if alert already exists
      const existingAlert = await pool.query(
        'SELECT id FROM stock_alerts WHERE product_id = $1 AND alert_type = $2 AND resolved_at IS NULL',
        [productId, alertType]
      );
      
      if (existingAlert.rows.length === 0) {
        await pool.query(`
          INSERT INTO stock_alerts (
            product_id, alert_type, current_stock, threshold,
            message, created_at
          ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
        `, [
          productId,
          alertType,
          currentStock,
          alertType === 'critical' ? this.criticalStockThreshold : this.lowStockThreshold,
          `${alertType.toUpperCase()} STOCK: Only ${currentStock} units remaining`
        ]);
        
        console.log(`⚠️  Created ${alertType} stock alert for product ${productId}`);
      }
    } catch (error) {
      console.error('❌ Failed to create stock alert:', error);
    }
  }

  /**
   * Get low stock products
   */
  async getLowStockProducts() {
    try {
      const result = await pool.query(`
        SELECT 
          p.id, p.name, p.sku, p.stock_quantity, p.reserved_quantity,
          (p.stock_quantity - COALESCE(p.reserved_quantity, 0)) as available_stock,
          c.name as category_name,
          CASE 
            WHEN p.stock_quantity <= $1 THEN 'critical'
            WHEN p.stock_quantity <= $2 THEN 'low'
            ELSE 'normal'
          END as alert_level
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.stock_quantity <= $2 AND p.is_active = true
        ORDER BY p.stock_quantity ASC, p.name
      `, [this.criticalStockThreshold, this.lowStockThreshold]);
      
      return result.rows;
    } catch (error) {
      console.error('❌ Failed to get low stock products:', error);
      throw error;
    }
  }

  /**
   * Get inventory statistics
   */
  async getInventoryStats() {
    try {
      const stats = await pool.query(`
        SELECT 
          COUNT(*) as total_products,
          SUM(stock_quantity) as total_units,
          SUM(stock_quantity * price) as total_value,
          SUM(CASE WHEN stock_quantity <= $1 THEN 1 ELSE 0 END) as critical_stock_count,
          SUM(CASE WHEN stock_quantity <= $2 AND stock_quantity > $1 THEN 1 ELSE 0 END) as low_stock_count,
          SUM(CASE WHEN stock_quantity = 0 THEN 1 ELSE 0 END) as out_of_stock_count,
          AVG(stock_quantity) as avg_stock_level
        FROM products 
        WHERE is_active = true
      `, [this.criticalStockThreshold, this.lowStockThreshold]);
      
      const topSelling = await pool.query(`
        SELECT 
          p.id, p.name, p.sku, 
          COALESCE(p.total_sold, 0) as total_sold,
          p.stock_quantity
        FROM products p
        WHERE p.is_active = true
        ORDER BY COALESCE(p.total_sold, 0) DESC
        LIMIT 10
      `);
      
      return {
        overview: stats.rows[0],
        topSelling: topSelling.rows
      };
    } catch (error) {
      console.error('❌ Failed to get inventory stats:', error);
      throw error;
    }
  }

  /**
   * Bulk update stock levels
   */
  async bulkUpdateStock(updates) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      for (const update of updates) {
        const { productId, newStock, reason } = update;
        
        // Get current stock
        const currentResult = await client.query(
          'SELECT stock_quantity, name, sku FROM products WHERE id = $1',
          [productId]
        );
        
        if (currentResult.rows.length === 0) {
          console.warn(`⚠️  Product not found: ${productId}`);
          continue;
        }
        
        const currentStock = currentResult.rows[0].stock_quantity;
        const difference = newStock - currentStock;
        
        // Update stock
        await client.query(
          'UPDATE products SET stock_quantity = $1 WHERE id = $2',
          [newStock, productId]
        );
        
        // Log the change
        await client.query(`
          INSERT INTO inventory_transactions (
            product_id, transaction_type, quantity, reference_type,
            notes, created_at
          ) VALUES ($1, $2, $3, 'bulk_update', $4, CURRENT_TIMESTAMP)
        `, [
          productId,
          difference > 0 ? 'adjustment_increase' : 'adjustment_decrease',
          Math.abs(difference),
          reason || 'Bulk stock update'
        ]);
      }
      
      await client.query('COMMIT');
      
      console.log(`✅ Bulk updated ${updates.length} products`);
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Bulk stock update failed:', error);
      throw error;
    } finally {
      client.release();
    }
  }
}

// Export singleton instance
export const inventoryManager = new InventoryManager();

// Export class for testing
export default InventoryManager;

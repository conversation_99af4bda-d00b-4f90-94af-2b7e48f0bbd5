# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION FOR MIDASTECHNICAL.COM
# =============================================================================

# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://midastechnical.com
NEXTAUTH_URL=https://midastechnical.com
NEXTAUTH_SECRET=your-production-nextauth-secret-key

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/midastechnical_production

# =============================================================================
# PAYMENT PROCESSING
# =============================================================================

# Stripe (Primary Payment Processor)
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# PayPal (Secondary Payment Processor)
PAYPAL_CLIENT_ID=your-paypal-live-client-id
PAYPAL_CLIENT_SECRET=your-paypal-live-client-secret
PAYPAL_WEBHOOK_ID=your-paypal-webhook-id
PAYPAL_MODE=live

# Cryptocurrency Payments
CRYPTO_API_KEY=your-crypto-exchange-api-key
CRYPTO_WEBHOOK_SECRET=your-crypto-webhook-secret
COINGECKO_API_KEY=your-coingecko-api-key

# =============================================================================
# MARKETPLACE INTEGRATION
# =============================================================================

# 4Seller Marketplace
FOURSELLER_API_URL=https://api.4seller.com/v1
FOURSELLER_API_KEY=your-4seller-api-key
FOURSELLER_SELLER_ID=your-4seller-seller-id

# =============================================================================
# COMMUNICATION SERVICES
# =============================================================================

# Twilio SMS
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number
TWILIO_MESSAGING_SERVICE_SID=your-twilio-messaging-service-sid
TWILIO_WEBHOOK_URL=https://midastechnical.com/api/webhooks/twilio

# Telegram Bot
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_ADMIN_CHAT_ID=your-telegram-admin-chat-id
TELEGRAM_SUPPORT_CHAT_ID=your-telegram-support-chat-id
TELEGRAM_WEBHOOK_URL=https://midastechnical.com/api/webhooks/telegram

# Email (SMTP)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# =============================================================================
# AUTOMATION WORKFLOWS
# =============================================================================

# Zapier Integration
ZAPIER_NEW_ORDER_WEBHOOK=https://hooks.zapier.com/hooks/catch/your-new-order-webhook
ZAPIER_LOW_STOCK_WEBHOOK=https://hooks.zapier.com/hooks/catch/your-low-stock-webhook
ZAPIER_CUSTOMER_REGISTRATION_WEBHOOK=https://hooks.zapier.com/hooks/catch/your-customer-registration-webhook
ZAPIER_PRODUCT_UPDATE_WEBHOOK=https://hooks.zapier.com/hooks/catch/your-product-update-webhook
ZAPIER_PAYMENT_RECEIVED_WEBHOOK=https://hooks.zapier.com/hooks/catch/your-payment-received-webhook
ZAPIER_SUPPORT_TICKET_WEBHOOK=https://hooks.zapier.com/hooks/catch/your-support-ticket-webhook

# n8n Workflow Automation
N8N_BASE_URL=https://your-n8n-instance.com
N8N_API_KEY=your-n8n-api-key
N8N_WEBHOOK_BASE_URL=https://your-n8n-instance.com/webhook

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# DUO (2FA)
DUO_INTEGRATION_KEY=your-duo-integration-key
DUO_SECRET_KEY=your-duo-secret-key
DUO_APPLICATION_KEY=your-duo-application-key
DUO_API_HOSTNAME=api-hostname.duosecurity.com

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

# Sentry Error Tracking
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project

# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# =============================================================================
# CDN & STORAGE
# =============================================================================

# Cloudinary (Image CDN)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# AWS S3 (Backup Storage)
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=midastechnical-backups

# =============================================================================
# TESTING & DEVELOPMENT
# =============================================================================

# Test Phone Number for SMS Testing
TEST_PHONE_NUMBER=+1234567890

# Test Email for Email Testing
TEST_EMAIL=<EMAIL>

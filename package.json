{"name": "midastechnical-store", "version": "2.0.0", "description": "Professional e-commerce platform for electronic components and repair services", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup": "node scripts/production-setup.js", "seed": "node scripts/seed-real-products.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@heroicons/react": "^2.2.0", "@sendgrid/mail": "^8.1.5", "@sentry/nextjs": "^9.27.0", "@stripe/stripe-js": "^7.3.1", "@types/pg": "^8.15.4", "axios": "^1.9.0", "bcrypt": "^6.0.0", "dotenv": "^16.5.0", "formidable": "^3.5.4", "mailgun-js": "^0.22.0", "micro": "^10.0.1", "next": "^15.1.6", "next-auth": "^4.24.11", "nodemailer": "^7.0.3", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "redis": "^5.5.6", "stripe": "^18.2.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["e-commerce", "electronics", "repair-services", "nextjs", "stripe", "postgresql"], "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^4.1.8"}}
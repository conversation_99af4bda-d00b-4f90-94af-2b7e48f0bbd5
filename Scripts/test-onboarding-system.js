#!/usr/bin/env node

/**
 * Comprehensive Onboarding System Testing Script
 * Tests all onboarding flows, email sequences, and user journey components
 */

const axios = require('axios');
const { Pool } = require('pg');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class OnboardingSystemTester {
  constructor() {
    this.baseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://midastechnical.com'
      : 'http://localhost:3001';
    
    this.testResults = {
      registration: { passed: 0, failed: 0, tests: [] },
      onboarding: { passed: 0, failed: 0, tests: [] },
      emailSequence: { passed: 0, failed: 0, tests: [] },
      recommendations: { passed: 0, failed: 0, tests: [] },
      referrals: { passed: 0, failed: 0, tests: [] },
      overall: { passed: 0, failed: 0 }
    };

    this.testUsers = [];
  }

  /**
   * Run comprehensive onboarding system tests
   */
  async runTests() {
    console.log('🧪 COMPREHENSIVE ONBOARDING SYSTEM TESTING');
    console.log('='.repeat(60));
    console.log(`🌐 Testing URL: ${this.baseUrl}`);
    console.log(`⏰ Started: ${new Date().toISOString()}`);
    console.log('');

    try {
      // Test user registration flow
      await this.testUserRegistration();
      
      // Test onboarding flow
      await this.testOnboardingFlow();
      
      // Test email sequence processing
      await this.testEmailSequenceProcessing();
      
      // Test product recommendations
      await this.testProductRecommendations();
      
      // Test referral system
      await this.testReferralSystem();
      
      // Generate comprehensive report
      await this.generateTestReport();
      
      // Cleanup test data
      await this.cleanupTestData();
      
      console.log('✅ Onboarding system testing completed!');
      return this.testResults.overall.failed === 0;

    } catch (error) {
      console.error('❌ Testing failed:', error);
      return false;
    }
  }

  /**
   * Test user registration flow
   */
  async testUserRegistration() {
    console.log('👤 Testing User Registration Flow...');
    
    const testCases = [
      {
        name: 'Valid Registration',
        data: {
          firstName: 'Test',
          lastName: 'User',
          email: `test.user.${Date.now()}@example.com`,
          password: 'TestPassword123!',
          deviceInterests: ['iphone', 'samsung'],
          marketingConsent: true
        },
        shouldSucceed: true
      },
      {
        name: 'Invalid Email Format',
        data: {
          firstName: 'Test',
          lastName: 'User',
          email: 'invalid-email',
          password: 'TestPassword123!',
          deviceInterests: ['iphone'],
          marketingConsent: false
        },
        shouldSucceed: false
      },
      {
        name: 'Weak Password',
        data: {
          firstName: 'Test',
          lastName: 'User',
          email: `test.weak.${Date.now()}@example.com`,
          password: '123',
          deviceInterests: ['tablet'],
          marketingConsent: false
        },
        shouldSucceed: false
      }
    ];

    for (const testCase of testCases) {
      try {
        const response = await this.makeRequest('POST', '/api/auth/register', testCase.data);
        
        if (testCase.shouldSucceed) {
          if (response.status === 201 && response.data.success) {
            this.addTestResult('registration', testCase.name, 'passed', 'Registration successful');
            if (testCase.data.email.includes('test.user.')) {
              this.testUsers.push({
                email: testCase.data.email,
                userId: response.data.user.id,
                firstName: testCase.data.firstName
              });
            }
          } else {
            this.addTestResult('registration', testCase.name, 'failed', `Expected success, got ${response.status}`);
          }
        } else {
          if (response.status >= 400) {
            this.addTestResult('registration', testCase.name, 'passed', 'Correctly rejected invalid data');
          } else {
            this.addTestResult('registration', testCase.name, 'failed', 'Should have rejected invalid data');
          }
        }
      } catch (error) {
        if (testCase.shouldSucceed) {
          this.addTestResult('registration', testCase.name, 'failed', error.message);
        } else {
          this.addTestResult('registration', testCase.name, 'passed', 'Correctly rejected invalid data');
        }
      }
    }
  }

  /**
   * Test onboarding flow
   */
  async testOnboardingFlow() {
    console.log('🎯 Testing Onboarding Flow...');
    
    if (this.testUsers.length === 0) {
      this.addTestResult('onboarding', 'No Test Users', 'failed', 'No valid test users created');
      return;
    }

    const testUser = this.testUsers[0];

    try {
      // Test onboarding status API
      const statusResponse = await this.makeAuthenticatedRequest('GET', '/api/onboarding/status', null, testUser.userId);
      
      if (statusResponse.status === 200) {
        this.addTestResult('onboarding', 'Onboarding Status API', 'passed', 'Status retrieved successfully');
        
        const onboardingData = statusResponse.data.data;
        
        // Test step completion
        const stepResponse = await this.makeAuthenticatedRequest('POST', '/api/onboarding/complete-step', {
          stepType: 'email_verified'
        }, testUser.userId);
        
        if (stepResponse.status === 200) {
          this.addTestResult('onboarding', 'Step Completion', 'passed', 'Step completed successfully');
        } else {
          this.addTestResult('onboarding', 'Step Completion', 'failed', `Failed to complete step: ${stepResponse.status}`);
        }
        
        // Test progress tracking
        const updatedStatusResponse = await this.makeAuthenticatedRequest('GET', '/api/onboarding/status', null, testUser.userId);
        
        if (updatedStatusResponse.status === 200) {
          const updatedData = updatedStatusResponse.data.data;
          if (updatedData.completedSteps.includes('email_verified')) {
            this.addTestResult('onboarding', 'Progress Tracking', 'passed', 'Progress correctly tracked');
          } else {
            this.addTestResult('onboarding', 'Progress Tracking', 'failed', 'Progress not tracked');
          }
        }
        
      } else {
        this.addTestResult('onboarding', 'Onboarding Status API', 'failed', `API returned ${statusResponse.status}`);
      }
      
    } catch (error) {
      this.addTestResult('onboarding', 'Onboarding Flow', 'failed', error.message);
    }
  }

  /**
   * Test email sequence processing
   */
  async testEmailSequenceProcessing() {
    console.log('📧 Testing Email Sequence Processing...');
    
    try {
      const client = await pool.connect();
      
      // Check if onboarding emails are scheduled
      const scheduledEmailsResult = await client.query(`
        SELECT COUNT(*) as count
        FROM email_campaigns_queue
        WHERE status = 'scheduled'
        AND created_at >= CURRENT_TIMESTAMP - INTERVAL '5 minutes'
      `);
      
      const scheduledCount = parseInt(scheduledEmailsResult.rows[0].count);
      
      if (scheduledCount > 0) {
        this.addTestResult('emailSequence', 'Email Scheduling', 'passed', `${scheduledCount} emails scheduled`);
      } else {
        this.addTestResult('emailSequence', 'Email Scheduling', 'warning', 'No emails scheduled recently');
      }
      
      // Test email template generation
      const { customerOnboardingService } = require('../lib/customer-onboarding.js');
      
      if (this.testUsers.length > 0) {
        const testUser = this.testUsers[0];
        
        try {
          await customerOnboardingService.startOnboarding(
            testUser.userId,
            testUser.email,
            testUser.firstName
          );
          
          this.addTestResult('emailSequence', 'Onboarding Start', 'passed', 'Onboarding sequence started');
          
          // Check if emails were queued
          const queuedEmailsResult = await client.query(`
            SELECT COUNT(*) as count
            FROM email_campaigns_queue
            WHERE user_id = $1
          `, [testUser.userId]);
          
          const queuedCount = parseInt(queuedEmailsResult.rows[0].count);
          
          if (queuedCount >= 5) {
            this.addTestResult('emailSequence', 'Email Queue', 'passed', `${queuedCount} emails queued`);
          } else {
            this.addTestResult('emailSequence', 'Email Queue', 'failed', `Only ${queuedCount} emails queued, expected 5`);
          }
          
        } catch (error) {
          this.addTestResult('emailSequence', 'Onboarding Start', 'failed', error.message);
        }
      }
      
      client.release();
      
    } catch (error) {
      this.addTestResult('emailSequence', 'Email Processing', 'failed', error.message);
    }
  }

  /**
   * Test product recommendations
   */
  async testProductRecommendations() {
    console.log('🛍️ Testing Product Recommendations...');
    
    try {
      // Test different recommendation types
      const recommendationTypes = [
        'frequently_bought_together',
        'customers_also_viewed',
        'category_bestsellers',
        'personalized'
      ];
      
      // Get a sample product ID
      const client = await pool.connect();
      const productResult = await client.query(`
        SELECT id FROM products WHERE is_active = true LIMIT 1
      `);
      
      if (productResult.rows.length === 0) {
        this.addTestResult('recommendations', 'Product Availability', 'failed', 'No active products found');
        client.release();
        return;
      }
      
      const productId = productResult.rows[0].id;
      client.release();
      
      for (const type of recommendationTypes) {
        try {
          const response = await this.makeRequest('GET', `/api/sales/recommendations?productId=${productId}&type=${type}&limit=6`);
          
          if (response.status === 200 && response.data.success) {
            const recommendations = response.data.data.recommendations;
            this.addTestResult('recommendations', `${type} Recommendations`, 'passed', 
              `Retrieved ${recommendations.length} recommendations`);
          } else {
            this.addTestResult('recommendations', `${type} Recommendations`, 'failed', 
              `API returned ${response.status}`);
          }
        } catch (error) {
          this.addTestResult('recommendations', `${type} Recommendations`, 'failed', error.message);
        }
      }
      
    } catch (error) {
      this.addTestResult('recommendations', 'Recommendations System', 'failed', error.message);
    }
  }

  /**
   * Test referral system
   */
  async testReferralSystem() {
    console.log('👥 Testing Referral System...');
    
    if (this.testUsers.length === 0) {
      this.addTestResult('referrals', 'No Test Users', 'failed', 'No test users available');
      return;
    }

    const testUser = this.testUsers[0];

    try {
      // Test referral code generation
      const referralResponse = await this.makeAuthenticatedRequest('POST', '/api/referrals', {}, testUser.userId);
      
      if (referralResponse.status === 200 && referralResponse.data.success) {
        const referralCode = referralResponse.data.data.referralCode;
        this.addTestResult('referrals', 'Referral Code Generation', 'passed', `Generated code: ${referralCode}`);
        
        // Test referral stats
        const statsResponse = await this.makeAuthenticatedRequest('GET', '/api/referrals', null, testUser.userId);
        
        if (statsResponse.status === 200 && statsResponse.data.success) {
          this.addTestResult('referrals', 'Referral Stats', 'passed', 'Stats retrieved successfully');
        } else {
          this.addTestResult('referrals', 'Referral Stats', 'failed', `Stats API returned ${statsResponse.status}`);
        }
        
        // Test referral signup processing
        const { referralProgramService } = require('../lib/referral-program.js');
        
        // Create a second test user for referral testing
        const refereeEmail = `test.referee.${Date.now()}@example.com`;
        const refereeResponse = await this.makeRequest('POST', '/api/auth/register', {
          firstName: 'Referee',
          lastName: 'User',
          email: refereeEmail,
          password: 'TestPassword123!',
          deviceInterests: ['laptop'],
          marketingConsent: false
        });
        
        if (refereeResponse.status === 201) {
          const refereeUserId = refereeResponse.data.user.id;
          
          try {
            const referralResult = await referralProgramService.processReferralSignup(
              refereeUserId,
              referralCode
            );
            
            if (referralResult.success) {
              this.addTestResult('referrals', 'Referral Signup Processing', 'passed', 'Referral processed successfully');
            } else {
              this.addTestResult('referrals', 'Referral Signup Processing', 'failed', referralResult.message);
            }
          } catch (error) {
            this.addTestResult('referrals', 'Referral Signup Processing', 'failed', error.message);
          }
        }
        
      } else {
        this.addTestResult('referrals', 'Referral Code Generation', 'failed', `API returned ${referralResponse.status}`);
      }
      
    } catch (error) {
      this.addTestResult('referrals', 'Referral System', 'failed', error.message);
    }
  }

  /**
   * Make HTTP request
   */
  async makeRequest(method, path, data = null) {
    try {
      const config = {
        method,
        url: `${this.baseUrl}${path}`,
        timeout: 10000,
        validateStatus: () => true // Don't throw on any status code
      };

      if (data) {
        config.data = data;
        config.headers = { 'Content-Type': 'application/json' };
      }

      return await axios(config);
    } catch (error) {
      return { status: 0, error: error.message };
    }
  }

  /**
   * Make authenticated request (simplified for testing)
   */
  async makeAuthenticatedRequest(method, path, data = null, userId = null) {
    // For testing purposes, we'll simulate authentication
    // In a real implementation, you'd use proper JWT tokens
    const config = {
      method,
      url: `${this.baseUrl}${path}`,
      timeout: 10000,
      validateStatus: () => true,
      headers: {
        'Content-Type': 'application/json',
        'X-Test-User-Id': userId // Test header for authentication simulation
      }
    };

    if (data) {
      config.data = data;
    }

    try {
      return await axios(config);
    } catch (error) {
      return { status: 0, error: error.message };
    }
  }

  /**
   * Add test result
   */
  addTestResult(category, testName, status, message) {
    const result = {
      name: testName,
      status,
      message,
      timestamp: new Date().toISOString()
    };

    this.testResults[category].tests.push(result);

    if (status === 'passed') {
      this.testResults[category].passed++;
      this.testResults.overall.passed++;
    } else {
      this.testResults[category].failed++;
      this.testResults.overall.failed++;
    }

    const icon = status === 'passed' ? '✅' : status === 'warning' ? '⚠️' : '❌';
    console.log(`   ${icon} ${testName}: ${message}`);
  }

  /**
   * Generate comprehensive test report
   */
  async generateTestReport() {
    console.log('\n📋 ONBOARDING SYSTEM TEST REPORT');
    console.log('='.repeat(60));

    const totalTests = this.testResults.overall.passed + this.testResults.overall.failed;
    const successRate = totalTests > 0 ? (this.testResults.overall.passed / totalTests * 100).toFixed(1) : 0;

    console.log(`Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${this.testResults.overall.passed}`);
    console.log(`❌ Failed: ${this.testResults.overall.failed}`);
    console.log(`📊 Success Rate: ${successRate}%`);
    console.log('');

    // Category breakdown
    Object.entries(this.testResults).forEach(([category, results]) => {
      if (category === 'overall') return;
      
      const categoryTotal = results.passed + results.failed;
      const categoryRate = categoryTotal > 0 ? (results.passed / categoryTotal * 100).toFixed(1) : 0;
      
      console.log(`${category.toUpperCase()}: ${results.passed}/${categoryTotal} (${categoryRate}%)`);
      
      // Show failed tests
      results.tests.forEach(test => {
        if (test.status === 'failed') {
          console.log(`   ❌ ${test.name}: ${test.message}`);
        }
      });
    });

    // Save test report to database
    try {
      const client = await pool.connect();
      
      await client.query(`
        CREATE TABLE IF NOT EXISTS test_reports (
          id SERIAL PRIMARY KEY,
          test_type VARCHAR(100) NOT NULL,
          total_tests INTEGER NOT NULL,
          passed_tests INTEGER NOT NULL,
          failed_tests INTEGER NOT NULL,
          success_rate DECIMAL(5,2) NOT NULL,
          test_results JSONB NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await client.query(`
        INSERT INTO test_reports (
          test_type, total_tests, passed_tests, failed_tests, success_rate, test_results
        ) VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        'onboarding_system',
        totalTests,
        this.testResults.overall.passed,
        this.testResults.overall.failed,
        parseFloat(successRate),
        JSON.stringify(this.testResults)
      ]);

      client.release();
      console.log('\n✅ Test report saved to database');

    } catch (error) {
      console.error('\n❌ Failed to save test report:', error.message);
    }
  }

  /**
   * Cleanup test data
   */
  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');
    
    try {
      const client = await pool.connect();
      
      // Remove test users and related data
      for (const testUser of this.testUsers) {
        await client.query('DELETE FROM users WHERE email = $1', [testUser.email]);
      }
      
      // Remove test email campaigns
      await client.query(`
        DELETE FROM email_campaigns_queue 
        WHERE email LIKE '%@example.com'
        AND created_at >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
      `);
      
      client.release();
      console.log('✅ Test data cleanup completed');
      
    } catch (error) {
      console.error('❌ Failed to cleanup test data:', error.message);
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new OnboardingSystemTester();
  
  tester.runTests()
    .then((success) => {
      if (success) {
        console.log('\n🎉 All onboarding system tests passed!');
        process.exit(0);
      } else {
        console.log('\n💥 Some tests failed!');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Testing crashed:', error);
      process.exit(1);
    });
}

module.exports = OnboardingSystemTester;

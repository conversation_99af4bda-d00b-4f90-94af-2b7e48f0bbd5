#!/bin/bash

# =====================================================
# MIDAS TECHNICAL DATABASE BACKUP SETUP SCRIPT
# =====================================================
# This script sets up automated daily backups to AWS S3 with 30-day retention
# Run after: bash Scripts/import-database-schema.sh

set -e  # Exit on any error

# Configuration
BACKUP_BUCKET="midastechnical-backups"
AWS_REGION="us-east-1"
RETENTION_DAYS=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}💾 MIDAS TECHNICAL DATABASE BACKUP SETUP${NC}"
echo "============================================================"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Check if credentials file exists
if [ ! -f /root/database_credentials.txt ]; then
    print_error "Database credentials file not found. Run production-database-setup.sh first."
    exit 1
fi

# Extract database credentials
DB_NAME=$(grep "Database Name:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_USER=$(grep "Database User:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_PASSWORD=$(grep "Database Password:" /root/database_credentials.txt | cut -d: -f2 | xargs)

# Step 1: Install AWS CLI
echo -e "\n${BLUE}☁️  Step 1: Installing AWS CLI${NC}"
echo "------------------------------------------------------------"

if ! command -v aws &> /dev/null; then
    print_info "Installing AWS CLI..."
    apt update -y
    apt install -y awscli
    print_status "AWS CLI installed"
else
    print_status "AWS CLI already installed"
fi

# Step 2: Configure AWS Credentials
echo -e "\n${BLUE}🔑 Step 2: AWS Configuration${NC}"
echo "------------------------------------------------------------"

print_warning "AWS credentials need to be configured manually"
print_info "Run: aws configure"
print_info "You'll need:"
print_info "  - AWS Access Key ID"
print_info "  - AWS Secret Access Key"
print_info "  - Default region: ${AWS_REGION}"
print_info "  - Default output format: json"

# Check if AWS is configured
if aws sts get-caller-identity &> /dev/null; then
    print_status "AWS credentials are configured"
else
    print_warning "AWS credentials not configured. Please run 'aws configure' first."
    print_info "Continuing with backup script setup..."
fi

# Step 3: Create S3 Bucket
echo -e "\n${BLUE}🪣 Step 3: Setting up S3 Bucket${NC}"
echo "------------------------------------------------------------"

print_info "Creating S3 bucket: ${BACKUP_BUCKET}"

# Create bucket if it doesn't exist
if aws s3 ls "s3://${BACKUP_BUCKET}" 2>&1 | grep -q 'NoSuchBucket'; then
    if aws s3 mb "s3://${BACKUP_BUCKET}" --region ${AWS_REGION}; then
        print_status "S3 bucket created: ${BACKUP_BUCKET}"
    else
        print_error "Failed to create S3 bucket"
        exit 1
    fi
else
    print_status "S3 bucket already exists: ${BACKUP_BUCKET}"
fi

# Configure bucket lifecycle policy for 30-day retention
print_info "Configuring 30-day retention policy..."

cat > /tmp/lifecycle-policy.json << EOF
{
    "Rules": [
        {
            "ID": "MidasTechnicalBackupRetention",
            "Status": "Enabled",
            "Filter": {
                "Prefix": "database-backups/"
            },
            "Expiration": {
                "Days": ${RETENTION_DAYS}
            },
            "NoncurrentVersionExpiration": {
                "NoncurrentDays": 7
            }
        }
    ]
}
EOF

if aws s3api put-bucket-lifecycle-configuration --bucket ${BACKUP_BUCKET} --lifecycle-configuration file:///tmp/lifecycle-policy.json; then
    print_status "Lifecycle policy configured (${RETENTION_DAYS}-day retention)"
else
    print_warning "Failed to set lifecycle policy"
fi

rm -f /tmp/lifecycle-policy.json

# Step 4: Create Backup Script
echo -e "\n${BLUE}📝 Step 4: Creating Backup Script${NC}"
echo "------------------------------------------------------------"

print_info "Creating automated backup script..."

cat > /usr/local/bin/midas-db-backup.sh << 'EOF'
#!/bin/bash

# =====================================================
# MIDAS TECHNICAL AUTOMATED DATABASE BACKUP SCRIPT
# =====================================================

set -e

# Configuration
BACKUP_BUCKET="midastechnical-backups"
BACKUP_DIR="/var/backups/postgresql"
LOG_FILE="/var/log/midas-db-backup.log"
RETENTION_LOCAL_DAYS=7

# Create backup directory
mkdir -p ${BACKUP_DIR}

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a ${LOG_FILE}
}

# Function to send notification (optional - configure with your notification service)
send_notification() {
    local status=$1
    local message=$2
    # Add your notification logic here (email, Slack, etc.)
    log_message "NOTIFICATION: ${status} - ${message}"
}

log_message "Starting database backup process"

# Read database credentials
if [ ! -f /root/database_credentials.txt ]; then
    log_message "ERROR: Database credentials file not found"
    send_notification "ERROR" "Database backup failed - credentials not found"
    exit 1
fi

DB_NAME=$(grep "Database Name:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_USER=$(grep "Database User:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_PASSWORD=$(grep "Database Password:" /root/database_credentials.txt | cut -d: -f2 | xargs)

# Set environment variable for pg_dump
export PGPASSWORD="${DB_PASSWORD}"

# Generate backup filename with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/midas_technical_${TIMESTAMP}.sql"
COMPRESSED_FILE="${BACKUP_FILE}.gz"

log_message "Creating database backup: ${BACKUP_FILE}"

# Create database backup
if pg_dump -h localhost -U ${DB_USER} -d ${DB_NAME} --verbose --no-owner --no-privileges > ${BACKUP_FILE} 2>>${LOG_FILE}; then
    log_message "Database backup created successfully"
else
    log_message "ERROR: Database backup failed"
    send_notification "ERROR" "Database backup failed during pg_dump"
    exit 1
fi

# Compress backup
log_message "Compressing backup file..."
if gzip ${BACKUP_FILE}; then
    log_message "Backup compressed successfully"
else
    log_message "ERROR: Backup compression failed"
    send_notification "ERROR" "Backup compression failed"
    exit 1
fi

# Upload to S3
log_message "Uploading backup to S3..."
S3_KEY="database-backups/$(date +%Y/%m/%d)/midas_technical_${TIMESTAMP}.sql.gz"

if aws s3 cp ${COMPRESSED_FILE} "s3://${BACKUP_BUCKET}/${S3_KEY}"; then
    log_message "Backup uploaded to S3: s3://${BACKUP_BUCKET}/${S3_KEY}"
    send_notification "SUCCESS" "Database backup completed successfully"
else
    log_message "ERROR: S3 upload failed"
    send_notification "ERROR" "Database backup S3 upload failed"
    exit 1
fi

# Clean up local backups older than retention period
log_message "Cleaning up local backups older than ${RETENTION_LOCAL_DAYS} days..."
find ${BACKUP_DIR} -name "midas_technical_*.sql.gz" -mtime +${RETENTION_LOCAL_DAYS} -delete

# Get backup file size
BACKUP_SIZE=$(du -h ${COMPRESSED_FILE} | cut -f1)
log_message "Backup completed successfully - Size: ${BACKUP_SIZE}"

# Clean up current backup file (keep only in S3 and recent local backups)
rm -f ${COMPRESSED_FILE}

log_message "Database backup process completed"

# Unset password environment variable
unset PGPASSWORD
EOF

# Make backup script executable
chmod +x /usr/local/bin/midas-db-backup.sh

print_status "Backup script created: /usr/local/bin/midas-db-backup.sh"

# Step 5: Set up Cron Job
echo -e "\n${BLUE}⏰ Step 5: Setting up Daily Backup Schedule${NC}"
echo "------------------------------------------------------------"

print_info "Configuring daily backup at 2:00 AM..."

# Add cron job for daily backups at 2:00 AM
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/midas-db-backup.sh") | crontab -

print_status "Daily backup scheduled at 2:00 AM"

# Step 6: Create Backup Monitoring Script
echo -e "\n${BLUE}📊 Step 6: Creating Backup Monitoring${NC}"
echo "------------------------------------------------------------"

cat > /usr/local/bin/midas-backup-status.sh << 'EOF'
#!/bin/bash

# =====================================================
# MIDAS TECHNICAL BACKUP STATUS CHECKER
# =====================================================

BACKUP_BUCKET="midastechnical-backups"
LOG_FILE="/var/log/midas-db-backup.log"

echo "🔍 MIDAS TECHNICAL BACKUP STATUS"
echo "============================================================"

# Check last backup log entry
if [ -f ${LOG_FILE} ]; then
    echo "📋 Last Backup Log Entries:"
    tail -10 ${LOG_FILE}
    echo ""
fi

# Check S3 backups
echo "☁️  Recent S3 Backups:"
aws s3 ls "s3://${BACKUP_BUCKET}/database-backups/" --recursive --human-readable | tail -10

echo ""
echo "📊 Backup Statistics:"
TOTAL_BACKUPS=$(aws s3 ls "s3://${BACKUP_BUCKET}/database-backups/" --recursive | wc -l)
echo "Total backups in S3: ${TOTAL_BACKUPS}"

# Check backup size trend
echo ""
echo "💾 Recent Backup Sizes:"
aws s3 ls "s3://${BACKUP_BUCKET}/database-backups/" --recursive --human-readable | tail -5 | awk '{print $3, $4, $5}'
EOF

chmod +x /usr/local/bin/midas-backup-status.sh

print_status "Backup monitoring script created: /usr/local/bin/midas-backup-status.sh"

# Step 7: Test Backup
echo -e "\n${BLUE}🧪 Step 7: Testing Backup System${NC}"
echo "------------------------------------------------------------"

print_info "Running test backup..."

if /usr/local/bin/midas-db-backup.sh; then
    print_status "Test backup completed successfully"
else
    print_error "Test backup failed"
    exit 1
fi

echo -e "\n${GREEN}🎉 DATABASE BACKUP SETUP COMPLETED!${NC}"
echo "============================================================"
echo -e "${YELLOW}📋 BACKUP CONFIGURATION SUMMARY:${NC}"
echo "• S3 Bucket: ${BACKUP_BUCKET}"
echo "• Retention: ${RETENTION_DAYS} days"
echo "• Schedule: Daily at 2:00 AM"
echo "• Local retention: 7 days"
echo ""
echo -e "${BLUE}🛠️  MANAGEMENT COMMANDS:${NC}"
echo "• Manual backup: sudo /usr/local/bin/midas-db-backup.sh"
echo "• Check status: sudo /usr/local/bin/midas-backup-status.sh"
echo "• View logs: sudo tail -f /var/log/midas-db-backup.log"
echo ""
echo -e "${YELLOW}📋 NEXT STEPS:${NC}"
echo "1. Configure AWS credentials: aws configure"
echo "2. Set up monitoring: bash Scripts/setup-database-monitoring.sh"
echo "3. Test backup restoration procedure"

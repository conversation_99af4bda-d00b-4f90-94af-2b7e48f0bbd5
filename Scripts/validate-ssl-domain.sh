#!/bin/bash

# =====================================================
# MIDAS TECHNICAL SSL AND DOMAIN VALIDATION SCRIPT
# =====================================================
# This script performs comprehensive SSL and domain validation
# Run after domain and SSL setup is complete

set -e  # Exit on any error

# Configuration
DOMAIN="midastechnical.com"
PRODUCTION_URL="https://${DOMAIN}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 MIDAS TECHNICAL SSL & DOMAIN VALIDATION${NC}"
echo "============================================================"
echo "Domain: ${DOMAIN}"
echo "Production URL: ${PRODUCTION_URL}"
echo "Test Date: $(date)"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Initialize test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local success_message="$3"
    local failure_message="$4"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "Testing: $test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_status "$success_message"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_error "$failure_message"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Step 1: DNS Resolution Tests
echo -e "\n${BLUE}🌐 Step 1: DNS Resolution Tests${NC}"
echo "------------------------------------------------------------"

# Get server's public IP
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "unknown")
print_info "Server IP: ${SERVER_IP}"

# Check DNS resolution
RESOLVED_IP=$(dig +short ${DOMAIN} 2>/dev/null | tail -n1)
print_info "DNS resolves to: ${RESOLVED_IP}"

if [ "${SERVER_IP}" = "${RESOLVED_IP}" ]; then
    print_status "DNS resolution correct"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "DNS resolution mismatch (may be due to CDN/proxy)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test www subdomain
WWW_IP=$(dig +short www.${DOMAIN} 2>/dev/null | tail -n1)
if [ -n "$WWW_IP" ]; then
    print_status "www.${DOMAIN} resolves to: ${WWW_IP}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "www.${DOMAIN} does not resolve"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Step 2: SSL Certificate Tests
echo -e "\n${BLUE}🔒 Step 2: SSL Certificate Tests${NC}"
echo "------------------------------------------------------------"

# Test SSL certificate validity
run_test "SSL Certificate Validity" \
    "echo | openssl s_client -connect ${DOMAIN}:443 -servername ${DOMAIN} 2>/dev/null | openssl x509 -noout -dates" \
    "SSL certificate is valid and accessible" \
    "SSL certificate is invalid or inaccessible"

# Test SSL certificate expiration
CERT_EXPIRY=$(echo | openssl s_client -connect ${DOMAIN}:443 -servername ${DOMAIN} 2>/dev/null | openssl x509 -noout -enddate 2>/dev/null | cut -d= -f2)
if [ -n "$CERT_EXPIRY" ]; then
    EXPIRY_EPOCH=$(date -d "$CERT_EXPIRY" +%s 2>/dev/null || echo "0")
    CURRENT_EPOCH=$(date +%s)
    DAYS_UNTIL_EXPIRY=$(( (EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
    
    if [ "$DAYS_UNTIL_EXPIRY" -gt 30 ]; then
        print_status "SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    elif [ "$DAYS_UNTIL_EXPIRY" -gt 7 ]; then
        print_warning "SSL certificate expires in $DAYS_UNTIL_EXPIRY days (renew soon)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    else
        print_error "SSL certificate expires in $DAYS_UNTIL_EXPIRY days (urgent renewal needed)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
else
    print_error "Could not determine SSL certificate expiry"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test SSL protocols
run_test "TLS 1.2 Support" \
    "echo | openssl s_client -connect ${DOMAIN}:443 -tls1_2 2>/dev/null | grep -q 'Protocol.*TLSv1.2'" \
    "TLS 1.2 is supported" \
    "TLS 1.2 is not supported"

run_test "TLS 1.3 Support" \
    "echo | openssl s_client -connect ${DOMAIN}:443 -tls1_3 2>/dev/null | grep -q 'Protocol.*TLSv1.3'" \
    "TLS 1.3 is supported" \
    "TLS 1.3 is not supported"

# Step 3: HTTP/HTTPS Tests
echo -e "\n${BLUE}🔄 Step 3: HTTP/HTTPS Redirect Tests${NC}"
echo "------------------------------------------------------------"

# Test HTTP to HTTPS redirect
HTTP_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "http://${DOMAIN}/" 2>/dev/null || echo "000")
if [ "$HTTP_RESPONSE" = "301" ] || [ "$HTTP_RESPONSE" = "302" ]; then
    print_status "HTTP to HTTPS redirect working (${HTTP_RESPONSE})"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_error "HTTP to HTTPS redirect not working (got ${HTTP_RESPONSE})"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test HTTPS response
HTTPS_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "${PRODUCTION_URL}/" 2>/dev/null || echo "000")
if [ "$HTTPS_RESPONSE" = "200" ]; then
    print_status "HTTPS site responding correctly (200)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_error "HTTPS site not responding correctly (got ${HTTPS_RESPONSE})"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Step 4: Security Headers Tests
echo -e "\n${BLUE}🛡️  Step 4: Security Headers Tests${NC}"
echo "------------------------------------------------------------"

# Get headers
HEADERS=$(curl -s -I "${PRODUCTION_URL}/" 2>/dev/null || echo "")

# Test individual security headers
SECURITY_HEADERS=(
    "Strict-Transport-Security:HSTS header"
    "X-Frame-Options:X-Frame-Options header"
    "X-Content-Type-Options:X-Content-Type-Options header"
    "X-XSS-Protection:X-XSS-Protection header"
    "Referrer-Policy:Referrer-Policy header"
    "Content-Security-Policy:Content-Security-Policy header"
)

for header_test in "${SECURITY_HEADERS[@]}"; do
    IFS=':' read -r header_name description <<< "$header_test"
    
    if echo "$HEADERS" | grep -qi "$header_name"; then
        print_status "$description present"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_warning "$description missing"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
done

# Step 5: Application Endpoint Tests
echo -e "\n${BLUE}🌐 Step 5: Application Endpoint Tests${NC}"
echo "------------------------------------------------------------"

# Test key application endpoints
ENDPOINTS=(
    "/:Homepage"
    "/products:Products page"
    "/repair:Repair services page"
    "/admin:Admin dashboard"
    "/api/health:Health check API"
    "/api/products:Products API"
    "/api/categories:Categories API"
)

for endpoint_test in "${ENDPOINTS[@]}"; do
    IFS=':' read -r endpoint description <<< "$endpoint_test"
    
    STATUS_CODE=$(curl -s -o /dev/null -w "%{http_code}" "${PRODUCTION_URL}${endpoint}" 2>/dev/null || echo "000")
    
    if [ "$STATUS_CODE" = "200" ]; then
        print_status "$description responding (200)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    elif [ "$STATUS_CODE" = "401" ] || [ "$STATUS_CODE" = "403" ]; then
        print_status "$description protected (${STATUS_CODE})"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_error "$description not responding correctly (${STATUS_CODE})"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
done

# Step 6: Performance Tests
echo -e "\n${BLUE}⚡ Step 6: Performance Tests${NC}"
echo "------------------------------------------------------------"

# Test page load time
LOAD_TIME=$(curl -o /dev/null -s -w "%{time_total}" "${PRODUCTION_URL}/" 2>/dev/null || echo "999")
LOAD_TIME_MS=$(echo "$LOAD_TIME * 1000" | bc -l 2>/dev/null | cut -d. -f1)

if [ "$LOAD_TIME_MS" -lt 3000 ]; then
    print_status "Page load time: ${LOAD_TIME}s (< 3s target)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
elif [ "$LOAD_TIME_MS" -lt 5000 ]; then
    print_warning "Page load time: ${LOAD_TIME}s (acceptable but could be improved)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
else
    print_error "Page load time: ${LOAD_TIME}s (> 5s, needs optimization)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test compression
COMPRESSION=$(curl -s -H "Accept-Encoding: gzip" -I "${PRODUCTION_URL}/" 2>/dev/null | grep -i "content-encoding")
if echo "$COMPRESSION" | grep -qi "gzip"; then
    print_status "Gzip compression enabled"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Gzip compression not detected"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Step 7: SSL Labs Grade Simulation
echo -e "\n${BLUE}🏆 Step 7: SSL Configuration Quality${NC}"
echo "------------------------------------------------------------"

# Simulate basic SSL Labs checks
SSL_SCORE=0

# Check for TLS 1.3 support
if echo | openssl s_client -connect ${DOMAIN}:443 -tls1_3 2>/dev/null | grep -q "TLSv1.3"; then
    SSL_SCORE=$((SSL_SCORE + 25))
    print_info "TLS 1.3 support: +25 points"
fi

# Check for HSTS
if echo "$HEADERS" | grep -qi "Strict-Transport-Security"; then
    SSL_SCORE=$((SSL_SCORE + 20))
    print_info "HSTS header: +20 points"
fi

# Check for secure ciphers (simplified check)
if echo | openssl s_client -connect ${DOMAIN}:443 2>/dev/null | grep -q "ECDHE"; then
    SSL_SCORE=$((SSL_SCORE + 25))
    print_info "Forward secrecy: +25 points"
fi

# Check certificate validity
if [ "$DAYS_UNTIL_EXPIRY" -gt 30 ]; then
    SSL_SCORE=$((SSL_SCORE + 20))
    print_info "Certificate validity: +20 points"
fi

# Check for HTTP redirect
if [ "$HTTP_RESPONSE" = "301" ]; then
    SSL_SCORE=$((SSL_SCORE + 10))
    print_info "HTTP redirect: +10 points"
fi

# Determine grade
if [ "$SSL_SCORE" -ge 90 ]; then
    SSL_GRADE="A+"
    print_status "Estimated SSL Labs grade: ${SSL_GRADE} (${SSL_SCORE}/100)"
elif [ "$SSL_SCORE" -ge 80 ]; then
    SSL_GRADE="A"
    print_status "Estimated SSL Labs grade: ${SSL_GRADE} (${SSL_SCORE}/100)"
elif [ "$SSL_SCORE" -ge 70 ]; then
    SSL_GRADE="B"
    print_warning "Estimated SSL Labs grade: ${SSL_GRADE} (${SSL_SCORE}/100)"
else
    SSL_GRADE="C or below"
    print_error "Estimated SSL Labs grade: ${SSL_GRADE} (${SSL_SCORE}/100)"
fi

# Final Report
echo -e "\n${BLUE}📊 VALIDATION SUMMARY${NC}"
echo "============================================================"

PASS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))

echo -e "Domain: ${DOMAIN}"
echo -e "Test Date: $(date)"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"
echo -e "Pass Rate: $PASS_RATE%"
echo -e "Estimated SSL Grade: $SSL_GRADE"

echo -e "\n${BLUE}🔗 EXTERNAL VALIDATION TOOLS:${NC}"
echo "------------------------------------------------------------"
echo "• SSL Labs: https://www.ssllabs.com/ssltest/analyze.html?d=${DOMAIN}"
echo "• Security Headers: https://securityheaders.com/?q=${PRODUCTION_URL}"
echo "• PageSpeed Insights: https://pagespeed.web.dev/report?url=${PRODUCTION_URL}"
echo "• GTmetrix: https://gtmetrix.com/?url=${PRODUCTION_URL}"

if [ "$FAILED_TESTS" -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL TESTS PASSED! DOMAIN IS PRODUCTION READY!${NC}"
    exit 0
elif [ "$PASS_RATE" -ge 85 ]; then
    echo -e "\n${YELLOW}⚠️  MOSTLY READY - Minor issues detected${NC}"
    echo -e "Review failed tests and consider improvements"
    exit 1
else
    echo -e "\n${RED}❌ CRITICAL ISSUES DETECTED${NC}"
    echo -e "Address failed tests before production deployment"
    exit 2
fi

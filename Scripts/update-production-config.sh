#!/bin/bash

# =====================================================
# MIDAS TECHNICAL PRODUCTION CONFIGURATION UPDATE SCRIPT
# =====================================================
# This script updates all configuration files for production domain
# Run after: bash Scripts/setup-production-domain.sh

set -e  # Exit on any error

# Configuration
DOMAIN="midastechnical.com"
PRODUCTION_URL="https://${DOMAIN}"
APP_DIR="/var/www/midastechnical"  # Adjust if different

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}⚙️  MIDAS TECHNICAL PRODUCTION CONFIG UPDATE${NC}"
echo "============================================================"
echo "Domain: ${DOMAIN}"
echo "Production URL: ${PRODUCTION_URL}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to backup file
backup_file() {
    local file="$1"
    if [ -f "$file" ]; then
        cp "$file" "$file.backup.$(date +%Y%m%d_%H%M%S)"
        print_info "Backed up: $file"
    fi
}

# Step 1: Update Environment Variables
echo -e "\n${BLUE}🔧 Step 1: Updating Environment Variables${NC}"
echo "------------------------------------------------------------"

ENV_FILE=".env.local"

if [ ! -f "$ENV_FILE" ]; then
    print_error "Environment file not found: $ENV_FILE"
    exit 1
fi

backup_file "$ENV_FILE"

print_info "Updating environment variables..."

# Update NEXTAUTH_URL
sed -i "s|NEXTAUTH_URL=.*|NEXTAUTH_URL=${PRODUCTION_URL}|g" "$ENV_FILE"
print_status "Updated NEXTAUTH_URL"

# Update NEXT_PUBLIC_SITE_URL
sed -i "s|NEXT_PUBLIC_SITE_URL=.*|NEXT_PUBLIC_SITE_URL=${PRODUCTION_URL}|g" "$ENV_FILE"
print_status "Updated NEXT_PUBLIC_SITE_URL"

# Update NODE_ENV to production
sed -i "s|NODE_ENV=.*|NODE_ENV=production|g" "$ENV_FILE"
print_status "Updated NODE_ENV to production"

# Generate new NEXTAUTH_SECRET if it's still the default
if grep -q "midas-technical-production-secret-key-32-chars-minimum-2024" "$ENV_FILE"; then
    NEW_SECRET=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    sed -i "s|NEXTAUTH_SECRET=.*|NEXTAUTH_SECRET=${NEW_SECRET}|g" "$ENV_FILE"
    print_status "Generated new NEXTAUTH_SECRET"
fi

# Update email configuration for production domain
sed -i "s|SMTP_FROM=.*|SMTP_FROM=noreply@${DOMAIN}|g" "$ENV_FILE"
sed -i "s|REPLY_TO_EMAIL=.*|REPLY_TO_EMAIL=support@${DOMAIN}|g" "$ENV_FILE"
sed -i "s|ADMIN_EMAIL=.*|ADMIN_EMAIL=admin@${DOMAIN}|g" "$ENV_FILE"
print_status "Updated email configuration"

# Update business information
sed -i "s|BUSINESS_EMAIL=.*|BUSINESS_EMAIL=info@${DOMAIN}|g" "$ENV_FILE"
print_status "Updated business email"

# Step 2: Update Next.js Configuration
echo -e "\n${BLUE}⚙️  Step 2: Updating Next.js Configuration${NC}"
echo "------------------------------------------------------------"

NEXTJS_CONFIG="next.config.js"

if [ ! -f "$NEXTJS_CONFIG" ]; then
    print_error "Next.js config file not found: $NEXTJS_CONFIG"
    exit 1
fi

backup_file "$NEXTJS_CONFIG"

print_info "Updating Next.js configuration..."

# Create updated next.config.js with production domain
cat > "$NEXTJS_CONFIG" << EOF
/** @type {import('next').NextConfig} */

// Import Sentry configuration if available
let withSentryConfig;
try {
  withSentryConfig = require('@sentry/nextjs').withSentryConfig;
} catch (e) {
  withSentryConfig = (config) => config;
}

const nextConfig = {
  // Basic configuration
  reactStrictMode: true,

  // Production environment variables
  env: {
    CUSTOM_KEY: 'production',
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || '${PRODUCTION_URL}',
  },

  // Image optimization for production
  images: {
    domains: [
      '${DOMAIN}',
      'cdn.${DOMAIN}',
      'res.cloudinary.com',
      'images.unsplash.com',
      'via.placeholder.com'
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  
  // Webpack configuration for better performance
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Important: return the modified config
    return config;
  },
  
  // Production security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload'
          },
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com https://js.stripe.com",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
              "font-src 'self' https://fonts.gstatic.com",
              "img-src 'self' data: https: blob:",
              "connect-src 'self' https://api.stripe.com https://www.google-analytics.com https://vitals.vercel-insights.com",
              "frame-src 'self' https://js.stripe.com https://hooks.stripe.com",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests"
            ].join('; ')
          }
        ]
      }
    ];
  },
  
  // Production redirects for SEO
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/shop',
        destination: '/products',
        permanent: true,
      },
      {
        source: '/store',
        destination: '/products',
        permanent: true,
      },
      {
        source: '/repair-services',
        destination: '/repair',
        permanent: true,
      },
      {
        source: '/order-tracking',
        destination: '/track',
        permanent: true,
      }
    ];
  },

  // API rewrites
  async rewrites() {
    return [
      {
        source: '/api/health',
        destination: '/api/system/health'
      },
      {
        source: '/sitemap.xml',
        destination: '/api/sitemap'
      },
      {
        source: '/robots.txt',
        destination: '/api/robots'
      }
    ];
  },

  // Production optimizations
  experimental: {
    esmExternals: true,
    optimizeCss: true,
  },

  // Server external packages (moved from experimental)
  serverExternalPackages: ['pg', 'bcrypt'],

  // Compiler options
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
  },

  // Production settings
  output: 'standalone',
  poweredByHeader: false,
  compress: true,
  generateEtags: true,
  productionBrowserSourceMaps: false,
  trailingSlash: false,
};

// Sentry configuration for production monitoring
const sentryWebpackPluginOptions = {
  silent: true,
  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,
  widenClientFileUpload: true,
  transpileClientSDK: true,
  tunnelRoute: "/monitoring/tunnel",
  hideSourceMaps: true,
  disableLogger: true,
};

// Export with Sentry if DSN is configured
module.exports = process.env.SENTRY_DSN
  ? withSentryConfig(nextConfig, sentryWebpackPluginOptions)
  : nextConfig;
EOF

print_status "Updated Next.js configuration"

# Step 3: Update Package.json Scripts
echo -e "\n${BLUE}📦 Step 3: Updating Package.json Scripts${NC}"
echo "------------------------------------------------------------"

PACKAGE_JSON="package.json"

if [ ! -f "$PACKAGE_JSON" ]; then
    print_error "Package.json not found: $PACKAGE_JSON"
    exit 1
fi

backup_file "$PACKAGE_JSON"

# Update package.json to include production scripts
python3 << EOF
import json

with open('$PACKAGE_JSON', 'r') as f:
    data = json.load(f)

# Update scripts for production
data['scripts']['start:prod'] = 'NODE_ENV=production next start'
data['scripts']['build:prod'] = 'NODE_ENV=production next build'
data['scripts']['deploy'] = 'npm run build:prod && npm run start:prod'

with open('$PACKAGE_JSON', 'w') as f:
    json.dump(data, f, indent=2)
EOF

print_status "Updated package.json scripts"

# Step 4: Create Production Systemd Service
echo -e "\n${BLUE}🔧 Step 4: Creating Production Systemd Service${NC}"
echo "------------------------------------------------------------"

print_info "Creating systemd service for production deployment..."

cat > /etc/systemd/system/midastechnical.service << EOF
[Unit]
Description=Midas Technical E-commerce Platform
After=network.target
Wants=postgresql.service

[Service]
Type=simple
User=www-data
WorkingDirectory=$(pwd)
Environment=NODE_ENV=production
Environment=PATH=/usr/bin:/usr/local/bin
Environment=PWD=$(pwd)
ExecStart=/usr/bin/npm run start:prod
Restart=on-failure
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=midastechnical

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable service
systemctl daemon-reload
systemctl enable midastechnical.service

print_status "Created and enabled systemd service"

# Step 5: Update API Configuration Files
echo -e "\n${BLUE}🔌 Step 5: Updating API Configuration${NC}"
echo "------------------------------------------------------------"

# Update any hardcoded localhost references in API files
find pages/api -name "*.js" -type f -exec grep -l "localhost:3000" {} \; | while read file; do
    backup_file "$file"
    sed -i "s|localhost:3000|${DOMAIN}|g" "$file"
    sed -i "s|http://${DOMAIN}|${PRODUCTION_URL}|g" "$file"
    print_info "Updated API file: $file"
done

print_status "Updated API configuration files"

# Step 6: Update Component Files
echo -e "\n${BLUE}🎨 Step 6: Updating Component Files${NC}"
echo "------------------------------------------------------------"

# Update any hardcoded localhost references in components
find components -name "*.js" -type f -exec grep -l "localhost:3000" {} \; | while read file; do
    backup_file "$file"
    sed -i "s|localhost:3000|${DOMAIN}|g" "$file"
    sed -i "s|http://${DOMAIN}|${PRODUCTION_URL}|g" "$file"
    print_info "Updated component file: $file"
done

print_status "Updated component files"

# Step 7: Create Production Validation Script
echo -e "\n${BLUE}🧪 Step 7: Creating Production Validation Script${NC}"
echo "------------------------------------------------------------"

cat > Scripts/validate-production-domain.sh << 'EOF'
#!/bin/bash

# =====================================================
# MIDAS TECHNICAL PRODUCTION DOMAIN VALIDATION SCRIPT
# =====================================================

DOMAIN="midastechnical.com"
PRODUCTION_URL="https://${DOMAIN}"

echo "🔍 VALIDATING PRODUCTION DOMAIN CONFIGURATION"
echo "============================================================"

# Test SSL certificate
echo "Testing SSL certificate..."
if curl -s -I "${PRODUCTION_URL}" | grep -q "HTTP/2 200"; then
    echo "✅ SSL certificate working"
else
    echo "❌ SSL certificate issue"
fi

# Test HTTP to HTTPS redirect
echo "Testing HTTP to HTTPS redirect..."
if curl -s -I "http://${DOMAIN}" | grep -q "301"; then
    echo "✅ HTTP to HTTPS redirect working"
else
    echo "❌ HTTP to HTTPS redirect not working"
fi

# Test security headers
echo "Testing security headers..."
HEADERS=$(curl -s -I "${PRODUCTION_URL}")
if echo "$HEADERS" | grep -q "Strict-Transport-Security"; then
    echo "✅ HSTS header present"
else
    echo "❌ HSTS header missing"
fi

# Test application endpoints
echo "Testing application endpoints..."
for endpoint in "/" "/api/health" "/products" "/repair"; do
    if curl -s -o /dev/null -w "%{http_code}" "${PRODUCTION_URL}${endpoint}" | grep -q "200"; then
        echo "✅ ${endpoint} responding"
    else
        echo "❌ ${endpoint} not responding"
    fi
done

echo "Validation complete!"
EOF

chmod +x Scripts/validate-production-domain.sh

print_status "Created production validation script"

echo -e "\n${GREEN}🎉 PRODUCTION CONFIGURATION UPDATE COMPLETED!${NC}"
echo "============================================================"
echo -e "${YELLOW}📋 CONFIGURATION SUMMARY:${NC}"
echo "• Environment variables updated for ${PRODUCTION_URL}"
echo "• Next.js configuration updated with production domain"
echo "• Security headers configured"
echo "• Systemd service created and enabled"
echo "• API and component files updated"
echo ""
echo -e "${BLUE}🔗 PRODUCTION URLS:${NC}"
echo "• Website: ${PRODUCTION_URL}"
echo "• Admin Panel: ${PRODUCTION_URL}/admin"
echo "• Repair Services: ${PRODUCTION_URL}/repair"
echo "• API Health: ${PRODUCTION_URL}/api/health"
echo ""
echo -e "${YELLOW}📋 NEXT STEPS:${NC}"
echo "1. Build the application: npm run build:prod"
echo "2. Start the production service: sudo systemctl start midastechnical"
echo "3. Validate domain configuration: bash Scripts/validate-production-domain.sh"
echo "4. Update Stripe webhook URLs to use ${PRODUCTION_URL}/api/webhooks/stripe"
echo "5. Test SSL rating at: https://www.ssllabs.com/ssltest/analyze.html?d=${DOMAIN}"
EOF

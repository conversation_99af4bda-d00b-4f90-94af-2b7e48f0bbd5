#!/bin/bash

# =====================================================
# MIDAS TECHNICAL PRODUCTION DOMAIN & SSL SETUP SCRIPT
# =====================================================
# This script configures the production domain with SSL certificates
# Run on production server with domain pointing to server IP

set -e  # Exit on any error

# Configuration
DOMAIN="midastechnical.com"
EMAIL="<EMAIL>"  # For Let's Encrypt notifications
APP_PORT="3000"
WEB_ROOT="/var/www/html"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🌐 MIDAS TECHNICAL PRODUCTION DOMAIN SETUP${NC}"
echo "============================================================"
echo "Domain: ${DOMAIN}"
echo "Email: ${EMAIL}"
echo "App Port: ${APP_PORT}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Step 1: Verify DNS Configuration
echo -e "\n${BLUE}🔍 Step 1: Verifying DNS Configuration${NC}"
echo "------------------------------------------------------------"

print_info "Checking DNS resolution for ${DOMAIN}..."

# Get server's public IP
SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || curl -s icanhazip.com)
print_info "Server IP: ${SERVER_IP}"

# Check DNS resolution
RESOLVED_IP=$(dig +short ${DOMAIN} | tail -n1)
print_info "DNS resolves to: ${RESOLVED_IP}"

if [ "${SERVER_IP}" = "${RESOLVED_IP}" ]; then
    print_status "DNS configuration correct"
else
    print_warning "DNS mismatch! Please update DNS records to point ${DOMAIN} to ${SERVER_IP}"
    print_info "Continuing with setup (DNS propagation may take time)..."
fi

# Step 2: Install and Configure Nginx
echo -e "\n${BLUE}🌐 Step 2: Installing and Configuring Nginx${NC}"
echo "------------------------------------------------------------"

print_info "Installing Nginx..."
apt update -y
apt install -y nginx

# Stop nginx temporarily for certificate generation
systemctl stop nginx

# Create Nginx configuration for the domain
print_info "Creating Nginx configuration..."

cat > /etc/nginx/sites-available/${DOMAIN} << EOF
# =====================================================
# MIDAS TECHNICAL PRODUCTION NGINX CONFIGURATION
# =====================================================

# HTTP server (redirects to HTTPS)
server {
    listen 80;
    listen [::]:80;
    server_name ${DOMAIN} www.${DOMAIN};
    
    # Let's Encrypt challenge location
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    # Redirect all HTTP traffic to HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name ${DOMAIN} www.${DOMAIN};
    
    # SSL Configuration (will be updated after certificate generation)
    ssl_certificate /etc/letsencrypt/live/${DOMAIN}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/${DOMAIN}/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com https://js.stripe.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.stripe.com https://www.google-analytics.com; frame-src 'self' https://js.stripe.com https://hooks.stripe.com; object-src 'none'; base-uri 'self'; form-action 'self';" always;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Rate Limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=login:10m rate=1r/s;
    
    # Main application proxy
    location / {
        proxy_pass http://127.0.0.1:${APP_PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
        
        # Rate limiting for general requests
        limit_req zone=api burst=20 nodelay;
    }
    
    # API endpoints with specific rate limiting
    location /api/auth/ {
        proxy_pass http://127.0.0.1:${APP_PORT};
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Strict rate limiting for authentication
        limit_req zone=login burst=5 nodelay;
    }
    
    # Webhook endpoints
    location /api/webhooks/ {
        proxy_pass http://127.0.0.1:${APP_PORT};
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Allow larger payloads for webhooks
        client_max_body_size 10M;
    }
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:${APP_PORT};
        proxy_set_header Host \$host;
        proxy_set_header X-Forwarded-Proto \$scheme;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:${APP_PORT};
        proxy_set_header Host \$host;
        access_log off;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(\.env|\.git|node_modules|package\.json) {
        deny all;
        access_log off;
        log_not_found off;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/${DOMAIN} /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
if nginx -t; then
    print_status "Nginx configuration valid"
else
    print_error "Nginx configuration invalid"
    exit 1
fi

# Step 3: Install Certbot and Generate SSL Certificates
echo -e "\n${BLUE}🔒 Step 3: Installing Certbot and Generating SSL Certificates${NC}"
echo "------------------------------------------------------------"

print_info "Installing Certbot..."
apt install -y certbot python3-certbot-nginx

# Create web root directory
mkdir -p ${WEB_ROOT}
chown -R www-data:www-data ${WEB_ROOT}

# Start nginx for certificate generation
systemctl start nginx

print_info "Generating SSL certificate for ${DOMAIN}..."

# Generate certificate
if certbot certonly \
    --webroot \
    --webroot-path=${WEB_ROOT} \
    --email ${EMAIL} \
    --agree-tos \
    --no-eff-email \
    --domains ${DOMAIN},www.${DOMAIN}; then
    print_status "SSL certificate generated successfully"
else
    print_error "SSL certificate generation failed"
    exit 1
fi

# Step 4: Configure Auto-Renewal
echo -e "\n${BLUE}🔄 Step 4: Configuring SSL Auto-Renewal${NC}"
echo "------------------------------------------------------------"

print_info "Setting up automatic certificate renewal..."

# Create renewal hook script
cat > /etc/letsencrypt/renewal-hooks/deploy/nginx-reload.sh << 'EOF'
#!/bin/bash
systemctl reload nginx
EOF

chmod +x /etc/letsencrypt/renewal-hooks/deploy/nginx-reload.sh

# Test auto-renewal
if certbot renew --dry-run; then
    print_status "Auto-renewal test successful"
else
    print_warning "Auto-renewal test failed - manual intervention may be required"
fi

# Add cron job for renewal (certbot usually adds this automatically)
if ! crontab -l | grep -q "certbot renew"; then
    (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
    print_status "Auto-renewal cron job added"
fi

# Step 5: Update Nginx Configuration with SSL
echo -e "\n${BLUE}🔧 Step 5: Updating Nginx Configuration with SSL${NC}"
echo "------------------------------------------------------------"

# Reload nginx with SSL configuration
systemctl reload nginx

if systemctl is-active --quiet nginx; then
    print_status "Nginx reloaded with SSL configuration"
else
    print_error "Nginx failed to reload"
    systemctl status nginx
    exit 1
fi

# Step 6: Configure Firewall
echo -e "\n${BLUE}🔥 Step 6: Configuring Firewall${NC}"
echo "------------------------------------------------------------"

print_info "Configuring UFW firewall..."

# Install and configure UFW
apt install -y ufw

# Configure firewall rules
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 'Nginx Full'
ufw allow 5432  # PostgreSQL (if needed for external connections)

# Enable firewall
ufw --force enable

print_status "Firewall configured and enabled"

# Step 7: Test SSL Configuration
echo -e "\n${BLUE}🧪 Step 7: Testing SSL Configuration${NC}"
echo "------------------------------------------------------------"

print_info "Testing SSL certificate..."

# Test SSL certificate
if openssl s_client -connect ${DOMAIN}:443 -servername ${DOMAIN} </dev/null 2>/dev/null | openssl x509 -noout -dates; then
    print_status "SSL certificate is valid"
else
    print_warning "SSL certificate test inconclusive"
fi

# Test HTTP to HTTPS redirect
print_info "Testing HTTP to HTTPS redirect..."
HTTP_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://${DOMAIN}/ || echo "000")
if [ "${HTTP_RESPONSE}" = "301" ]; then
    print_status "HTTP to HTTPS redirect working"
else
    print_warning "HTTP to HTTPS redirect may not be working (got ${HTTP_RESPONSE})"
fi

echo -e "\n${GREEN}🎉 DOMAIN AND SSL SETUP COMPLETED!${NC}"
echo "============================================================"
echo -e "${YELLOW}📋 SETUP SUMMARY:${NC}"
echo "• Domain: ${DOMAIN}"
echo "• SSL Certificate: Let's Encrypt"
echo "• Auto-renewal: Configured"
echo "• HTTP to HTTPS: Enabled"
echo "• Security headers: Configured"
echo "• Firewall: Enabled"
echo ""
echo -e "${BLUE}🔗 URLS:${NC}"
echo "• Website: https://${DOMAIN}"
echo "• Admin: https://${DOMAIN}/admin"
echo "• Repair Services: https://${DOMAIN}/repair"
echo "• API Health: https://${DOMAIN}/health"
echo ""
echo -e "${YELLOW}📋 NEXT STEPS:${NC}"
echo "1. Update application environment variables: bash Scripts/update-production-config.sh"
echo "2. Test SSL rating: https://www.ssllabs.com/ssltest/analyze.html?d=${DOMAIN}"
echo "3. Update Stripe webhook URLs to use https://${DOMAIN}"
echo "4. Restart the application with production configuration"

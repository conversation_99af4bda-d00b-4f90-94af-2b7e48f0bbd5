#!/bin/bash

# =====================================================
# MIDAS TECHNICAL DATABASE SCHEMA IMPORT SCRIPT
# =====================================================
# This script imports the complete database schema and seeds initial data
# Run after: bash Scripts/production-database-setup.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📊 MIDAS TECHNICAL DATABASE SCHEMA IMPORT${NC}"
echo "============================================================"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if credentials file exists
if [ ! -f /root/database_credentials.txt ]; then
    print_error "Database credentials file not found. Run production-database-setup.sh first."
    exit 1
fi

# Extract database credentials
DB_NAME=$(grep "Database Name:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_USER=$(grep "Database User:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_PASSWORD=$(grep "Database Password:" /root/database_credentials.txt | cut -d: -f2 | xargs)

print_info "Using database: ${DB_NAME}"
print_info "Using user: ${DB_USER}"

# Set environment variable for psql
export PGPASSWORD="${DB_PASSWORD}"

# Step 1: Test Database Connection
echo -e "\n${BLUE}🔌 Step 1: Testing Database Connection${NC}"
echo "------------------------------------------------------------"

if psql -h localhost -U ${DB_USER} -d ${DB_NAME} -c "SELECT version();" > /dev/null 2>&1; then
    print_status "Database connection successful"
else
    print_error "Cannot connect to database. Check credentials and PostgreSQL service."
    exit 1
fi

# Step 2: Import Database Schema
echo -e "\n${BLUE}📋 Step 2: Importing Database Schema${NC}"
echo "------------------------------------------------------------"

if [ ! -f "database/repair_system_schema.sql" ]; then
    print_error "Schema file not found: database/repair_system_schema.sql"
    exit 1
fi

print_info "Importing complete database schema..."
if psql -h localhost -U ${DB_USER} -d ${DB_NAME} -f database/repair_system_schema.sql > /dev/null 2>&1; then
    print_status "Database schema imported successfully"
else
    print_error "Failed to import database schema"
    exit 1
fi

# Step 3: Verify Schema Import
echo -e "\n${BLUE}🔍 Step 3: Verifying Schema Import${NC}"
echo "------------------------------------------------------------"

# Check if all required tables exist
REQUIRED_TABLES=(
    "users"
    "products" 
    "categories"
    "orders"
    "order_items"
    "cart_items"
    "repair_tickets"
    "repair_services"
    "repair_service_categories"
    "device_types"
    "repair_technicians"
    "repair_parts"
    "inventory_transactions"
    "stock_alerts"
    "webhook_logs"
    "payment_sessions"
    "shipping_labels"
)

print_info "Checking required tables..."
for table in "${REQUIRED_TABLES[@]}"; do
    if psql -h localhost -U ${DB_USER} -d ${DB_NAME} -c "\dt ${table}" | grep -q "${table}"; then
        print_status "Table exists: ${table}"
    else
        print_error "Missing table: ${table}"
        exit 1
    fi
done

# Step 4: Import Initial Data
echo -e "\n${BLUE}🌱 Step 4: Importing Initial Data${NC}"
echo "------------------------------------------------------------"

print_info "Running production setup to seed initial data..."
if node Scripts/production-setup.js > /dev/null 2>&1; then
    print_status "Initial data seeded successfully"
else
    print_warning "Some issues with data seeding (may be due to existing data)"
fi

# Step 5: Verify Data Import
echo -e "\n${BLUE}📊 Step 5: Verifying Data Import${NC}"
echo "------------------------------------------------------------"

# Check product count
PRODUCT_COUNT=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT COUNT(*) FROM products WHERE is_active = true;" | xargs)
print_info "Active products: ${PRODUCT_COUNT}"

if [ "${PRODUCT_COUNT}" -ge 500 ]; then
    print_status "Product count meets requirement (${PRODUCT_COUNT} >= 500)"
else
    print_warning "Product count below target (${PRODUCT_COUNT} < 500)"
fi

# Check category count
CATEGORY_COUNT=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT COUNT(*) FROM categories WHERE is_active = true;" | xargs)
print_info "Active categories: ${CATEGORY_COUNT}"

if [ "${CATEGORY_COUNT}" -ge 20 ]; then
    print_status "Category count meets requirement (${CATEGORY_COUNT} >= 20)"
else
    print_warning "Category count below target (${CATEGORY_COUNT} < 20)"
fi

# Check repair service categories
REPAIR_CATEGORIES=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT COUNT(*) FROM repair_service_categories;" | xargs)
print_info "Repair service categories: ${REPAIR_CATEGORIES}"

if [ "${REPAIR_CATEGORIES}" -ge 10 ]; then
    print_status "Repair categories configured (${REPAIR_CATEGORIES} categories)"
else
    print_warning "Few repair categories (${REPAIR_CATEGORIES} < 10)"
fi

# Check device types
DEVICE_TYPES=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT COUNT(*) FROM device_types;" | xargs)
print_info "Device types: ${DEVICE_TYPES}"

if [ "${DEVICE_TYPES}" -ge 30 ]; then
    print_status "Device types configured (${DEVICE_TYPES} devices)"
else
    print_warning "Few device types (${DEVICE_TYPES} < 30)"
fi

# Calculate inventory value
INVENTORY_VALUE=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT COALESCE(SUM(stock_quantity * price), 0) FROM products WHERE is_active = true;" | xargs)
print_info "Total inventory value: \$${INVENTORY_VALUE}"

# Step 6: Create Database Indexes for Performance
echo -e "\n${BLUE}⚡ Step 6: Optimizing Database Performance${NC}"
echo "------------------------------------------------------------"

print_info "Creating additional performance indexes..."

psql -h localhost -U ${DB_USER} -d ${DB_NAME} << EOF
-- Additional performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_category_active ON products(category_id, is_active);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_price_range ON products(price) WHERE is_active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_user_status ON orders(user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_created_date ON orders(created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_repair_tickets_user_status ON repair_tickets(user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inventory_product_type ON inventory_transactions(product_id, transaction_type);

-- Update table statistics
ANALYZE;
EOF

print_status "Performance indexes created and statistics updated"

# Step 7: Set up Connection Pooling
echo -e "\n${BLUE}🏊 Step 7: Configuring Connection Pooling${NC}"
echo "------------------------------------------------------------"

print_info "Installing and configuring PgBouncer..."

# Install PgBouncer
apt install -y pgbouncer

# Configure PgBouncer
cat > /etc/pgbouncer/pgbouncer.ini << EOF
[databases]
${DB_NAME} = host=localhost port=5432 dbname=${DB_NAME}

[pgbouncer]
listen_port = 6432
listen_addr = 127.0.0.1
auth_type = md5
auth_file = /etc/pgbouncer/userlist.txt
logfile = /var/log/pgbouncer/pgbouncer.log
pidfile = /var/run/pgbouncer/pgbouncer.pid
admin_users = postgres
pool_mode = transaction
max_client_conn = 100
default_pool_size = 20
reserve_pool_size = 5
server_reset_query = DISCARD ALL
EOF

# Create user list for PgBouncer
echo "\"${DB_USER}\" \"${DB_PASSWORD}\"" > /etc/pgbouncer/userlist.txt
chmod 600 /etc/pgbouncer/userlist.txt

# Start PgBouncer
systemctl enable pgbouncer
systemctl start pgbouncer

print_status "PgBouncer configured and started on port 6432"

echo -e "\n${GREEN}🎉 DATABASE SCHEMA IMPORT COMPLETED!${NC}"
echo "============================================================"
echo -e "${YELLOW}📊 IMPORT SUMMARY:${NC}"
echo "• Products: ${PRODUCT_COUNT} active products"
echo "• Categories: ${CATEGORY_COUNT} active categories" 
echo "• Repair Categories: ${REPAIR_CATEGORIES} service categories"
echo "• Device Types: ${DEVICE_TYPES} supported devices"
echo "• Inventory Value: \$${INVENTORY_VALUE}"
echo ""
echo -e "${BLUE}🔗 CONNECTION DETAILS:${NC}"
echo "• Direct Connection: postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}"
echo "• Pooled Connection: postgresql://${DB_USER}:${DB_PASSWORD}@localhost:6432/${DB_NAME}"
echo ""
echo -e "${YELLOW}📋 NEXT STEPS:${NC}"
echo "1. Set up automated backups: bash Scripts/setup-database-backups.sh"
echo "2. Configure monitoring: bash Scripts/setup-database-monitoring.sh"
echo "3. Update application .env.local with connection string"

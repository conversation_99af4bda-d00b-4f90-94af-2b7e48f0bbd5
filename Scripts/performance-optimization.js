#!/usr/bin/env node

/**
 * Performance Optimization Script
 * Optimizes database queries, adds indexes, and implements caching
 */

const { Pool } = require('pg');
const Redis = require('redis');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class PerformanceOptimizer {
  constructor() {
    this.redisClient = null;
    this.optimizations = [];
  }

  /**
   * Run all performance optimizations
   */
  async optimize() {
    console.log('🚀 Starting Performance Optimization for Midas Technical Platform');
    console.log('');

    try {
      // Database optimizations
      await this.optimizeDatabase();
      
      // Setup caching
      await this.setupCaching();
      
      // Optimize queries
      await this.optimizeQueries();
      
      // Create materialized views
      await this.createMaterializedViews();
      
      // Setup connection pooling
      await this.optimizeConnectionPooling();
      
      // Generate optimization report
      await this.generateOptimizationReport();
      
      console.log('✅ Performance optimization completed successfully!');
      
    } catch (error) {
      console.error('❌ Performance optimization failed:', error);
      throw error;
    }
  }

  /**
   * Optimize database performance
   */
  async optimizeDatabase() {
    console.log('🗄️ Optimizing database performance...');
    
    const client = await pool.connect();
    
    try {
      // Analyze table statistics
      await client.query('ANALYZE;');
      this.optimizations.push('Updated table statistics');
      
      // Create additional indexes for performance
      const indexes = [
        // Product search optimization
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_search_vector ON products USING gin(to_tsvector(\'english\', name || \' \' || description));',
        
        // Order performance indexes
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_customer_created ON orders(customer_email, created_at DESC);',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_status_created ON orders(status, created_at DESC);',
        
        // Inventory tracking indexes
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inventory_transactions_product_created ON inventory_transactions(product_id, created_at DESC);',
        
        // Analytics indexes
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_events_name_created ON analytics_events(event_name, created_at DESC);',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ecommerce_events_action_created ON ecommerce_events(action, created_at DESC);',
        
        // Fulfillment indexes
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shipping_labels_order_tracking ON shipping_labels(order_id, tracking_number);',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_status_history_order_created ON order_status_history(order_id, created_at DESC);'
      ];
      
      for (const indexQuery of indexes) {
        try {
          await client.query(indexQuery);
          console.log(`   ✅ Created index: ${indexQuery.split(' ')[5]}`);
          this.optimizations.push(`Created index: ${indexQuery.split(' ')[5]}`);
        } catch (error) {
          if (!error.message.includes('already exists')) {
            console.warn(`   ⚠️ Index creation warning: ${error.message}`);
          }
        }
      }
      
      // Optimize PostgreSQL settings
      await this.optimizePostgreSQLSettings(client);
      
    } finally {
      client.release();
    }
  }

  /**
   * Optimize PostgreSQL settings
   */
  async optimizePostgreSQLSettings(client) {
    console.log('⚙️ Optimizing PostgreSQL settings...');
    
    const settings = [
      // Memory settings
      { name: 'shared_buffers', value: '256MB', description: 'Shared buffer cache' },
      { name: 'effective_cache_size', value: '1GB', description: 'Effective cache size' },
      { name: 'work_mem', value: '4MB', description: 'Working memory per operation' },
      
      // Query optimization
      { name: 'random_page_cost', value: '1.1', description: 'Random page cost for SSD' },
      { name: 'effective_io_concurrency', value: '200', description: 'IO concurrency for SSD' },
      
      // Connection settings
      { name: 'max_connections', value: '200', description: 'Maximum connections' }
    ];
    
    for (const setting of settings) {
      try {
        // Check current value
        const result = await client.query(`SHOW ${setting.name};`);
        const currentValue = result.rows[0][setting.name];
        
        console.log(`   📊 ${setting.name}: ${currentValue} (recommended: ${setting.value})`);
        
        if (currentValue !== setting.value) {
          console.log(`   💡 Consider updating ${setting.name} to ${setting.value} in postgresql.conf`);
          this.optimizations.push(`Recommend ${setting.name} = ${setting.value} (${setting.description})`);
        }
        
      } catch (error) {
        console.warn(`   ⚠️ Could not check ${setting.name}: ${error.message}`);
      }
    }
  }

  /**
   * Setup Redis caching
   */
  async setupCaching() {
    console.log('🗄️ Setting up Redis caching...');
    
    try {
      // Try to connect to Redis
      if (process.env.REDIS_URL) {
        this.redisClient = Redis.createClient({
          url: process.env.REDIS_URL
        });
        
        await this.redisClient.connect();
        
        // Test Redis connection
        await this.redisClient.set('test_key', 'test_value', { EX: 60 });
        const testValue = await this.redisClient.get('test_key');
        
        if (testValue === 'test_value') {
          console.log('   ✅ Redis caching enabled');
          this.optimizations.push('Redis caching configured');
          
          // Setup cache warming
          await this.warmCache();
        }
        
      } else {
        console.log('   ⚠️ Redis not configured. Consider adding REDIS_URL for caching.');
        this.optimizations.push('Recommend Redis caching for improved performance');
      }
      
    } catch (error) {
      console.warn(`   ⚠️ Redis setup failed: ${error.message}`);
      this.optimizations.push('Redis caching not available - consider setting up Redis');
    }
  }

  /**
   * Warm up cache with frequently accessed data
   */
  async warmCache() {
    if (!this.redisClient) return;
    
    console.log('🔥 Warming up cache...');
    
    const client = await pool.connect();
    
    try {
      // Cache popular products
      const popularProducts = await client.query(`
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.is_active = true 
        ORDER BY p.created_at DESC 
        LIMIT 50
      `);
      
      await this.redisClient.setEx(
        'popular_products',
        3600, // 1 hour
        JSON.stringify(popularProducts.rows)
      );
      
      // Cache categories
      const categories = await client.query(`
        SELECT c.*, COUNT(p.id) as product_count
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.is_active = true
        WHERE c.is_active = true
        GROUP BY c.id
        ORDER BY c.name
      `);
      
      await this.redisClient.setEx(
        'categories',
        7200, // 2 hours
        JSON.stringify(categories.rows)
      );
      
      console.log('   ✅ Cache warmed with popular products and categories');
      this.optimizations.push('Cache warmed with frequently accessed data');
      
    } finally {
      client.release();
    }
  }

  /**
   * Optimize database queries
   */
  async optimizeQueries() {
    console.log('🔍 Optimizing database queries...');
    
    const client = await pool.connect();
    
    try {
      // Analyze slow queries
      const slowQueries = await client.query(`
        SELECT query, calls, total_time, mean_time, rows
        FROM pg_stat_statements 
        WHERE mean_time > 100 
        ORDER BY mean_time DESC 
        LIMIT 10
      `);
      
      if (slowQueries.rows.length > 0) {
        console.log('   📊 Slow queries detected:');
        slowQueries.rows.forEach((query, index) => {
          console.log(`   ${index + 1}. Mean time: ${query.mean_time.toFixed(2)}ms - ${query.query.substring(0, 100)}...`);
        });
        this.optimizations.push(`Found ${slowQueries.rows.length} slow queries for optimization`);
      } else {
        console.log('   ✅ No slow queries detected');
      }
      
      // Check for missing indexes
      const missingIndexes = await client.query(`
        SELECT schemaname, tablename, attname, n_distinct, correlation
        FROM pg_stats
        WHERE schemaname = 'public'
        AND n_distinct > 100
        AND correlation < 0.1
        ORDER BY n_distinct DESC
        LIMIT 10
      `);
      
      if (missingIndexes.rows.length > 0) {
        console.log('   📊 Potential missing indexes:');
        missingIndexes.rows.forEach(row => {
          console.log(`   - ${row.tablename}.${row.attname} (distinct: ${row.n_distinct})`);
        });
        this.optimizations.push(`Identified ${missingIndexes.rows.length} potential index opportunities`);
      }
      
    } catch (error) {
      console.warn(`   ⚠️ Query analysis failed: ${error.message}`);
      console.log('   💡 Consider enabling pg_stat_statements extension for query analysis');
      this.optimizations.push('Recommend enabling pg_stat_statements for query optimization');
    } finally {
      client.release();
    }
  }

  /**
   * Create materialized views for complex queries
   */
  async createMaterializedViews() {
    console.log('📊 Creating materialized views...');
    
    const client = await pool.connect();
    
    try {
      // Product analytics view
      await client.query(`
        CREATE MATERIALIZED VIEW IF NOT EXISTS product_analytics AS
        SELECT 
          p.id,
          p.name,
          p.sku,
          p.price,
          p.stock_quantity,
          c.name as category_name,
          COALESCE(order_stats.total_sold, 0) as total_sold,
          COALESCE(order_stats.revenue, 0) as revenue,
          COALESCE(view_stats.view_count, 0) as view_count
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN (
          SELECT 
            oi.product_id,
            SUM(oi.quantity) as total_sold,
            SUM(oi.quantity * oi.unit_price) as revenue
          FROM order_items oi
          JOIN orders o ON oi.order_id = o.id
          WHERE o.status NOT IN ('cancelled', 'refunded')
          GROUP BY oi.product_id
        ) order_stats ON p.id = order_stats.product_id
        LEFT JOIN (
          SELECT 
            (properties->>'productId')::integer as product_id,
            COUNT(*) as view_count
          FROM analytics_events
          WHERE event_name = 'product_view'
          AND properties->>'productId' IS NOT NULL
          GROUP BY properties->>'productId'
        ) view_stats ON p.id = view_stats.product_id
        WHERE p.is_active = true;
      `);
      
      // Create unique index on materialized view
      await client.query(`
        CREATE UNIQUE INDEX IF NOT EXISTS idx_product_analytics_id 
        ON product_analytics(id);
      `);
      
      console.log('   ✅ Created product_analytics materialized view');
      this.optimizations.push('Created product_analytics materialized view');
      
      // Order fulfillment dashboard view
      await client.query(`
        CREATE MATERIALIZED VIEW IF NOT EXISTS fulfillment_dashboard AS
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as total_orders,
          COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_orders,
          COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
          COUNT(CASE WHEN status = 'ready_to_ship' THEN 1 END) as ready_to_ship,
          COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped_orders,
          COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
          SUM(total_amount) as daily_revenue,
          AVG(EXTRACT(EPOCH FROM (shipped_at - created_at))/3600) as avg_processing_hours
        FROM orders
        WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(created_at)
        ORDER BY date DESC;
      `);
      
      console.log('   ✅ Created fulfillment_dashboard materialized view');
      this.optimizations.push('Created fulfillment_dashboard materialized view');
      
      // Refresh materialized views
      await client.query('REFRESH MATERIALIZED VIEW product_analytics;');
      await client.query('REFRESH MATERIALIZED VIEW fulfillment_dashboard;');
      
      console.log('   ✅ Refreshed materialized views');
      
    } catch (error) {
      console.warn(`   ⚠️ Materialized view creation failed: ${error.message}`);
    } finally {
      client.release();
    }
  }

  /**
   * Optimize connection pooling
   */
  async optimizeConnectionPooling() {
    console.log('🔗 Optimizing connection pooling...');
    
    // Check current pool configuration
    const poolConfig = {
      max: pool.options.max || 20,
      min: pool.options.min || 0,
      idleTimeoutMillis: pool.options.idleTimeoutMillis || 30000,
      connectionTimeoutMillis: pool.options.connectionTimeoutMillis || 2000
    };
    
    console.log('   📊 Current pool configuration:');
    console.log(`      Max connections: ${poolConfig.max}`);
    console.log(`      Min connections: ${poolConfig.min}`);
    console.log(`      Idle timeout: ${poolConfig.idleTimeoutMillis}ms`);
    console.log(`      Connection timeout: ${poolConfig.connectionTimeoutMillis}ms`);
    
    // Recommendations
    const recommendations = [];
    
    if (poolConfig.max < 20) {
      recommendations.push('Consider increasing max connections to 20-50 for production');
    }
    
    if (poolConfig.idleTimeoutMillis > 30000) {
      recommendations.push('Consider reducing idle timeout to 30 seconds');
    }
    
    if (recommendations.length > 0) {
      console.log('   💡 Recommendations:');
      recommendations.forEach(rec => {
        console.log(`      - ${rec}`);
        this.optimizations.push(rec);
      });
    } else {
      console.log('   ✅ Connection pooling configuration looks good');
    }
  }

  /**
   * Generate optimization report
   */
  async generateOptimizationReport() {
    console.log('📋 Generating optimization report...');
    
    const report = {
      timestamp: new Date().toISOString(),
      optimizations: this.optimizations,
      performance_recommendations: [
        'Enable Redis caching for frequently accessed data',
        'Monitor slow queries using pg_stat_statements',
        'Refresh materialized views regularly (daily/hourly)',
        'Consider read replicas for high-traffic scenarios',
        'Implement CDN for static assets',
        'Use connection pooling in production',
        'Monitor database performance metrics',
        'Set up automated backups and point-in-time recovery'
      ]
    };
    
    // Save report to database
    const client = await pool.connect();
    
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS performance_optimization_reports (
          id SERIAL PRIMARY KEY,
          optimizations JSONB,
          recommendations JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
      `);
      
      await client.query(`
        INSERT INTO performance_optimization_reports (optimizations, recommendations)
        VALUES ($1, $2)
      `, [
        JSON.stringify(report.optimizations),
        JSON.stringify(report.performance_recommendations)
      ]);
      
      console.log('   ✅ Optimization report saved to database');
      
    } finally {
      client.release();
    }
    
    // Display summary
    console.log('\n📊 OPTIMIZATION SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total optimizations applied: ${this.optimizations.length}`);
    console.log('\nOptimizations:');
    this.optimizations.forEach((opt, index) => {
      console.log(`   ${index + 1}. ${opt}`);
    });
    
    console.log('\n💡 Additional Recommendations:');
    report.performance_recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    if (this.redisClient) {
      await this.redisClient.quit();
    }
  }
}

// Run optimization if called directly
if (require.main === module) {
  const optimizer = new PerformanceOptimizer();
  
  optimizer.optimize()
    .then(() => {
      console.log('\n🎉 Performance optimization completed successfully!');
      return optimizer.cleanup();
    })
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Performance optimization failed:', error);
      optimizer.cleanup().then(() => process.exit(1));
    });
}

module.exports = PerformanceOptimizer;

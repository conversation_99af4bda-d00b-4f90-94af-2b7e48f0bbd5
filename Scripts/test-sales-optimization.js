#!/usr/bin/env node

/**
 * Sales Optimization System Testing Script
 * Tests cart recovery, dynamic pricing, recommendations, and analytics
 */

const axios = require('axios');
const { Pool } = require('pg');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class SalesOptimizationTester {
  constructor() {
    this.baseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://midastechnical.com'
      : 'http://localhost:3001';
    
    this.testResults = {
      cartRecovery: { passed: 0, failed: 0, tests: [] },
      dynamicPricing: { passed: 0, failed: 0, tests: [] },
      recommendations: { passed: 0, failed: 0, tests: [] },
      analytics: { passed: 0, failed: 0, tests: [] },
      automation: { passed: 0, failed: 0, tests: [] },
      overall: { passed: 0, failed: 0 }
    };

    this.testData = {
      users: [],
      products: [],
      carts: []
    };
  }

  /**
   * Run comprehensive sales optimization tests
   */
  async runTests() {
    console.log('🛒 COMPREHENSIVE SALES OPTIMIZATION TESTING');
    console.log('='.repeat(60));
    console.log(`🌐 Testing URL: ${this.baseUrl}`);
    console.log(`⏰ Started: ${new Date().toISOString()}`);
    console.log('');

    try {
      // Setup test data
      await this.setupTestData();
      
      // Test cart abandonment and recovery
      await this.testCartAbandonmentRecovery();
      
      // Test dynamic pricing system
      await this.testDynamicPricing();
      
      // Test product recommendations
      await this.testProductRecommendations();
      
      // Test sales analytics
      await this.testSalesAnalytics();
      
      // Test automation processing
      await this.testAutomationProcessing();
      
      // Generate comprehensive report
      await this.generateTestReport();
      
      // Cleanup test data
      await this.cleanupTestData();
      
      console.log('✅ Sales optimization testing completed!');
      return this.testResults.overall.failed === 0;

    } catch (error) {
      console.error('❌ Testing failed:', error);
      return false;
    }
  }

  /**
   * Setup test data
   */
  async setupTestData() {
    console.log('🔧 Setting up test data...');
    
    try {
      const client = await pool.connect();
      
      // Get sample products
      const productsResult = await client.query(`
        SELECT id, name, price, category_id, stock_quantity
        FROM products 
        WHERE is_active = true 
        AND stock_quantity > 0
        LIMIT 10
      `);
      
      this.testData.products = productsResult.rows;
      
      if (this.testData.products.length === 0) {
        throw new Error('No active products found for testing');
      }
      
      // Create test users
      for (let i = 0; i < 3; i++) {
        const email = `test.sales.${Date.now()}.${i}@example.com`;
        const userResult = await client.query(`
          INSERT INTO users (
            email, first_name, last_name, password_hash, 
            email_verified, created_at
          ) VALUES ($1, $2, $3, $4, true, CURRENT_TIMESTAMP)
          RETURNING id, email
        `, [email, `TestSales${i}`, 'User', 'hashed_password']);
        
        this.testData.users.push(userResult.rows[0]);
      }
      
      client.release();
      
      console.log(`   ✅ Created ${this.testData.users.length} test users`);
      console.log(`   ✅ Found ${this.testData.products.length} test products`);
      
    } catch (error) {
      console.error('❌ Failed to setup test data:', error);
      throw error;
    }
  }

  /**
   * Test cart abandonment and recovery system
   */
  async testCartAbandonmentRecovery() {
    console.log('🛒 Testing Cart Abandonment & Recovery...');
    
    try {
      const { salesOptimizationService } = require('../lib/sales-optimization.js');
      const testUser = this.testData.users[0];
      const testProducts = this.testData.products.slice(0, 3);
      
      // Create test cart data
      const cartData = {
        items: testProducts.map(product => ({
          productId: product.id,
          quantity: 2,
          price: product.price,
          name: product.name
        }))
      };
      
      // Test cart abandonment tracking
      await salesOptimizationService.trackCartAbandonment(testUser.id, cartData);
      this.addTestResult('cartRecovery', 'Cart Abandonment Tracking', 'passed', 'Cart abandonment tracked successfully');
      
      // Verify abandonment was recorded
      const client = await pool.connect();
      const abandonmentResult = await client.query(`
        SELECT * FROM abandoned_carts WHERE user_id = $1
      `, [testUser.id]);
      
      if (abandonmentResult.rows.length > 0) {
        const abandonedCart = abandonmentResult.rows[0];
        this.addTestResult('cartRecovery', 'Abandonment Database Record', 'passed', 
          `Cart value: $${abandonedCart.cart_value}, Items: ${abandonedCart.items_count}`);
        
        // Test cart recovery email processing
        try {
          await salesOptimizationService.processAbandonedCartRecovery();
          this.addTestResult('cartRecovery', 'Recovery Email Processing', 'passed', 'Recovery processing completed');
          
          // Check if recovery emails were sent
          const recoveryResult = await client.query(`
            SELECT recovery_emails_sent FROM abandoned_carts WHERE user_id = $1
          `, [testUser.id]);
          
          if (recoveryResult.rows.length > 0) {
            const emailsSent = recoveryResult.rows[0].recovery_emails_sent;
            if (emailsSent > 0) {
              this.addTestResult('cartRecovery', 'Recovery Email Sent', 'passed', `${emailsSent} recovery emails sent`);
            } else {
              this.addTestResult('cartRecovery', 'Recovery Email Sent', 'warning', 'No recovery emails sent (may be due to timing)');
            }
          }
          
        } catch (error) {
          this.addTestResult('cartRecovery', 'Recovery Email Processing', 'failed', error.message);
        }
        
      } else {
        this.addTestResult('cartRecovery', 'Abandonment Database Record', 'failed', 'No abandonment record found');
      }
      
      client.release();
      
    } catch (error) {
      this.addTestResult('cartRecovery', 'Cart Recovery System', 'failed', error.message);
    }
  }

  /**
   * Test dynamic pricing system
   */
  async testDynamicPricing() {
    console.log('💰 Testing Dynamic Pricing System...');
    
    try {
      const { salesOptimizationService } = require('../lib/sales-optimization.js');
      const testProduct = this.testData.products[0];
      const testUser = this.testData.users[0];
      
      // Test different quantity tiers
      const quantityTests = [
        { quantity: 1, expectedDiscount: false },
        { quantity: 15, expectedDiscount: true, minDiscount: 5 },
        { quantity: 30, expectedDiscount: true, minDiscount: 10 },
        { quantity: 60, expectedDiscount: true, minDiscount: 15 }
      ];
      
      for (const test of quantityTests) {
        const pricing = await salesOptimizationService.calculateDynamicPricing(
          testProduct.id,
          testUser.id,
          test.quantity
        );
        
        if (test.expectedDiscount) {
          const discountPercentage = ((pricing.originalPrice - pricing.finalPrice) / pricing.originalPrice) * 100;
          if (discountPercentage >= test.minDiscount) {
            this.addTestResult('dynamicPricing', `Bulk Discount (${test.quantity} units)`, 'passed', 
              `${discountPercentage.toFixed(1)}% discount applied`);
          } else {
            this.addTestResult('dynamicPricing', `Bulk Discount (${test.quantity} units)`, 'failed', 
              `Expected ${test.minDiscount}% discount, got ${discountPercentage.toFixed(1)}%`);
          }
        } else {
          if (pricing.finalPrice === pricing.originalPrice) {
            this.addTestResult('dynamicPricing', `No Discount (${test.quantity} units)`, 'passed', 
              'No discount correctly applied for small quantity');
          } else {
            this.addTestResult('dynamicPricing', `No Discount (${test.quantity} units)`, 'failed', 
              'Unexpected discount applied');
          }
        }
      }
      
      // Test API endpoint
      const response = await this.makeRequest('GET', 
        `/api/sales/dynamic-pricing?productId=${testProduct.id}&quantity=25&userId=${testUser.id}`);
      
      if (response.status === 200 && response.data.success) {
        const apiPricing = response.data.data;
        this.addTestResult('dynamicPricing', 'Dynamic Pricing API', 'passed', 
          `API returned pricing: $${apiPricing.finalPrice} (${apiPricing.discounts.length} discounts)`);
      } else {
        this.addTestResult('dynamicPricing', 'Dynamic Pricing API', 'failed', 
          `API returned ${response.status}`);
      }
      
    } catch (error) {
      this.addTestResult('dynamicPricing', 'Dynamic Pricing System', 'failed', error.message);
    }
  }

  /**
   * Test product recommendations
   */
  async testProductRecommendations() {
    console.log('🎯 Testing Product Recommendations...');
    
    try {
      const testProduct = this.testData.products[0];
      
      const recommendationTypes = [
        'frequently_bought_together',
        'customers_also_viewed',
        'category_bestsellers'
      ];
      
      for (const type of recommendationTypes) {
        const response = await this.makeRequest('GET', 
          `/api/sales/recommendations?productId=${testProduct.id}&type=${type}&limit=6`);
        
        if (response.status === 200 && response.data.success) {
          const recommendations = response.data.data.recommendations;
          this.addTestResult('recommendations', `${type} API`, 'passed', 
            `Retrieved ${recommendations.length} recommendations`);
          
          // Validate recommendation structure
          if (recommendations.length > 0) {
            const firstRec = recommendations[0];
            const hasRequiredFields = firstRec.id && firstRec.name && firstRec.price && firstRec.url;
            
            if (hasRequiredFields) {
              this.addTestResult('recommendations', `${type} Structure`, 'passed', 
                'Recommendations have required fields');
            } else {
              this.addTestResult('recommendations', `${type} Structure`, 'failed', 
                'Missing required fields in recommendations');
            }
          }
          
        } else {
          this.addTestResult('recommendations', `${type} API`, 'failed', 
            `API returned ${response.status}`);
        }
      }
      
      // Test recommendation service directly
      const { salesOptimizationService } = require('../lib/sales-optimization.js');
      const directRecommendations = await salesOptimizationService.getProductRecommendations(
        testProduct.id, 
        this.testData.users[0].id, 
        'personalized'
      );
      
      this.addTestResult('recommendations', 'Direct Service Call', 'passed', 
        `Service returned ${directRecommendations.length} personalized recommendations`);
      
    } catch (error) {
      this.addTestResult('recommendations', 'Recommendations System', 'failed', error.message);
    }
  }

  /**
   * Test sales analytics
   */
  async testSalesAnalytics() {
    console.log('📊 Testing Sales Analytics...');
    
    try {
      // Test analytics API endpoints
      const analyticsEndpoints = [
        { path: '/api/admin/sales-analytics?metric=overview', name: 'Overview Analytics' },
        { path: '/api/admin/sales-analytics?metric=funnel', name: 'Funnel Analytics' },
        { path: '/api/admin/sales-analytics?metric=abandonment', name: 'Abandonment Analytics' }
      ];
      
      for (const endpoint of analyticsEndpoints) {
        try {
          const response = await this.makeAuthenticatedRequest('GET', endpoint.path);
          
          if (response.status === 200 && response.data.success) {
            this.addTestResult('analytics', endpoint.name, 'passed', 'Analytics data retrieved successfully');
          } else if (response.status === 401 || response.status === 403) {
            this.addTestResult('analytics', endpoint.name, 'passed', 'Correctly protected admin endpoint');
          } else {
            this.addTestResult('analytics', endpoint.name, 'failed', `API returned ${response.status}`);
          }
        } catch (error) {
          this.addTestResult('analytics', endpoint.name, 'failed', error.message);
        }
      }
      
      // Test sales funnel analytics directly
      const { salesFunnelAnalytics } = require('../lib/sales-funnel-analytics.js');
      
      const dateFrom = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const dateTo = new Date().toISOString().split('T')[0];
      
      try {
        const funnelData = await salesFunnelAnalytics.getConversionFunnel(dateFrom, dateTo);
        
        if (funnelData.funnel && funnelData.overall) {
          this.addTestResult('analytics', 'Funnel Analytics Service', 'passed', 
            `Retrieved funnel data with ${funnelData.funnel.length} stages`);
        } else {
          this.addTestResult('analytics', 'Funnel Analytics Service', 'failed', 
            'Invalid funnel data structure');
        }
      } catch (error) {
        this.addTestResult('analytics', 'Funnel Analytics Service', 'failed', error.message);
      }
      
    } catch (error) {
      this.addTestResult('analytics', 'Analytics System', 'failed', error.message);
    }
  }

  /**
   * Test automation processing
   */
  async testAutomationProcessing() {
    console.log('🤖 Testing Automation Processing...');
    
    try {
      // Test sales automation script
      const { spawn } = require('child_process');
      
      const automationPromise = new Promise((resolve, reject) => {
        const process = spawn('node', ['scripts/process-sales-automation.js'], {
          cwd: process.cwd(),
          timeout: 30000
        });
        
        let output = '';
        let errorOutput = '';
        
        process.stdout.on('data', (data) => {
          output += data.toString();
        });
        
        process.stderr.on('data', (data) => {
          errorOutput += data.toString();
        });
        
        process.on('close', (code) => {
          if (code === 0) {
            resolve({ success: true, output });
          } else {
            reject(new Error(`Process exited with code ${code}: ${errorOutput}`));
          }
        });
        
        process.on('error', (error) => {
          reject(error);
        });
        
        // Kill process after timeout
        setTimeout(() => {
          process.kill('SIGTERM');
          resolve({ success: true, output: 'Process completed (timeout)' });
        }, 25000);
      });
      
      try {
        const result = await automationPromise;
        this.addTestResult('automation', 'Sales Automation Script', 'passed', 
          'Automation script executed successfully');
      } catch (error) {
        this.addTestResult('automation', 'Sales Automation Script', 'failed', error.message);
      }
      
      // Test individual automation components
      const { customerOnboardingService } = require('../lib/customer-onboarding.js');
      const { salesOptimizationService } = require('../lib/sales-optimization.js');
      
      try {
        await customerOnboardingService.processScheduledEmails();
        this.addTestResult('automation', 'Email Processing', 'passed', 'Email processing completed');
      } catch (error) {
        this.addTestResult('automation', 'Email Processing', 'failed', error.message);
      }
      
      try {
        await salesOptimizationService.processAbandonedCartRecovery();
        this.addTestResult('automation', 'Cart Recovery Processing', 'passed', 'Cart recovery processing completed');
      } catch (error) {
        this.addTestResult('automation', 'Cart Recovery Processing', 'failed', error.message);
      }
      
    } catch (error) {
      this.addTestResult('automation', 'Automation System', 'failed', error.message);
    }
  }

  /**
   * Make HTTP request
   */
  async makeRequest(method, path, data = null) {
    try {
      const config = {
        method,
        url: `${this.baseUrl}${path}`,
        timeout: 10000,
        validateStatus: () => true
      };

      if (data) {
        config.data = data;
        config.headers = { 'Content-Type': 'application/json' };
      }

      return await axios(config);
    } catch (error) {
      return { status: 0, error: error.message };
    }
  }

  /**
   * Make authenticated request (for admin endpoints)
   */
  async makeAuthenticatedRequest(method, path, data = null) {
    // For testing admin endpoints, we'll simulate admin authentication
    const config = {
      method,
      url: `${this.baseUrl}${path}`,
      timeout: 10000,
      validateStatus: () => true,
      headers: {
        'Content-Type': 'application/json',
        'X-Test-Admin': 'true' // Test header for admin simulation
      }
    };

    if (data) {
      config.data = data;
    }

    try {
      return await axios(config);
    } catch (error) {
      return { status: 0, error: error.message };
    }
  }

  /**
   * Add test result
   */
  addTestResult(category, testName, status, message) {
    const result = {
      name: testName,
      status,
      message,
      timestamp: new Date().toISOString()
    };

    this.testResults[category].tests.push(result);

    if (status === 'passed') {
      this.testResults[category].passed++;
      this.testResults.overall.passed++;
    } else {
      this.testResults[category].failed++;
      this.testResults.overall.failed++;
    }

    const icon = status === 'passed' ? '✅' : status === 'warning' ? '⚠️' : '❌';
    console.log(`   ${icon} ${testName}: ${message}`);
  }

  /**
   * Generate comprehensive test report
   */
  async generateTestReport() {
    console.log('\n📋 SALES OPTIMIZATION TEST REPORT');
    console.log('='.repeat(60));

    const totalTests = this.testResults.overall.passed + this.testResults.overall.failed;
    const successRate = totalTests > 0 ? (this.testResults.overall.passed / totalTests * 100).toFixed(1) : 0;

    console.log(`Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${this.testResults.overall.passed}`);
    console.log(`❌ Failed: ${this.testResults.overall.failed}`);
    console.log(`📊 Success Rate: ${successRate}%`);
    console.log('');

    // Category breakdown
    Object.entries(this.testResults).forEach(([category, results]) => {
      if (category === 'overall') return;
      
      const categoryTotal = results.passed + results.failed;
      const categoryRate = categoryTotal > 0 ? (results.passed / categoryTotal * 100).toFixed(1) : 0;
      
      console.log(`${category.toUpperCase()}: ${results.passed}/${categoryTotal} (${categoryRate}%)`);
      
      // Show failed tests
      results.tests.forEach(test => {
        if (test.status === 'failed') {
          console.log(`   ❌ ${test.name}: ${test.message}`);
        }
      });
    });

    // Save test report
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO test_reports (
          test_type, total_tests, passed_tests, failed_tests, success_rate, test_results
        ) VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        'sales_optimization',
        totalTests,
        this.testResults.overall.passed,
        this.testResults.overall.failed,
        parseFloat(successRate),
        JSON.stringify(this.testResults)
      ]);

      client.release();
      console.log('\n✅ Test report saved to database');

    } catch (error) {
      console.error('\n❌ Failed to save test report:', error.message);
    }
  }

  /**
   * Cleanup test data
   */
  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');
    
    try {
      const client = await pool.connect();
      
      // Remove test users and related data
      for (const user of this.testData.users) {
        await client.query('DELETE FROM users WHERE id = $1', [user.id]);
      }
      
      client.release();
      console.log('✅ Test data cleanup completed');
      
    } catch (error) {
      console.error('❌ Failed to cleanup test data:', error.message);
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new SalesOptimizationTester();
  
  tester.runTests()
    .then((success) => {
      if (success) {
        console.log('\n🎉 All sales optimization tests passed!');
        process.exit(0);
      } else {
        console.log('\n💥 Some tests failed!');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Testing crashed:', error);
      process.exit(1);
    });
}

module.exports = SalesOptimizationTester;

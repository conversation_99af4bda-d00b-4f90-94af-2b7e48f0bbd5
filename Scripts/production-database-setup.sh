#!/bin/bash

# =====================================================
# MIDAS TECHNICAL PRODUCTION DATABASE SETUP SCRIPT
# =====================================================
# This script sets up a production PostgreSQL database with all requirements
# Run with: sudo bash Scripts/production-database-setup.sh

set -e  # Exit on any error

# Configuration variables
DB_NAME="midastechnical_store"
DB_USER="midastechnical_user"
DB_PASSWORD=""  # Will be generated
POSTGRES_VERSION="14"
BACKUP_BUCKET="midastechnical-backups"
AWS_REGION="us-east-1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 MIDAS TECHNICAL PRODUCTION DATABASE SETUP${NC}"
echo "============================================================"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Generate secure password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Step 1: System Requirements Check
echo -e "\n${BLUE}📋 Step 1: Checking System Requirements${NC}"
echo "------------------------------------------------------------"

# Check RAM
TOTAL_RAM=$(free -g | awk '/^Mem:/{print $2}')
if [ "$TOTAL_RAM" -lt 4 ]; then
    print_error "Insufficient RAM: ${TOTAL_RAM}GB (minimum 4GB required)"
    exit 1
else
    print_status "RAM: ${TOTAL_RAM}GB (✓ meets minimum 4GB requirement)"
fi

# Check CPU cores
CPU_CORES=$(nproc)
if [ "$CPU_CORES" -lt 2 ]; then
    print_error "Insufficient CPU cores: ${CPU_CORES} (minimum 2 required)"
    exit 1
else
    print_status "CPU Cores: ${CPU_CORES} (✓ meets minimum 2 cores requirement)"
fi

# Check disk space
DISK_SPACE=$(df -h / | awk 'NR==2{print $4}' | sed 's/G//')
if [ "${DISK_SPACE%.*}" -lt 20 ]; then
    print_warning "Low disk space: ${DISK_SPACE}GB (recommend 50GB+ for production)"
else
    print_status "Disk Space: ${DISK_SPACE}GB available"
fi

# Step 2: Install PostgreSQL 14+
echo -e "\n${BLUE}📦 Step 2: Installing PostgreSQL ${POSTGRES_VERSION}${NC}"
echo "------------------------------------------------------------"

# Update system packages
print_info "Updating system packages..."
apt update -y

# Install required packages
print_info "Installing required packages..."
apt install -y wget ca-certificates

# Add PostgreSQL official APT repository
if [ ! -f /etc/apt/sources.list.d/pgdg.list ]; then
    print_info "Adding PostgreSQL official repository..."
    wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add -
    echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list
    apt update -y
fi

# Install PostgreSQL
print_info "Installing PostgreSQL ${POSTGRES_VERSION}..."
apt install -y postgresql-${POSTGRES_VERSION} postgresql-client-${POSTGRES_VERSION} postgresql-contrib-${POSTGRES_VERSION}

# Start and enable PostgreSQL
systemctl start postgresql
systemctl enable postgresql

print_status "PostgreSQL ${POSTGRES_VERSION} installed and started"

# Step 3: Configure PostgreSQL for Production
echo -e "\n${BLUE}⚙️  Step 3: Configuring PostgreSQL for Production${NC}"
echo "------------------------------------------------------------"

# Calculate optimal settings based on available RAM
SHARED_BUFFERS=$((TOTAL_RAM * 256))  # 25% of RAM in MB
EFFECTIVE_CACHE_SIZE=$((TOTAL_RAM * 768))  # 75% of RAM in MB

print_info "Configuring PostgreSQL settings..."
print_info "Shared Buffers: ${SHARED_BUFFERS}MB (25% of ${TOTAL_RAM}GB RAM)"
print_info "Effective Cache Size: ${EFFECTIVE_CACHE_SIZE}MB (75% of ${TOTAL_RAM}GB RAM)"

# Backup original configuration
cp /etc/postgresql/${POSTGRES_VERSION}/main/postgresql.conf /etc/postgresql/${POSTGRES_VERSION}/main/postgresql.conf.backup

# Configure PostgreSQL settings
cat >> /etc/postgresql/${POSTGRES_VERSION}/main/postgresql.conf << EOF

# =====================================================
# MIDAS TECHNICAL PRODUCTION OPTIMIZATIONS
# =====================================================

# Memory Settings
shared_buffers = ${SHARED_BUFFERS}MB
effective_cache_size = ${EFFECTIVE_CACHE_SIZE}MB
work_mem = 16MB
maintenance_work_mem = 256MB

# Connection Settings
max_connections = 100
shared_preload_libraries = 'pg_stat_statements'

# Performance Settings
random_page_cost = 1.1
effective_io_concurrency = 200
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Logging Settings
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on

# SSL Configuration
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
ssl_min_protocol_version = 'TLSv1.2'

EOF

print_status "PostgreSQL configuration updated"

# Step 4: Configure SSL/TLS
echo -e "\n${BLUE}🔒 Step 4: Configuring SSL/TLS Security${NC}"
echo "------------------------------------------------------------"

print_info "Generating SSL certificates..."
cd /var/lib/postgresql/${POSTGRES_VERSION}/main/

# Generate private key
openssl genrsa -out server.key 2048
chmod 600 server.key
chown postgres:postgres server.key

# Generate certificate
openssl req -new -key server.key -days 365 -out server.crt -x509 -subj "/C=US/ST=CA/L=San Francisco/O=Midas Technical/CN=midastechnical.com"
chmod 644 server.crt
chown postgres:postgres server.crt

print_status "SSL certificates generated and configured"

# Step 5: Create Database and User
echo -e "\n${BLUE}👤 Step 5: Creating Database and User${NC}"
echo "------------------------------------------------------------"

# Generate secure password
DB_PASSWORD=$(generate_password)

print_info "Creating database and user..."

# Switch to postgres user and create database
sudo -u postgres psql << EOF
-- Create database
CREATE DATABASE ${DB_NAME};

-- Create user with secure password
CREATE USER ${DB_USER} WITH ENCRYPTED PASSWORD '${DB_PASSWORD}';

-- Grant necessary permissions
GRANT CONNECT ON DATABASE ${DB_NAME} TO ${DB_USER};
GRANT USAGE ON SCHEMA public TO ${DB_USER};
GRANT CREATE ON SCHEMA public TO ${DB_USER};
GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};

-- Enable required extensions
\c ${DB_NAME}
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Grant permissions on extensions
GRANT ALL ON ALL TABLES IN SCHEMA public TO ${DB_USER};
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO ${DB_USER};
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO ${DB_USER};

-- Set default permissions for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO ${DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO ${DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO ${DB_USER};

\q
EOF

print_status "Database '${DB_NAME}' and user '${DB_USER}' created"

# Save connection details
cat > /root/database_credentials.txt << EOF
# =====================================================
# MIDAS TECHNICAL DATABASE CREDENTIALS
# =====================================================
Database Name: ${DB_NAME}
Database User: ${DB_USER}
Database Password: ${DB_PASSWORD}
Connection String: postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}

# Environment Variable for .env.local:
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}

# =====================================================
# SECURITY NOTICE
# =====================================================
# Store these credentials securely and delete this file after copying
# Never commit these credentials to version control
EOF

chmod 600 /root/database_credentials.txt

print_status "Database credentials saved to /root/database_credentials.txt"

# Step 6: Configure Authentication
echo -e "\n${BLUE}🔐 Step 6: Configuring Authentication${NC}"
echo "------------------------------------------------------------"

# Configure pg_hba.conf for secure connections
print_info "Configuring client authentication..."

# Backup original pg_hba.conf
cp /etc/postgresql/${POSTGRES_VERSION}/main/pg_hba.conf /etc/postgresql/${POSTGRES_VERSION}/main/pg_hba.conf.backup

# Configure secure authentication
cat > /etc/postgresql/${POSTGRES_VERSION}/main/pg_hba.conf << EOF
# =====================================================
# MIDAS TECHNICAL PRODUCTION AUTHENTICATION
# =====================================================

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# Local connections
local   all             postgres                                peer
local   all             all                                     md5

# IPv4 local connections
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5

# Production application connections (SSL required)
hostssl ${DB_NAME}      ${DB_USER}      0.0.0.0/0               md5

# Deny all other connections
host    all             all             0.0.0.0/0               reject

EOF

print_status "Authentication configured with SSL requirement"

# Restart PostgreSQL to apply changes
print_info "Restarting PostgreSQL to apply configuration changes..."
systemctl restart postgresql

# Test connection
print_info "Testing database connection..."
if sudo -u postgres psql -d ${DB_NAME} -c "SELECT version();" > /dev/null 2>&1; then
    print_status "Database connection test successful"
else
    print_error "Database connection test failed"
    exit 1
fi

echo -e "\n${GREEN}🎉 DATABASE SETUP COMPLETED SUCCESSFULLY!${NC}"
echo "============================================================"
echo -e "${YELLOW}📋 NEXT STEPS:${NC}"
echo "1. Copy database credentials from /root/database_credentials.txt"
echo "2. Run the schema import script: bash Scripts/import-database-schema.sh"
echo "3. Configure automated backups: bash Scripts/setup-database-backups.sh"
echo "4. Set up monitoring: bash Scripts/setup-database-monitoring.sh"
echo ""
echo -e "${BLUE}Connection String:${NC}"
echo "postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}"

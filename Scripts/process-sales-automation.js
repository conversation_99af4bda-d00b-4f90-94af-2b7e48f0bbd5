#!/usr/bin/env node

/**
 * Sales Automation Processing Script
 * Processes onboarding emails, cart recovery, and referral tracking
 * Should be run as a cron job every 15 minutes
 */

const { customerOnboardingService } = require('../lib/customer-onboarding.js');
const { salesOptimizationService } = require('../lib/sales-optimization.js');
const { referralProgramService } = require('../lib/referral-program.js');
const { Pool } = require('pg');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class SalesAutomationProcessor {
  constructor() {
    this.processedCount = {
      onboardingEmails: 0,
      cartRecoveryEmails: 0,
      referralProcessing: 0,
      errors: 0
    };
  }

  /**
   * Run all sales automation processes
   */
  async runAutomation() {
    console.log('🤖 Starting Sales Automation Processing');
    console.log(`⏰ ${new Date().toISOString()}`);
    console.log('');

    try {
      // Process onboarding emails
      await this.processOnboardingEmails();
      
      // Process cart recovery emails
      await this.processCartRecoveryEmails();
      
      // Process referral completions
      await this.processReferralCompletions();
      
      // Clean up old data
      await this.cleanupOldData();
      
      // Generate summary report
      await this.generateSummaryReport();
      
      console.log('✅ Sales automation processing completed successfully!');
      
    } catch (error) {
      console.error('❌ Sales automation processing failed:', error);
      this.processedCount.errors++;
      throw error;
    }
  }

  /**
   * Process scheduled onboarding emails
   */
  async processOnboardingEmails() {
    console.log('📧 Processing onboarding emails...');
    
    try {
      await customerOnboardingService.processScheduledEmails();
      
      // Count processed emails
      const client = await pool.connect();
      const result = await client.query(`
        SELECT COUNT(*) as count
        FROM email_campaigns_queue
        WHERE status = 'sent' 
        AND sent_at >= CURRENT_TIMESTAMP - INTERVAL '15 minutes'
      `);
      
      this.processedCount.onboardingEmails = parseInt(result.rows[0].count);
      client.release();
      
      console.log(`   ✅ Processed ${this.processedCount.onboardingEmails} onboarding emails`);
      
    } catch (error) {
      console.error('❌ Failed to process onboarding emails:', error);
      this.processedCount.errors++;
    }
  }

  /**
   * Process cart recovery emails
   */
  async processCartRecoveryEmails() {
    console.log('🛒 Processing cart recovery emails...');
    
    try {
      const beforeCount = await this.getCartRecoveryEmailCount();
      
      await salesOptimizationService.processAbandonedCartRecovery();
      
      const afterCount = await this.getCartRecoveryEmailCount();
      this.processedCount.cartRecoveryEmails = afterCount - beforeCount;
      
      console.log(`   ✅ Processed ${this.processedCount.cartRecoveryEmails} cart recovery emails`);
      
    } catch (error) {
      console.error('❌ Failed to process cart recovery emails:', error);
      this.processedCount.errors++;
    }
  }

  /**
   * Process referral completions
   */
  async processReferralCompletions() {
    console.log('👥 Processing referral completions...');
    
    try {
      const client = await pool.connect();
      
      // Find orders that should trigger referral completion
      const ordersResult = await client.query(`
        SELECT o.id, o.customer_email, o.total_amount, u.id as user_id
        FROM orders o
        JOIN users u ON o.customer_email = u.email
        JOIN referral_signups rs ON u.id = rs.referred_user_id
        WHERE o.status = 'confirmed'
        AND o.created_at >= CURRENT_TIMESTAMP - INTERVAL '15 minutes'
        AND rs.status = 'pending'
        AND o.total_amount >= 50 -- Minimum order value
      `);

      let processedReferrals = 0;
      
      for (const order of ordersResult.rows) {
        try {
          const result = await referralProgramService.processReferralCompletion(
            order.id,
            order.user_id,
            parseFloat(order.total_amount)
          );
          
          if (result.success) {
            processedReferrals++;
          }
        } catch (error) {
          console.error(`❌ Failed to process referral for order ${order.id}:`, error);
        }
      }
      
      this.processedCount.referralProcessing = processedReferrals;
      client.release();
      
      console.log(`   ✅ Processed ${processedReferrals} referral completions`);
      
    } catch (error) {
      console.error('❌ Failed to process referral completions:', error);
      this.processedCount.errors++;
    }
  }

  /**
   * Clean up old data
   */
  async cleanupOldData() {
    console.log('🧹 Cleaning up old data...');
    
    try {
      const client = await pool.connect();
      
      // Clean up old email verification tokens (older than 7 days)
      await client.query(`
        DELETE FROM email_verification_tokens
        WHERE expires_at < CURRENT_TIMESTAMP - INTERVAL '7 days'
      `);
      
      // Clean up old rate limit logs (older than 24 hours)
      await client.query(`
        DELETE FROM rate_limit_logs
        WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '24 hours'
      `);
      
      // Clean up old analytics events (older than 90 days)
      await client.query(`
        DELETE FROM analytics_events
        WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days'
        AND event_name NOT IN ('user_registered', 'complete_purchase')
      `);
      
      // Clean up old error logs (older than 30 days)
      await client.query(`
        DELETE FROM error_logs
        WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days'
        AND severity NOT IN ('critical', 'error')
      `);
      
      client.release();
      
      console.log('   ✅ Old data cleanup completed');
      
    } catch (error) {
      console.error('❌ Failed to clean up old data:', error);
      this.processedCount.errors++;
    }
  }

  /**
   * Generate summary report
   */
  async generateSummaryReport() {
    console.log('📊 Generating summary report...');
    
    try {
      const client = await pool.connect();
      
      // Get current metrics
      const metricsResult = await client.query(`
        WITH today_metrics AS (
          SELECT 
            COUNT(CASE WHEN co.started_at >= CURRENT_DATE THEN 1 END) as new_signups_today,
            COUNT(CASE WHEN co.status = 'completed' AND co.completed_at >= CURRENT_DATE THEN 1 END) as completed_onboarding_today,
            COUNT(CASE WHEN ac.created_at >= CURRENT_DATE THEN 1 END) as new_abandoned_carts_today,
            COUNT(CASE WHEN ac.recovered = true AND ac.recovered_at >= CURRENT_DATE THEN 1 END) as recovered_carts_today,
            COUNT(CASE WHEN rs.completed_at >= CURRENT_DATE THEN 1 END) as completed_referrals_today
          FROM customer_onboarding co
          FULL OUTER JOIN abandoned_carts ac ON 1=1
          FULL OUTER JOIN referral_signups rs ON 1=1
        )
        SELECT * FROM today_metrics
      `);
      
      const metrics = metricsResult.rows[0];
      
      // Save automation report
      await client.query(`
        INSERT INTO automation_reports (
          report_type, processed_counts, metrics, created_at
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
      `, [
        'sales_automation',
        JSON.stringify(this.processedCount),
        JSON.stringify(metrics)
      ]);
      
      client.release();
      
      console.log('\n📋 AUTOMATION SUMMARY REPORT');
      console.log('='.repeat(40));
      console.log(`📧 Onboarding emails sent: ${this.processedCount.onboardingEmails}`);
      console.log(`🛒 Cart recovery emails sent: ${this.processedCount.cartRecoveryEmails}`);
      console.log(`👥 Referrals processed: ${this.processedCount.referralProcessing}`);
      console.log(`❌ Errors encountered: ${this.processedCount.errors}`);
      console.log('');
      console.log('📊 TODAY\'S METRICS:');
      console.log(`   New signups: ${metrics.new_signups_today}`);
      console.log(`   Completed onboarding: ${metrics.completed_onboarding_today}`);
      console.log(`   New abandoned carts: ${metrics.new_abandoned_carts_today}`);
      console.log(`   Recovered carts: ${metrics.recovered_carts_today}`);
      console.log(`   Completed referrals: ${metrics.completed_referrals_today}`);
      
    } catch (error) {
      console.error('❌ Failed to generate summary report:', error);
      this.processedCount.errors++;
    }
  }

  /**
   * Get cart recovery email count
   */
  async getCartRecoveryEmailCount() {
    try {
      const client = await pool.connect();
      const result = await client.query(`
        SELECT SUM(recovery_emails_sent) as total
        FROM abandoned_carts
      `);
      client.release();
      
      return parseInt(result.rows[0].total || 0);
    } catch (error) {
      console.error('❌ Failed to get cart recovery email count:', error);
      return 0;
    }
  }

  /**
   * Check system health
   */
  async checkSystemHealth() {
    console.log('🏥 Checking system health...');
    
    try {
      const client = await pool.connect();
      
      // Check database connection
      await client.query('SELECT 1');
      
      // Check email service configuration
      const emailConfigured = process.env.SENDGRID_API_KEY && 
                             process.env.SENDGRID_API_KEY.startsWith('SG.');
      
      // Check critical tables
      const tablesResult = await client.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name IN (
          'customer_onboarding', 'abandoned_carts', 'referral_signups',
          'email_campaigns_queue', 'analytics_events'
        )
      `);
      
      const requiredTables = ['customer_onboarding', 'abandoned_carts', 'referral_signups', 'email_campaigns_queue', 'analytics_events'];
      const existingTables = tablesResult.rows.map(row => row.table_name);
      const missingTables = requiredTables.filter(table => !existingTables.includes(table));
      
      client.release();
      
      if (missingTables.length > 0) {
        console.warn(`⚠️ Missing database tables: ${missingTables.join(', ')}`);
        return false;
      }
      
      if (!emailConfigured) {
        console.warn('⚠️ Email service not properly configured');
        return false;
      }
      
      console.log('✅ System health check passed');
      return true;
      
    } catch (error) {
      console.error('❌ System health check failed:', error);
      return false;
    }
  }
}

// Create automation report table if it doesn't exist
async function createAutomationReportTable() {
  try {
    const client = await pool.connect();
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS automation_reports (
        id SERIAL PRIMARY KEY,
        report_type VARCHAR(100) NOT NULL,
        processed_counts JSONB,
        metrics JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_automation_reports_type_created 
      ON automation_reports(report_type, created_at)
    `);
    
    client.release();
    
  } catch (error) {
    console.error('❌ Failed to create automation report table:', error);
  }
}

// Run automation if called directly
if (require.main === module) {
  const processor = new SalesAutomationProcessor();
  
  createAutomationReportTable()
    .then(() => processor.checkSystemHealth())
    .then((healthy) => {
      if (!healthy) {
        console.error('💥 System health check failed. Aborting automation.');
        process.exit(1);
      }
      
      return processor.runAutomation();
    })
    .then(() => {
      console.log('\n🎉 Sales automation processing completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Sales automation processing failed:', error);
      process.exit(1);
    });
}

module.exports = SalesAutomationProcessor;

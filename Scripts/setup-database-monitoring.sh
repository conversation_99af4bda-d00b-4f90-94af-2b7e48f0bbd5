#!/bin/bash

# =====================================================
# MIDAS TECHNICAL DATABASE MONITORING SETUP SCRIPT
# =====================================================
# This script sets up comprehensive database monitoring
# Run after: bash Scripts/setup-database-backups.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📊 MIDAS TECHNICAL DATABASE MONITORING SETUP${NC}"
echo "============================================================"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Check if credentials file exists
if [ ! -f /root/database_credentials.txt ]; then
    print_error "Database credentials file not found. Run production-database-setup.sh first."
    exit 1
fi

# Extract database credentials
DB_NAME=$(grep "Database Name:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_USER=$(grep "Database User:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_PASSWORD=$(grep "Database Password:" /root/database_credentials.txt | cut -d: -f2 | xargs)

# Step 1: Install Monitoring Tools
echo -e "\n${BLUE}🛠️  Step 1: Installing Monitoring Tools${NC}"
echo "------------------------------------------------------------"

print_info "Installing monitoring packages..."
apt update -y
apt install -y htop iotop sysstat postgresql-contrib

print_status "Monitoring tools installed"

# Step 2: Configure PostgreSQL Statistics
echo -e "\n${BLUE}📈 Step 2: Configuring PostgreSQL Statistics${NC}"
echo "------------------------------------------------------------"

print_info "Enabling pg_stat_statements extension..."

export PGPASSWORD="${DB_PASSWORD}"

psql -h localhost -U ${DB_USER} -d ${DB_NAME} << EOF
-- Enable pg_stat_statements for query monitoring
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Create monitoring views
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 1000  -- queries taking more than 1 second
ORDER BY mean_time DESC;

-- Create connection monitoring view
CREATE OR REPLACE VIEW connection_stats AS
SELECT 
    datname,
    numbackends,
    xact_commit,
    xact_rollback,
    blks_read,
    blks_hit,
    tup_returned,
    tup_fetched,
    tup_inserted,
    tup_updated,
    tup_deleted
FROM pg_stat_database 
WHERE datname = '${DB_NAME}';

-- Create table size monitoring view
CREATE OR REPLACE VIEW table_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

EOF

print_status "PostgreSQL statistics configured"

# Step 3: Create Database Monitoring Script
echo -e "\n${BLUE}📊 Step 3: Creating Database Monitoring Script${NC}"
echo "------------------------------------------------------------"

cat > /usr/local/bin/midas-db-monitor.sh << 'EOF'
#!/bin/bash

# =====================================================
# MIDAS TECHNICAL DATABASE MONITORING SCRIPT
# =====================================================

# Configuration
LOG_FILE="/var/log/midas-db-monitor.log"
ALERT_EMAIL="<EMAIL>"
DISK_WARNING_THRESHOLD=80
DISK_CRITICAL_THRESHOLD=90
CONNECTION_WARNING_THRESHOLD=80
CONNECTION_CRITICAL_THRESHOLD=95

# Read database credentials
if [ ! -f /root/database_credentials.txt ]; then
    echo "ERROR: Database credentials file not found"
    exit 1
fi

DB_NAME=$(grep "Database Name:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_USER=$(grep "Database User:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_PASSWORD=$(grep "Database Password:" /root/database_credentials.txt | cut -d: -f2 | xargs)

export PGPASSWORD="${DB_PASSWORD}"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a ${LOG_FILE}
}

# Function to send alert (configure with your notification service)
send_alert() {
    local severity=$1
    local message=$2
    log_message "ALERT [${severity}]: ${message}"
    # Add your notification logic here (email, Slack, etc.)
}

# Check disk space
check_disk_space() {
    local usage=$(df /var/lib/postgresql | awk 'NR==2{print $5}' | sed 's/%//')
    
    if [ "$usage" -ge "$DISK_CRITICAL_THRESHOLD" ]; then
        send_alert "CRITICAL" "Disk space critical: ${usage}% used"
        return 2
    elif [ "$usage" -ge "$DISK_WARNING_THRESHOLD" ]; then
        send_alert "WARNING" "Disk space warning: ${usage}% used"
        return 1
    else
        log_message "Disk space OK: ${usage}% used"
        return 0
    fi
}

# Check database connections
check_connections() {
    local current_connections=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname='${DB_NAME}';" | xargs)
    local max_connections=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SHOW max_connections;" | xargs)
    local usage_percent=$((current_connections * 100 / max_connections))
    
    if [ "$usage_percent" -ge "$CONNECTION_CRITICAL_THRESHOLD" ]; then
        send_alert "CRITICAL" "Database connections critical: ${current_connections}/${max_connections} (${usage_percent}%)"
        return 2
    elif [ "$usage_percent" -ge "$CONNECTION_WARNING_THRESHOLD" ]; then
        send_alert "WARNING" "Database connections warning: ${current_connections}/${max_connections} (${usage_percent}%)"
        return 1
    else
        log_message "Database connections OK: ${current_connections}/${max_connections} (${usage_percent}%)"
        return 0
    fi
}

# Check for slow queries
check_slow_queries() {
    local slow_query_count=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT count(*) FROM slow_queries;" | xargs)
    
    if [ "$slow_query_count" -gt 10 ]; then
        send_alert "WARNING" "High number of slow queries detected: ${slow_query_count}"
        # Log the slowest queries
        psql -h localhost -U ${DB_USER} -d ${DB_NAME} -c "SELECT query, mean_time FROM slow_queries LIMIT 5;" >> ${LOG_FILE}
    else
        log_message "Slow queries OK: ${slow_query_count} queries > 1000ms"
    fi
}

# Check database size growth
check_database_size() {
    local db_size=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT pg_size_pretty(pg_database_size('${DB_NAME}'));" | xargs)
    log_message "Database size: ${db_size}"
}

# Check memory usage
check_memory_usage() {
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$memory_usage" -ge 90 ]; then
        send_alert "WARNING" "High memory usage: ${memory_usage}%"
    else
        log_message "Memory usage OK: ${memory_usage}%"
    fi
}

# Main monitoring function
run_monitoring() {
    log_message "Starting database monitoring check"
    
    check_disk_space
    check_connections
    check_slow_queries
    check_database_size
    check_memory_usage
    
    log_message "Database monitoring check completed"
}

# Run monitoring
run_monitoring

# Unset password environment variable
unset PGPASSWORD
EOF

chmod +x /usr/local/bin/midas-db-monitor.sh

print_status "Database monitoring script created"

# Step 4: Create Performance Report Script
echo -e "\n${BLUE}📈 Step 4: Creating Performance Report Script${NC}"
echo "------------------------------------------------------------"

cat > /usr/local/bin/midas-db-report.sh << 'EOF'
#!/bin/bash

# =====================================================
# MIDAS TECHNICAL DATABASE PERFORMANCE REPORT
# =====================================================

# Read database credentials
if [ ! -f /root/database_credentials.txt ]; then
    echo "ERROR: Database credentials file not found"
    exit 1
fi

DB_NAME=$(grep "Database Name:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_USER=$(grep "Database User:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_PASSWORD=$(grep "Database Password:" /root/database_credentials.txt | cut -d: -f2 | xargs)

export PGPASSWORD="${DB_PASSWORD}"

echo "📊 MIDAS TECHNICAL DATABASE PERFORMANCE REPORT"
echo "============================================================"
echo "Generated: $(date)"
echo ""

echo "🗄️  DATABASE OVERVIEW:"
echo "------------------------------------------------------------"
psql -h localhost -U ${DB_USER} -d ${DB_NAME} -c "
SELECT 
    'Database Size' as metric,
    pg_size_pretty(pg_database_size('${DB_NAME}')) as value
UNION ALL
SELECT 
    'Active Connections',
    count(*)::text
FROM pg_stat_activity 
WHERE datname = '${DB_NAME}'
UNION ALL
SELECT 
    'Total Tables',
    count(*)::text
FROM pg_tables 
WHERE schemaname = 'public';
"

echo ""
echo "📊 TABLE SIZES:"
echo "------------------------------------------------------------"
psql -h localhost -U ${DB_USER} -d ${DB_NAME} -c "SELECT * FROM table_sizes LIMIT 10;"

echo ""
echo "⚡ SLOW QUERIES (>1000ms):"
echo "------------------------------------------------------------"
psql -h localhost -U ${DB_USER} -d ${DB_NAME} -c "SELECT query, calls, mean_time FROM slow_queries LIMIT 5;"

echo ""
echo "🔗 CONNECTION STATISTICS:"
echo "------------------------------------------------------------"
psql -h localhost -U ${DB_USER} -d ${DB_NAME} -c "SELECT * FROM connection_stats;"

echo ""
echo "💾 SYSTEM RESOURCES:"
echo "------------------------------------------------------------"
echo "Memory Usage:"
free -h

echo ""
echo "Disk Usage:"
df -h /var/lib/postgresql

echo ""
echo "CPU Load:"
uptime

unset PGPASSWORD
EOF

chmod +x /usr/local/bin/midas-db-report.sh

print_status "Performance report script created"

# Step 5: Set up Monitoring Cron Jobs
echo -e "\n${BLUE}⏰ Step 5: Setting up Monitoring Schedule${NC}"
echo "------------------------------------------------------------"

print_info "Configuring monitoring cron jobs..."

# Add monitoring cron jobs
(crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/midas-db-monitor.sh") | crontab -
(crontab -l 2>/dev/null; echo "0 6 * * * /usr/local/bin/midas-db-report.sh > /var/log/midas-db-daily-report.log") | crontab -

print_status "Monitoring scheduled:"
print_info "  • Health checks: Every 5 minutes"
print_info "  • Daily reports: 6:00 AM"

# Step 6: Create Log Rotation
echo -e "\n${BLUE}🔄 Step 6: Setting up Log Rotation${NC}"
echo "------------------------------------------------------------"

cat > /etc/logrotate.d/midas-db-monitoring << EOF
/var/log/midas-db-monitor.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}

/var/log/midas-db-daily-report.log {
    weekly
    rotate 12
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF

print_status "Log rotation configured"

# Step 7: Test Monitoring
echo -e "\n${BLUE}🧪 Step 7: Testing Monitoring System${NC}"
echo "------------------------------------------------------------"

print_info "Running test monitoring check..."

if /usr/local/bin/midas-db-monitor.sh; then
    print_status "Monitoring test completed successfully"
else
    print_error "Monitoring test failed"
    exit 1
fi

print_info "Generating test performance report..."
/usr/local/bin/midas-db-report.sh > /tmp/test-report.log

if [ -s /tmp/test-report.log ]; then
    print_status "Performance report generated successfully"
    rm -f /tmp/test-report.log
else
    print_error "Performance report generation failed"
fi

echo -e "\n${GREEN}🎉 DATABASE MONITORING SETUP COMPLETED!${NC}"
echo "============================================================"
echo -e "${YELLOW}📋 MONITORING CONFIGURATION SUMMARY:${NC}"
echo "• Health checks: Every 5 minutes"
echo "• Daily reports: 6:00 AM"
echo "• Log rotation: 30 days for monitoring, 12 weeks for reports"
echo "• Disk space alerts: 80% warning, 90% critical"
echo "• Connection alerts: 80% warning, 95% critical"
echo ""
echo -e "${BLUE}🛠️  MANAGEMENT COMMANDS:${NC}"
echo "• Manual health check: sudo /usr/local/bin/midas-db-monitor.sh"
echo "• Generate report: sudo /usr/local/bin/midas-db-report.sh"
echo "• View monitoring logs: sudo tail -f /var/log/midas-db-monitor.log"
echo "• View daily reports: sudo tail -f /var/log/midas-db-daily-report.log"
echo ""
echo -e "${YELLOW}📋 NEXT STEPS:${NC}"
echo "1. Configure notification endpoints in monitoring scripts"
echo "2. Set up external monitoring (optional)"
echo "3. Test alert notifications"
echo "4. Review and adjust monitoring thresholds"

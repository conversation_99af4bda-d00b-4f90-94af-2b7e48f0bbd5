#!/usr/bin/env node

/**
 * Production Testing and Validation Script
 * Comprehensive testing of all production systems and integrations
 */

const axios = require('axios');
const { Pool } = require('pg');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class ProductionTester {
  constructor() {
    this.baseUrl = process.env.NODE_ENV === 'production'
      ? (process.env.NEXT_PUBLIC_SITE_URL || 'https://midastechnical.com')
      : 'http://localhost:3001';
    this.testResults = {
      database: { status: 'pending', tests: [] },
      authentication: { status: 'pending', tests: [] },
      email: { status: 'pending', tests: [] },
      shipping: { status: 'pending', tests: [] },
      monitoring: { status: 'pending', tests: [] },
      orderFlow: { status: 'pending', tests: [] },
      performance: { status: 'pending', tests: [] }
    };
    this.overallStatus = 'pending';
  }

  /**
   * Run complete production test suite
   */
  async runProductionTests() {
    console.log('🚀 Starting Production Testing for Midas Technical Platform');
    console.log(`🌐 Testing URL: ${this.baseUrl}`);
    console.log('');

    try {
      // Run all test suites
      await this.testDatabaseConnectivity();
      await this.testAuthentication();
      await this.testEmailService();
      await this.testShippingIntegrations();
      await this.testMonitoringServices();
      await this.testOrderFulfillmentWorkflow();
      await this.testPerformanceMetrics();

      // Generate final report
      await this.generateTestReport();

      console.log('✅ Production testing completed!');
      return this.overallStatus === 'passed';

    } catch (error) {
      console.error('❌ Production testing failed:', error);
      this.overallStatus = 'failed';
      return false;
    }
  }

  /**
   * Test database connectivity and schema
   */
  async testDatabaseConnectivity() {
    console.log('🗄️ Testing Database Connectivity...');
    
    try {
      const client = await pool.connect();
      
      // Test basic connectivity
      await client.query('SELECT NOW()');
      this.addTestResult('database', 'Database Connection', 'passed', 'Successfully connected to database');

      // Test critical tables exist
      const tables = ['users', 'products', 'orders', 'categories', 'shipping_labels', 'email_logs'];
      for (const table of tables) {
        const result = await client.query(`SELECT COUNT(*) FROM ${table}`);
        this.addTestResult('database', `Table: ${table}`, 'passed', `Table exists with ${result.rows[0].count} records`);
      }

      // Test inventory value
      const inventoryResult = await client.query(`
        SELECT SUM(price * stock_quantity) as total_value 
        FROM products 
        WHERE is_active = true
      `);
      
      const totalValue = parseFloat(inventoryResult.rows[0].total_value || 0);
      if (totalValue > 30000) {
        this.addTestResult('database', 'Inventory Value', 'passed', `Total inventory: $${totalValue.toFixed(2)}`);
      } else {
        this.addTestResult('database', 'Inventory Value', 'warning', `Low inventory value: $${totalValue.toFixed(2)}`);
      }

      client.release();
      this.testResults.database.status = 'passed';

    } catch (error) {
      this.addTestResult('database', 'Database Connection', 'failed', error.message);
      this.testResults.database.status = 'failed';
    }
  }

  /**
   * Test authentication system
   */
  async testAuthentication() {
    console.log('🔐 Testing Authentication System...');

    try {
      // Test NextAuth configuration
      const authConfigTest = await this.makeRequest('GET', '/api/auth/providers');
      if (authConfigTest.status === 200) {
        this.addTestResult('authentication', 'NextAuth Configuration', 'passed', 'Auth providers configured');
      } else {
        this.addTestResult('authentication', 'NextAuth Configuration', 'failed', 'Auth providers not accessible');
      }

      // Test admin authentication middleware
      const adminTest = await this.makeRequest('GET', '/api/admin/fulfillment');
      if (adminTest.status === 401) {
        this.addTestResult('authentication', 'Admin Protection', 'passed', 'Admin routes properly protected');
      } else {
        this.addTestResult('authentication', 'Admin Protection', 'failed', 'Admin routes not protected');
      }

      // Test environment variables
      if (process.env.NEXTAUTH_SECRET && process.env.NEXTAUTH_SECRET.length >= 32) {
        this.addTestResult('authentication', 'NextAuth Secret', 'passed', 'Secure secret configured');
      } else {
        this.addTestResult('authentication', 'NextAuth Secret', 'failed', 'NextAuth secret not properly configured');
      }

      this.testResults.authentication.status = 'passed';

    } catch (error) {
      this.addTestResult('authentication', 'Authentication System', 'failed', error.message);
      this.testResults.authentication.status = 'failed';
    }
  }

  /**
   * Test email service configuration
   */
  async testEmailService() {
    console.log('📧 Testing Email Service...');

    try {
      // Test SendGrid configuration
      if (process.env.SENDGRID_API_KEY && process.env.SENDGRID_API_KEY.startsWith('SG.')) {
        this.addTestResult('email', 'SendGrid Configuration', 'passed', 'SendGrid API key configured');
      } else {
        this.addTestResult('email', 'SendGrid Configuration', 'warning', 'SendGrid API key not configured');
      }

      // Test email templates
      const templates = [
        'SENDGRID_ORDER_CONFIRMATION_TEMPLATE',
        'SENDGRID_SHIPPING_NOTIFICATION_TEMPLATE',
        'SENDGRID_DELIVERY_CONFIRMATION_TEMPLATE'
      ];

      for (const template of templates) {
        if (process.env[template]) {
          this.addTestResult('email', `Template: ${template}`, 'passed', 'Template ID configured');
        } else {
          this.addTestResult('email', `Template: ${template}`, 'warning', 'Template ID not configured');
        }
      }

      // Test email service initialization
      try {
        const { productionEmailService } = require('../lib/email-service-production.js');
        this.addTestResult('email', 'Email Service Initialization', 'passed', 'Email service loaded successfully');
      } catch (error) {
        this.addTestResult('email', 'Email Service Initialization', 'failed', error.message);
      }

      this.testResults.email.status = 'passed';

    } catch (error) {
      this.addTestResult('email', 'Email Service', 'failed', error.message);
      this.testResults.email.status = 'failed';
    }
  }

  /**
   * Test shipping integrations
   */
  async testShippingIntegrations() {
    console.log('🚚 Testing Shipping Integrations...');

    try {
      // Test carrier API configurations
      const carriers = [
        { name: 'UPS', keys: ['UPS_ACCESS_KEY', 'UPS_USERNAME', 'UPS_PASSWORD', 'UPS_ACCOUNT_NUMBER'] },
        { name: 'FedEx', keys: ['FEDEX_KEY', 'FEDEX_PASSWORD', 'FEDEX_ACCOUNT_NUMBER', 'FEDEX_METER_NUMBER'] },
        { name: 'USPS', keys: ['USPS_USER_ID', 'USPS_PASSWORD'] }
      ];

      for (const carrier of carriers) {
        const configuredKeys = carrier.keys.filter(key => process.env[key]);
        if (configuredKeys.length === carrier.keys.length) {
          this.addTestResult('shipping', `${carrier.name} Configuration`, 'passed', 'All API keys configured');
        } else {
          this.addTestResult('shipping', `${carrier.name} Configuration`, 'warning', 
            `Missing keys: ${carrier.keys.filter(key => !process.env[key]).join(', ')}`);
        }
      }

      // Test shipping service initialization
      try {
        const { shippingService } = require('../lib/shipping-service.js');
        this.addTestResult('shipping', 'Shipping Service Initialization', 'passed', 'Shipping service loaded successfully');
      } catch (error) {
        this.addTestResult('shipping', 'Shipping Service Initialization', 'failed', error.message);
      }

      // Test real shipping APIs flag
      if (process.env.USE_REAL_SHIPPING_APIS === 'true') {
        this.addTestResult('shipping', 'Real APIs Enabled', 'passed', 'Production shipping APIs enabled');
      } else {
        this.addTestResult('shipping', 'Real APIs Enabled', 'warning', 'Using mock shipping APIs');
      }

      this.testResults.shipping.status = 'passed';

    } catch (error) {
      this.addTestResult('shipping', 'Shipping Integrations', 'failed', error.message);
      this.testResults.shipping.status = 'failed';
    }
  }

  /**
   * Test monitoring services
   */
  async testMonitoringServices() {
    console.log('📊 Testing Monitoring Services...');

    try {
      // Test Sentry configuration
      if (process.env.SENTRY_DSN) {
        this.addTestResult('monitoring', 'Sentry Configuration', 'passed', 'Sentry DSN configured');
      } else {
        this.addTestResult('monitoring', 'Sentry Configuration', 'warning', 'Sentry DSN not configured');
      }

      // Test Google Analytics configuration
      if (process.env.NEXT_PUBLIC_GA_ID) {
        this.addTestResult('monitoring', 'Google Analytics', 'passed', 'GA4 measurement ID configured');
      } else {
        this.addTestResult('monitoring', 'Google Analytics', 'warning', 'GA4 not configured');
      }

      // Test monitoring service initialization
      try {
        const { monitoringService } = require('../lib/monitoring.js');
        this.addTestResult('monitoring', 'Monitoring Service', 'passed', 'Monitoring service loaded successfully');
      } catch (error) {
        this.addTestResult('monitoring', 'Monitoring Service', 'failed', error.message);
      }

      this.testResults.monitoring.status = 'passed';

    } catch (error) {
      this.addTestResult('monitoring', 'Monitoring Services', 'failed', error.message);
      this.testResults.monitoring.status = 'failed';
    }
  }

  /**
   * Test complete order fulfillment workflow
   */
  async testOrderFulfillmentWorkflow() {
    console.log('📦 Testing Order Fulfillment Workflow...');

    try {
      // Test product API
      const productsTest = await this.makeRequest('GET', '/api/products?limit=5');
      if (productsTest.status === 200 && productsTest.data.data.products.length > 0) {
        this.addTestResult('orderFlow', 'Product API', 'passed', `${productsTest.data.data.products.length} products available`);
      } else {
        this.addTestResult('orderFlow', 'Product API', 'failed', 'No products available');
      }

      // Test categories API
      const categoriesTest = await this.makeRequest('GET', '/api/categories');
      if (categoriesTest.status === 200) {
        this.addTestResult('orderFlow', 'Categories API', 'passed', 'Categories API working');
      } else {
        this.addTestResult('orderFlow', 'Categories API', 'failed', 'Categories API not working');
      }

      // Test order tracking API
      const trackingTest = await this.makeRequest('GET', '/api/orders/track?orderNumber=TEST-001');
      if (trackingTest.status === 200 || trackingTest.status === 404) {
        this.addTestResult('orderFlow', 'Order Tracking API', 'passed', 'Order tracking API accessible');
      } else {
        this.addTestResult('orderFlow', 'Order Tracking API', 'failed', 'Order tracking API not working');
      }

      // Test fulfillment database tables
      const client = await pool.connect();
      const fulfillmentTables = ['shipping_labels', 'order_status_history', 'fulfillment_queue'];
      for (const table of fulfillmentTables) {
        try {
          await client.query(`SELECT COUNT(*) FROM ${table}`);
          this.addTestResult('orderFlow', `Fulfillment Table: ${table}`, 'passed', 'Table exists and accessible');
        } catch (error) {
          this.addTestResult('orderFlow', `Fulfillment Table: ${table}`, 'failed', error.message);
        }
      }
      client.release();

      this.testResults.orderFlow.status = 'passed';

    } catch (error) {
      this.addTestResult('orderFlow', 'Order Fulfillment Workflow', 'failed', error.message);
      this.testResults.orderFlow.status = 'failed';
    }
  }

  /**
   * Test performance metrics
   */
  async testPerformanceMetrics() {
    console.log('⚡ Testing Performance Metrics...');

    try {
      // Test homepage load time
      const startTime = Date.now();
      const homepageTest = await this.makeRequest('GET', '/');
      const loadTime = Date.now() - startTime;

      if (loadTime < 3000) {
        this.addTestResult('performance', 'Homepage Load Time', 'passed', `${loadTime}ms (< 3s)`);
      } else {
        this.addTestResult('performance', 'Homepage Load Time', 'warning', `${loadTime}ms (> 3s)`);
      }

      // Test API response times
      const apiStartTime = Date.now();
      await this.makeRequest('GET', '/api/products?limit=10');
      const apiLoadTime = Date.now() - apiStartTime;

      if (apiLoadTime < 1000) {
        this.addTestResult('performance', 'API Response Time', 'passed', `${apiLoadTime}ms (< 1s)`);
      } else {
        this.addTestResult('performance', 'API Response Time', 'warning', `${apiLoadTime}ms (> 1s)`);
      }

      // Test database query performance
      const client = await pool.connect();
      const dbStartTime = Date.now();
      await client.query('SELECT COUNT(*) FROM products WHERE is_active = true');
      const dbQueryTime = Date.now() - dbStartTime;
      client.release();

      if (dbQueryTime < 100) {
        this.addTestResult('performance', 'Database Query Time', 'passed', `${dbQueryTime}ms (< 100ms)`);
      } else {
        this.addTestResult('performance', 'Database Query Time', 'warning', `${dbQueryTime}ms (> 100ms)`);
      }

      this.testResults.performance.status = 'passed';

    } catch (error) {
      this.addTestResult('performance', 'Performance Metrics', 'failed', error.message);
      this.testResults.performance.status = 'failed';
    }
  }

  /**
   * Make HTTP request with error handling
   */
  async makeRequest(method, path, data = null) {
    try {
      const config = {
        method,
        url: `${this.baseUrl}${path}`,
        timeout: 10000,
        validateStatus: () => true // Don't throw on any status code
      };

      if (data) {
        config.data = data;
        config.headers = { 'Content-Type': 'application/json' };
      }

      return await axios(config);
    } catch (error) {
      return { status: 0, error: error.message };
    }
  }

  /**
   * Add test result
   */
  addTestResult(category, testName, status, message) {
    this.testResults[category].tests.push({
      name: testName,
      status,
      message,
      timestamp: new Date().toISOString()
    });

    const icon = status === 'passed' ? '✅' : status === 'warning' ? '⚠️' : '❌';
    console.log(`   ${icon} ${testName}: ${message}`);
  }

  /**
   * Generate comprehensive test report
   */
  async generateTestReport() {
    console.log('\n📋 PRODUCTION TEST REPORT');
    console.log('='.repeat(60));

    let totalTests = 0;
    let passedTests = 0;
    let warningTests = 0;
    let failedTests = 0;

    // Calculate overall statistics
    Object.values(this.testResults).forEach(category => {
      category.tests.forEach(test => {
        totalTests++;
        if (test.status === 'passed') passedTests++;
        else if (test.status === 'warning') warningTests++;
        else if (test.status === 'failed') failedTests++;
      });
    });

    // Determine overall status
    if (failedTests === 0 && warningTests === 0) {
      this.overallStatus = 'passed';
    } else if (failedTests === 0) {
      this.overallStatus = 'warning';
    } else {
      this.overallStatus = 'failed';
    }

    // Display summary
    console.log(`Overall Status: ${this.overallStatus.toUpperCase()}`);
    console.log(`Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`⚠️ Warnings: ${warningTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log('');

    // Display category results
    Object.entries(this.testResults).forEach(([category, results]) => {
      const categoryIcon = results.status === 'passed' ? '✅' : results.status === 'warning' ? '⚠️' : '❌';
      console.log(`${categoryIcon} ${category.toUpperCase()}: ${results.status}`);
      
      if (results.tests.some(test => test.status !== 'passed')) {
        results.tests.forEach(test => {
          if (test.status !== 'passed') {
            const testIcon = test.status === 'warning' ? '⚠️' : '❌';
            console.log(`   ${testIcon} ${test.name}: ${test.message}`);
          }
        });
      }
    });

    // Save report to database
    await this.saveTestReport();

    // Production readiness assessment
    console.log('\n🎯 PRODUCTION READINESS ASSESSMENT');
    console.log('='.repeat(60));
    
    if (this.overallStatus === 'passed') {
      console.log('🎉 SYSTEM IS PRODUCTION READY!');
      console.log('✅ All critical systems are operational');
      console.log('✅ All integrations are properly configured');
      console.log('✅ Performance metrics are within acceptable ranges');
    } else if (this.overallStatus === 'warning') {
      console.log('⚠️ SYSTEM IS MOSTLY READY WITH WARNINGS');
      console.log('✅ Critical systems are operational');
      console.log('⚠️ Some optional features need configuration');
      console.log('💡 Review warnings before production deployment');
    } else {
      console.log('❌ SYSTEM IS NOT READY FOR PRODUCTION');
      console.log('❌ Critical failures detected');
      console.log('🔧 Fix all failed tests before deployment');
    }
  }

  /**
   * Save test report to database
   */
  async saveTestReport() {
    try {
      const client = await pool.connect();
      
      await client.query(`
        CREATE TABLE IF NOT EXISTS production_test_reports (
          id SERIAL PRIMARY KEY,
          overall_status VARCHAR(20) NOT NULL,
          total_tests INTEGER NOT NULL,
          passed_tests INTEGER NOT NULL,
          warning_tests INTEGER NOT NULL,
          failed_tests INTEGER NOT NULL,
          test_results JSONB NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
      `);

      const totalTests = Object.values(this.testResults).reduce((sum, cat) => sum + cat.tests.length, 0);
      const passedTests = Object.values(this.testResults).reduce((sum, cat) => 
        sum + cat.tests.filter(t => t.status === 'passed').length, 0);
      const warningTests = Object.values(this.testResults).reduce((sum, cat) => 
        sum + cat.tests.filter(t => t.status === 'warning').length, 0);
      const failedTests = Object.values(this.testResults).reduce((sum, cat) => 
        sum + cat.tests.filter(t => t.status === 'failed').length, 0);

      await client.query(`
        INSERT INTO production_test_reports (
          overall_status, total_tests, passed_tests, warning_tests, failed_tests, test_results
        ) VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        this.overallStatus,
        totalTests,
        passedTests,
        warningTests,
        failedTests,
        JSON.stringify(this.testResults)
      ]);

      client.release();
      console.log('\n✅ Test report saved to database');

    } catch (error) {
      console.error('\n❌ Failed to save test report:', error.message);
    }
  }
}

// Run production tests if called directly
if (require.main === module) {
  const tester = new ProductionTester();
  
  tester.runProductionTests()
    .then((success) => {
      if (success) {
        console.log('\n🎉 Production testing completed successfully!');
        process.exit(0);
      } else {
        console.log('\n💥 Production testing failed!');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Production testing crashed:', error);
      process.exit(1);
    });
}

module.exports = ProductionTester;

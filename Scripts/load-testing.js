#!/usr/bin/env node

/**
 * Load Testing Script for Midas Technical E-commerce Platform
 * Tests the complete order flow under high-volume traffic
 */

const axios = require('axios');
const { Pool } = require('pg');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

class LoadTester {
  constructor() {
    this.baseUrl = process.env.LOAD_TEST_URL || 'http://localhost:3001';
    this.concurrentUsers = parseInt(process.env.LOAD_TEST_USERS) || 50;
    this.testDuration = parseInt(process.env.LOAD_TEST_DURATION) || 300; // 5 minutes
    this.results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity,
      responseTimes: [],
      errors: [],
      throughput: 0
    };
  }

  /**
   * Run complete load test suite
   */
  async runLoadTest() {
    console.log('🚀 Starting Load Test for Midas Technical E-commerce Platform');
    console.log(`📊 Configuration:`);
    console.log(`   - Base URL: ${this.baseUrl}`);
    console.log(`   - Concurrent Users: ${this.concurrentUsers}`);
    console.log(`   - Test Duration: ${this.testDuration} seconds`);
    console.log(`   - Target: Complete order flow simulation`);
    console.log('');

    try {
      // Pre-test setup
      await this.setupTestData();
      
      // Run load tests
      const startTime = Date.now();
      
      await Promise.all([
        this.testProductBrowsing(),
        this.testProductSearch(),
        this.testOrderFlow(),
        this.testAPIEndpoints(),
        this.testOrderTracking()
      ]);
      
      const endTime = Date.now();
      const totalDuration = (endTime - startTime) / 1000;
      
      // Calculate final metrics
      this.calculateMetrics(totalDuration);
      
      // Generate report
      await this.generateReport();
      
      console.log('✅ Load test completed successfully!');
      
    } catch (error) {
      console.error('❌ Load test failed:', error);
      throw error;
    }
  }

  /**
   * Set up test data
   */
  async setupTestData() {
    console.log('📋 Setting up test data...');
    
    try {
      const client = await pool.connect();
      
      // Ensure we have test products
      const productCount = await client.query('SELECT COUNT(*) FROM products WHERE is_active = true');
      if (parseInt(productCount.rows[0].count) < 10) {
        console.warn('⚠️ Not enough test products. Run seed script first.');
      }
      
      // Create test user for authenticated requests
      await client.query(`
        INSERT INTO users (email, first_name, last_name, email_verified, created_at)
        VALUES ('<EMAIL>', 'Load', 'Test', true, CURRENT_TIMESTAMP)
        ON CONFLICT (email) DO NOTHING
      `);
      
      client.release();
      console.log('✅ Test data setup complete');
      
    } catch (error) {
      console.error('❌ Test data setup failed:', error);
      throw error;
    }
  }

  /**
   * Test product browsing performance
   */
  async testProductBrowsing() {
    console.log('🛍️ Testing product browsing...');
    
    const promises = [];
    for (let i = 0; i < this.concurrentUsers; i++) {
      promises.push(this.simulateProductBrowsing(i));
    }
    
    await Promise.all(promises);
  }

  /**
   * Simulate product browsing for a single user
   */
  async simulateProductBrowsing(userId) {
    const endTime = Date.now() + (this.testDuration * 1000);
    
    while (Date.now() < endTime) {
      try {
        // Browse homepage
        await this.makeRequest('GET', '/');
        
        // Browse products page
        await this.makeRequest('GET', '/products');
        
        // Browse specific category
        await this.makeRequest('GET', '/api/products?category=phone-parts');
        
        // View product details
        await this.makeRequest('GET', '/api/products?limit=1');
        
        // Random delay between 1-3 seconds
        await this.sleep(1000 + Math.random() * 2000);
        
      } catch (error) {
        this.results.errors.push({
          type: 'product_browsing',
          error: error.message,
          userId
        });
      }
    }
  }

  /**
   * Test product search performance
   */
  async testProductSearch() {
    console.log('🔍 Testing product search...');
    
    const searchTerms = ['iPhone', 'Samsung', 'battery', 'screen', 'repair', 'cable'];
    const promises = [];
    
    for (let i = 0; i < Math.floor(this.concurrentUsers / 2); i++) {
      promises.push(this.simulateProductSearch(i, searchTerms));
    }
    
    await Promise.all(promises);
  }

  /**
   * Simulate product search for a single user
   */
  async simulateProductSearch(userId, searchTerms) {
    const endTime = Date.now() + (this.testDuration * 1000);
    
    while (Date.now() < endTime) {
      try {
        const searchTerm = searchTerms[Math.floor(Math.random() * searchTerms.length)];
        
        // Search products
        await this.makeRequest('GET', `/api/products?search=${encodeURIComponent(searchTerm)}`);
        
        // Search with filters
        await this.makeRequest('GET', `/api/products?search=${encodeURIComponent(searchTerm)}&sort=price&order=ASC`);
        
        await this.sleep(2000 + Math.random() * 3000);
        
      } catch (error) {
        this.results.errors.push({
          type: 'product_search',
          error: error.message,
          userId
        });
      }
    }
  }

  /**
   * Test complete order flow
   */
  async testOrderFlow() {
    console.log('🛒 Testing order flow...');
    
    const promises = [];
    for (let i = 0; i < Math.floor(this.concurrentUsers / 4); i++) {
      promises.push(this.simulateOrderFlow(i));
    }
    
    await Promise.all(promises);
  }

  /**
   * Simulate complete order flow for a single user
   */
  async simulateOrderFlow(userId) {
    const endTime = Date.now() + (this.testDuration * 1000);
    
    while (Date.now() < endTime) {
      try {
        // Get products for cart
        const productsResponse = await this.makeRequest('GET', '/api/products?limit=3');
        const products = productsResponse.data.data.products;
        
        if (products.length === 0) {
          await this.sleep(5000);
          continue;
        }
        
        // Simulate adding to cart (client-side operation)
        const cartItems = products.slice(0, 2).map(product => ({
          productId: product.id,
          quantity: Math.floor(Math.random() * 3) + 1,
          price: product.price
        }));
        
        // Calculate cart total
        const cartTotal = cartItems.reduce((total, item) => 
          total + (parseFloat(item.price) * item.quantity), 0
        );
        
        // Simulate checkout process (would normally create Stripe session)
        console.log(`💰 Simulated order: $${cartTotal.toFixed(2)} (User ${userId})`);
        
        await this.sleep(10000 + Math.random() * 5000);
        
      } catch (error) {
        this.results.errors.push({
          type: 'order_flow',
          error: error.message,
          userId
        });
      }
    }
  }

  /**
   * Test API endpoints performance
   */
  async testAPIEndpoints() {
    console.log('🔌 Testing API endpoints...');
    
    const endpoints = [
      { method: 'GET', path: '/api/products' },
      { method: 'GET', path: '/api/categories' },
      { method: 'GET', path: '/api/orders/track?orderNumber=TEST-001' }
    ];
    
    const promises = [];
    for (let i = 0; i < Math.floor(this.concurrentUsers / 3); i++) {
      promises.push(this.simulateAPIRequests(i, endpoints));
    }
    
    await Promise.all(promises);
  }

  /**
   * Simulate API requests for a single user
   */
  async simulateAPIRequests(userId, endpoints) {
    const endTime = Date.now() + (this.testDuration * 1000);
    
    while (Date.now() < endTime) {
      try {
        const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
        await this.makeRequest(endpoint.method, endpoint.path);
        
        await this.sleep(1000 + Math.random() * 2000);
        
      } catch (error) {
        this.results.errors.push({
          type: 'api_requests',
          error: error.message,
          userId
        });
      }
    }
  }

  /**
   * Test order tracking performance
   */
  async testOrderTracking() {
    console.log('📦 Testing order tracking...');
    
    const promises = [];
    for (let i = 0; i < Math.floor(this.concurrentUsers / 5); i++) {
      promises.push(this.simulateOrderTracking(i));
    }
    
    await Promise.all(promises);
  }

  /**
   * Simulate order tracking for a single user
   */
  async simulateOrderTracking(userId) {
    const endTime = Date.now() + (this.testDuration * 1000);
    
    while (Date.now() < endTime) {
      try {
        // Test tracking page
        await this.makeRequest('GET', '/track');
        
        // Test tracking API with mock data
        await this.makeRequest('GET', '/api/orders/track?orderNumber=MDT-2024-001');
        
        await this.sleep(5000 + Math.random() * 5000);
        
      } catch (error) {
        this.results.errors.push({
          type: 'order_tracking',
          error: error.message,
          userId
        });
      }
    }
  }

  /**
   * Make HTTP request and track performance
   */
  async makeRequest(method, path, data = null) {
    const startTime = Date.now();
    
    try {
      const config = {
        method,
        url: `${this.baseUrl}${path}`,
        timeout: 30000, // 30 second timeout
        headers: {
          'User-Agent': 'LoadTest/1.0'
        }
      };
      
      if (data) {
        config.data = data;
        config.headers['Content-Type'] = 'application/json';
      }
      
      const response = await axios(config);
      const responseTime = Date.now() - startTime;
      
      this.results.totalRequests++;
      this.results.successfulRequests++;
      this.results.responseTimes.push(responseTime);
      
      if (responseTime > this.results.maxResponseTime) {
        this.results.maxResponseTime = responseTime;
      }
      
      if (responseTime < this.results.minResponseTime) {
        this.results.minResponseTime = responseTime;
      }
      
      return response;
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      this.results.totalRequests++;
      this.results.failedRequests++;
      this.results.responseTimes.push(responseTime);
      
      throw error;
    }
  }

  /**
   * Calculate final metrics
   */
  calculateMetrics(totalDuration) {
    if (this.results.responseTimes.length > 0) {
      this.results.averageResponseTime = 
        this.results.responseTimes.reduce((a, b) => a + b, 0) / this.results.responseTimes.length;
    }
    
    this.results.throughput = this.results.totalRequests / totalDuration;
    
    // Calculate percentiles
    const sortedTimes = this.results.responseTimes.sort((a, b) => a - b);
    this.results.p50 = this.getPercentile(sortedTimes, 50);
    this.results.p95 = this.getPercentile(sortedTimes, 95);
    this.results.p99 = this.getPercentile(sortedTimes, 99);
  }

  /**
   * Get percentile value
   */
  getPercentile(sortedArray, percentile) {
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[index] || 0;
  }

  /**
   * Generate load test report
   */
  async generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      configuration: {
        baseUrl: this.baseUrl,
        concurrentUsers: this.concurrentUsers,
        testDuration: this.testDuration
      },
      results: this.results,
      recommendations: this.generateRecommendations()
    };
    
    console.log('\n📊 LOAD TEST RESULTS');
    console.log('='.repeat(50));
    console.log(`Total Requests: ${this.results.totalRequests}`);
    console.log(`Successful Requests: ${this.results.successfulRequests}`);
    console.log(`Failed Requests: ${this.results.failedRequests}`);
    console.log(`Success Rate: ${((this.results.successfulRequests / this.results.totalRequests) * 100).toFixed(2)}%`);
    console.log(`Average Response Time: ${this.results.averageResponseTime.toFixed(2)}ms`);
    console.log(`Min Response Time: ${this.results.minResponseTime}ms`);
    console.log(`Max Response Time: ${this.results.maxResponseTime}ms`);
    console.log(`50th Percentile: ${this.results.p50}ms`);
    console.log(`95th Percentile: ${this.results.p95}ms`);
    console.log(`99th Percentile: ${this.results.p99}ms`);
    console.log(`Throughput: ${this.results.throughput.toFixed(2)} requests/second`);
    console.log(`Total Errors: ${this.results.errors.length}`);
    
    if (this.results.errors.length > 0) {
      console.log('\n❌ ERROR SUMMARY:');
      const errorTypes = {};
      this.results.errors.forEach(error => {
        errorTypes[error.type] = (errorTypes[error.type] || 0) + 1;
      });
      
      Object.entries(errorTypes).forEach(([type, count]) => {
        console.log(`   ${type}: ${count} errors`);
      });
    }
    
    console.log('\n💡 RECOMMENDATIONS:');
    report.recommendations.forEach(rec => {
      console.log(`   - ${rec}`);
    });
    
    // Save report to database
    await this.saveReportToDatabase(report);
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations() {
    const recommendations = [];
    
    if (this.results.averageResponseTime > 3000) {
      recommendations.push('Average response time exceeds 3 seconds. Consider optimizing database queries and adding caching.');
    }
    
    if (this.results.p95 > 5000) {
      recommendations.push('95th percentile response time is high. Investigate slow queries and optimize critical paths.');
    }
    
    if ((this.results.failedRequests / this.results.totalRequests) > 0.01) {
      recommendations.push('Error rate exceeds 1%. Review error logs and improve error handling.');
    }
    
    if (this.results.throughput < 10) {
      recommendations.push('Low throughput detected. Consider scaling infrastructure and optimizing application performance.');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Performance looks good! System is handling load well.');
    }
    
    return recommendations;
  }

  /**
   * Save report to database
   */
  async saveReportToDatabase(report) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO performance_test_results (
          test_type, configuration, results, recommendations, created_at
        ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
      `, [
        'load_test',
        JSON.stringify(report.configuration),
        JSON.stringify(report.results),
        JSON.stringify(report.recommendations)
      ]);
      
      client.release();
      console.log('✅ Report saved to database');
      
    } catch (error) {
      console.error('❌ Failed to save report:', error);
    }
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run load test if called directly
if (require.main === module) {
  const loadTester = new LoadTester();
  
  loadTester.runLoadTest()
    .then(() => {
      console.log('🎉 Load test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Load test failed:', error);
      process.exit(1);
    });
}

module.exports = LoadTester;

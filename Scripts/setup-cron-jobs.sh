#!/bin/bash

# =====================================================
# MIDAS TECHNICAL CRON JOB SETUP SCRIPT
# Sets up automated processing for sales optimization
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/var/www/midastechnical.com"
NODE_PATH="/usr/bin/node"
LOG_DIR="/var/log/midastechnical"
USER="www-data"

echo -e "${BLUE}🤖 Setting up Midas Technical Cron Jobs${NC}"
echo -e "${BLUE}📁 Project Directory: ${PROJECT_DIR}${NC}"
echo ""

# =====================================================
# STEP 1: VERIFY ENVIRONMENT
# =====================================================
echo -e "${YELLOW}📋 Step 1: Verifying Environment${NC}"

# Check if project directory exists
if [ ! -d "$PROJECT_DIR" ]; then
    echo -e "${RED}❌ Project directory not found: $PROJECT_DIR${NC}"
    echo "Please update PROJECT_DIR in this script to match your deployment path"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js not found${NC}"
    echo "Please install Node.js before setting up cron jobs"
    exit 1
fi

# Check if the automation script exists
if [ ! -f "$PROJECT_DIR/scripts/process-sales-automation.js" ]; then
    echo -e "${RED}❌ Sales automation script not found${NC}"
    echo "Please ensure the project is properly deployed"
    exit 1
fi

echo -e "${GREEN}✅ Environment verification passed${NC}"

# =====================================================
# STEP 2: CREATE LOG DIRECTORY
# =====================================================
echo -e "${YELLOW}📁 Step 2: Setting up Log Directory${NC}"

# Create log directory if it doesn't exist
sudo mkdir -p "$LOG_DIR"
sudo chown "$USER:$USER" "$LOG_DIR"
sudo chmod 755 "$LOG_DIR"

echo -e "${GREEN}✅ Log directory created: $LOG_DIR${NC}"

# =====================================================
# STEP 3: CREATE CRON SCRIPTS
# =====================================================
echo -e "${YELLOW}🔧 Step 3: Creating Cron Scripts${NC}"

# Create sales automation script
cat > "$PROJECT_DIR/scripts/cron-sales-automation.sh" << EOF
#!/bin/bash

# Sales Automation Cron Script
# Runs every 15 minutes to process onboarding emails, cart recovery, and referrals

# Set environment
export NODE_ENV=production
export PATH=/usr/local/bin:/usr/bin:/bin

# Change to project directory
cd "$PROJECT_DIR"

# Load environment variables
if [ -f .env.local ]; then
    export \$(cat .env.local | grep -v '^#' | xargs)
fi

# Run sales automation with logging
echo "\$(date): Starting sales automation processing" >> "$LOG_DIR/sales-automation.log"

$NODE_PATH scripts/process-sales-automation.js >> "$LOG_DIR/sales-automation.log" 2>&1

if [ \$? -eq 0 ]; then
    echo "\$(date): Sales automation completed successfully" >> "$LOG_DIR/sales-automation.log"
else
    echo "\$(date): Sales automation failed with exit code \$?" >> "$LOG_DIR/sales-automation.log"
    
    # Send alert email if configured
    if [ ! -z "\$ADMIN_EMAIL" ]; then
        echo "Sales automation failed at \$(date). Check logs at $LOG_DIR/sales-automation.log" | \\
        mail -s "Midas Technical: Sales Automation Failed" "\$ADMIN_EMAIL" || true
    fi
fi

echo "" >> "$LOG_DIR/sales-automation.log"
EOF

# Create database cleanup script
cat > "$PROJECT_DIR/scripts/cron-database-cleanup.sh" << EOF
#!/bin/bash

# Database Cleanup Cron Script
# Runs daily to clean up old data and optimize performance

# Set environment
export NODE_ENV=production
export PATH=/usr/local/bin:/usr/bin:/bin

# Change to project directory
cd "$PROJECT_DIR"

# Load environment variables
if [ -f .env.local ]; then
    export \$(cat .env.local | grep -v '^#' | xargs)
fi

# Run database cleanup with logging
echo "\$(date): Starting database cleanup" >> "$LOG_DIR/database-cleanup.log"

# Clean up old analytics events (older than 90 days)
psql "\$DATABASE_URL" -c "DELETE FROM analytics_events WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days' AND event_name NOT IN ('user_registered', 'complete_purchase');" >> "$LOG_DIR/database-cleanup.log" 2>&1

# Clean up old email verification tokens (older than 7 days)
psql "\$DATABASE_URL" -c "DELETE FROM email_verification_tokens WHERE expires_at < CURRENT_TIMESTAMP - INTERVAL '7 days';" >> "$LOG_DIR/database-cleanup.log" 2>&1

# Clean up old error logs (older than 30 days)
psql "\$DATABASE_URL" -c "DELETE FROM error_logs WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days' AND severity NOT IN ('critical', 'error');" >> "$LOG_DIR/database-cleanup.log" 2>&1

# Vacuum and analyze tables for performance
psql "\$DATABASE_URL" -c "VACUUM ANALYZE;" >> "$LOG_DIR/database-cleanup.log" 2>&1

echo "\$(date): Database cleanup completed" >> "$LOG_DIR/database-cleanup.log"
echo "" >> "$LOG_DIR/database-cleanup.log"
EOF

# Create log rotation script
cat > "$PROJECT_DIR/scripts/cron-log-rotation.sh" << EOF
#!/bin/bash

# Log Rotation Cron Script
# Runs weekly to rotate and compress log files

# Set log directory
LOG_DIR="$LOG_DIR"

# Rotate sales automation logs
if [ -f "\$LOG_DIR/sales-automation.log" ]; then
    mv "\$LOG_DIR/sales-automation.log" "\$LOG_DIR/sales-automation.log.\$(date +%Y%m%d)"
    gzip "\$LOG_DIR/sales-automation.log.\$(date +%Y%m%d)"
    touch "\$LOG_DIR/sales-automation.log"
    chown $USER:$USER "\$LOG_DIR/sales-automation.log"
fi

# Rotate database cleanup logs
if [ -f "\$LOG_DIR/database-cleanup.log" ]; then
    mv "\$LOG_DIR/database-cleanup.log" "\$LOG_DIR/database-cleanup.log.\$(date +%Y%m%d)"
    gzip "\$LOG_DIR/database-cleanup.log.\$(date +%Y%m%d)"
    touch "\$LOG_DIR/database-cleanup.log"
    chown $USER:$USER "\$LOG_DIR/database-cleanup.log"
fi

# Remove logs older than 30 days
find "\$LOG_DIR" -name "*.gz" -mtime +30 -delete

echo "\$(date): Log rotation completed" >> "\$LOG_DIR/system.log"
EOF

# Make scripts executable
chmod +x "$PROJECT_DIR/scripts/cron-sales-automation.sh"
chmod +x "$PROJECT_DIR/scripts/cron-database-cleanup.sh"
chmod +x "$PROJECT_DIR/scripts/cron-log-rotation.sh"

echo -e "${GREEN}✅ Cron scripts created and made executable${NC}"

# =====================================================
# STEP 4: SETUP CRON JOBS
# =====================================================
echo -e "${YELLOW}⏰ Step 4: Setting up Cron Jobs${NC}"

# Create temporary crontab file
TEMP_CRON=$(mktemp)

# Get existing crontab (if any)
sudo -u "$USER" crontab -l 2>/dev/null > "$TEMP_CRON" || true

# Remove any existing Midas Technical cron jobs
sed -i '/# Midas Technical/d' "$TEMP_CRON"
sed -i '/process-sales-automation/d' "$TEMP_CRON"
sed -i '/cron-sales-automation/d' "$TEMP_CRON"
sed -i '/cron-database-cleanup/d' "$TEMP_CRON"
sed -i '/cron-log-rotation/d' "$TEMP_CRON"

# Add new cron jobs
cat >> "$TEMP_CRON" << EOF

# Midas Technical - Sales Automation (every 15 minutes)
*/15 * * * * $PROJECT_DIR/scripts/cron-sales-automation.sh

# Midas Technical - Database Cleanup (daily at 2 AM)
0 2 * * * $PROJECT_DIR/scripts/cron-database-cleanup.sh

# Midas Technical - Log Rotation (weekly on Sunday at 3 AM)
0 3 * * 0 $PROJECT_DIR/scripts/cron-log-rotation.sh

EOF

# Install the new crontab
sudo -u "$USER" crontab "$TEMP_CRON"

# Clean up temporary file
rm "$TEMP_CRON"

echo -e "${GREEN}✅ Cron jobs installed successfully${NC}"

# =====================================================
# STEP 5: CREATE MONITORING SCRIPT
# =====================================================
echo -e "${YELLOW}📊 Step 5: Creating Monitoring Script${NC}"

cat > "$PROJECT_DIR/scripts/monitor-automation.sh" << EOF
#!/bin/bash

# Automation Monitoring Script
# Checks the health and status of automated processes

LOG_DIR="$LOG_DIR"
PROJECT_DIR="$PROJECT_DIR"

echo "=== Midas Technical Automation Status ==="
echo "Generated: \$(date)"
echo ""

# Check if cron jobs are running
echo "📅 CRON JOB STATUS:"
sudo -u $USER crontab -l | grep "Midas Technical" -A 3 || echo "No Midas Technical cron jobs found"
echo ""

# Check recent sales automation runs
echo "🤖 SALES AUTOMATION (Last 5 runs):"
if [ -f "\$LOG_DIR/sales-automation.log" ]; then
    tail -n 50 "\$LOG_DIR/sales-automation.log" | grep "Starting sales automation" | tail -5
    echo ""
    echo "Last completion:"
    tail -n 50 "\$LOG_DIR/sales-automation.log" | grep "completed successfully" | tail -1
else
    echo "No sales automation logs found"
fi
echo ""

# Check database cleanup
echo "🗄️ DATABASE CLEANUP (Last run):"
if [ -f "\$LOG_DIR/database-cleanup.log" ]; then
    tail -n 20 "\$LOG_DIR/database-cleanup.log" | grep "Starting database cleanup" | tail -1
    tail -n 20 "\$LOG_DIR/database-cleanup.log" | grep "completed" | tail -1
else
    echo "No database cleanup logs found"
fi
echo ""

# Check disk space
echo "💾 DISK SPACE:"
df -h "\$PROJECT_DIR" | tail -1
df -h "\$LOG_DIR" | tail -1
echo ""

# Check recent errors
echo "❌ RECENT ERRORS:"
if [ -f "\$LOG_DIR/sales-automation.log" ]; then
    grep -i "error\|failed" "\$LOG_DIR/sales-automation.log" | tail -3 || echo "No recent errors found"
else
    echo "No error logs available"
fi
echo ""

# Check process status
echo "🔄 PROCESS STATUS:"
ps aux | grep "process-sales-automation" | grep -v grep || echo "No automation processes currently running"
echo ""

echo "=== End of Status Report ==="
EOF

chmod +x "$PROJECT_DIR/scripts/monitor-automation.sh"

echo -e "${GREEN}✅ Monitoring script created${NC}"

# =====================================================
# STEP 6: INITIAL TEST RUN
# =====================================================
echo -e "${YELLOW}🧪 Step 6: Running Initial Test${NC}"

echo "Testing sales automation script..."
cd "$PROJECT_DIR"

# Load environment variables
if [ -f .env.local ]; then
    export $(cat .env.local | grep -v '^#' | xargs)
fi

# Run a test of the automation script
echo "Running test automation..."
timeout 60 $NODE_PATH scripts/process-sales-automation.js || echo "Test completed (may have timed out)"

echo -e "${GREEN}✅ Initial test completed${NC}"

# =====================================================
# FINAL SUMMARY
# =====================================================
echo ""
echo -e "${GREEN}🎉 CRON JOB SETUP COMPLETED!${NC}"
echo ""
echo -e "${BLUE}📋 SUMMARY:${NC}"
echo -e "   ✅ Sales automation: Every 15 minutes"
echo -e "   ✅ Database cleanup: Daily at 2 AM"
echo -e "   ✅ Log rotation: Weekly on Sunday at 3 AM"
echo ""
echo -e "${BLUE}📁 LOG FILES:${NC}"
echo -e "   📄 Sales automation: $LOG_DIR/sales-automation.log"
echo -e "   📄 Database cleanup: $LOG_DIR/database-cleanup.log"
echo -e "   📄 System logs: $LOG_DIR/system.log"
echo ""
echo -e "${BLUE}🔧 MANAGEMENT COMMANDS:${NC}"
echo -e "   📊 Check status: $PROJECT_DIR/scripts/monitor-automation.sh"
echo -e "   📅 View crontab: sudo -u $USER crontab -l"
echo -e "   📄 View logs: tail -f $LOG_DIR/sales-automation.log"
echo ""
echo -e "${YELLOW}⚠️ IMPORTANT NOTES:${NC}"
echo -e "   • Ensure .env.local file has all required environment variables"
echo -e "   • Monitor logs regularly for any errors"
echo -e "   • Database cleanup runs daily to maintain performance"
echo -e "   • Log files are rotated weekly to prevent disk space issues"
echo ""
echo -e "${GREEN}The sales automation system is now running automatically! 🚀${NC}"

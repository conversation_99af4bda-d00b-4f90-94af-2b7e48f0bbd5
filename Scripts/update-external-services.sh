#!/bin/bash

# =====================================================
# MIDAS TECHNICAL EXTERNAL SERVICES UPDATE SCRIPT
# =====================================================
# This script helps update external service configurations for production domain
# Run after: bash Scripts/update-production-config.sh

set -e  # Exit on any error

# Configuration
DOMAIN="midastechnical.com"
PRODUCTION_URL="https://${DOMAIN}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔗 MIDAS TECHNICAL EXTERNAL SERVICES UPDATE${NC}"
echo "============================================================"
echo "Domain: ${DOMAIN}"
echo "Production URL: ${PRODUCTION_URL}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Step 1: Stripe Webhook Configuration
echo -e "\n${BLUE}💳 Step 1: Stripe Webhook Configuration${NC}"
echo "------------------------------------------------------------"

print_info "Stripe webhook endpoints that need to be updated:"
echo ""
echo "🔗 STRIPE WEBHOOK URLS TO UPDATE:"
echo "------------------------------------------------------------"
echo "Main webhook endpoint:"
echo "  ${PRODUCTION_URL}/api/webhooks/stripe"
echo ""
echo "Required webhook events:"
echo "  • checkout.session.completed"
echo "  • payment_intent.succeeded"
echo "  • payment_intent.payment_failed"
echo "  • invoice.payment_succeeded"
echo "  • customer.subscription.created"
echo "  • customer.subscription.updated"
echo "  • customer.subscription.deleted"
echo ""
echo "📋 MANUAL STEPS REQUIRED:"
echo "1. Log in to Stripe Dashboard (https://dashboard.stripe.com)"
echo "2. Go to Developers > Webhooks"
echo "3. Update existing webhook endpoint URL to: ${PRODUCTION_URL}/api/webhooks/stripe"
echo "4. Or create new webhook with the above URL"
echo "5. Copy the webhook signing secret to your .env.local file"
echo ""

# Step 2: Email Service Configuration
echo -e "\n${BLUE}📧 Step 2: Email Service Configuration${NC}"
echo "------------------------------------------------------------"

print_info "Updating email templates and configurations..."

# Create email template update script
cat > Scripts/update-email-templates.js << EOF
#!/usr/bin/env node

/**
 * Email Template Update Script
 * Updates email templates to use production domain
 */

const fs = require('fs');
const path = require('path');

const DOMAIN = '${DOMAIN}';
const PRODUCTION_URL = '${PRODUCTION_URL}';

// Email templates that need domain updates
const emailTemplates = [
  'lib/email-service.js',
  'lib/email-service-production.js',
  'pages/api/auth/[...nextauth].js'
];

console.log('🔄 Updating email templates...');

emailTemplates.forEach(templatePath => {
  if (fs.existsSync(templatePath)) {
    let content = fs.readFileSync(templatePath, 'utf8');
    
    // Replace localhost references
    content = content.replace(/localhost:3000/g, DOMAIN);
    content = content.replace(/http:\/\/\${DOMAIN}/g, PRODUCTION_URL);
    
    // Update email template URLs
    content = content.replace(
      /href="http:\/\/[^"]*"/g, 
      \`href="\${PRODUCTION_URL}"\`
    );
    
    fs.writeFileSync(templatePath, content);
    console.log(\`✅ Updated: \${templatePath}\`);
  }
});

console.log('📧 Email templates updated successfully!');
EOF

chmod +x Scripts/update-email-templates.js
node Scripts/update-email-templates.js

print_status "Email templates updated"

# Step 3: SendGrid Configuration
echo -e "\n${BLUE}📮 Step 3: SendGrid Configuration${NC}"
echo "------------------------------------------------------------"

print_info "SendGrid configuration updates needed:"
echo ""
echo "🔗 SENDGRID UPDATES REQUIRED:"
echo "------------------------------------------------------------"
echo "1. Domain Authentication:"
echo "   • Add ${DOMAIN} to SendGrid domain authentication"
echo "   • Configure DNS records for domain verification"
echo "   • Enable link branding for ${DOMAIN}"
echo ""
echo "2. Template Updates:"
echo "   • Update all dynamic templates to use ${PRODUCTION_URL}"
echo "   • Update logo URLs to use ${PRODUCTION_URL}/logo.png"
echo "   • Update footer links to use production domain"
echo ""
echo "3. Webhook Configuration:"
echo "   • Update webhook URL to: ${PRODUCTION_URL}/api/webhooks/sendgrid"
echo ""

# Step 4: Google Analytics Configuration
echo -e "\n${BLUE}📊 Step 4: Google Analytics Configuration${NC}"
echo "------------------------------------------------------------"

print_info "Google Analytics configuration updates:"
echo ""
echo "🔗 GOOGLE ANALYTICS UPDATES:"
echo "------------------------------------------------------------"
echo "1. Property Settings:"
echo "   • Update website URL to ${PRODUCTION_URL}"
echo "   • Add ${DOMAIN} to allowed domains"
echo "   • Configure enhanced ecommerce tracking"
echo ""
echo "2. Goals and Conversions:"
echo "   • Update goal URLs to use production domain"
echo "   • Configure ecommerce conversion tracking"
echo "   • Set up repair service conversion goals"
echo ""

# Step 5: Social Media and External Links
echo -e "\n${BLUE}🌐 Step 5: Social Media and External Links${NC}"
echo "------------------------------------------------------------"

print_info "Social media and external service updates needed:"
echo ""
echo "🔗 EXTERNAL SERVICES TO UPDATE:"
echo "------------------------------------------------------------"
echo "1. Social Media Profiles:"
echo "   • Facebook: Update website URL to ${PRODUCTION_URL}"
echo "   • Twitter: Update website URL in bio"
echo "   • LinkedIn: Update company website URL"
echo "   • Instagram: Update bio link"
echo ""
echo "2. Business Listings:"
echo "   • Google My Business: Update website URL"
echo "   • Yelp: Update business website"
echo "   • Better Business Bureau: Update website URL"
echo ""
echo "3. Payment Processors:"
echo "   • PayPal: Update return URLs and webhook endpoints"
echo "   • Square: Update webhook endpoints (if used)"
echo ""

# Step 6: API Documentation and External Integrations
echo -e "\n${BLUE}🔌 Step 6: API Documentation and Integrations${NC}"
echo "------------------------------------------------------------"

print_info "API and integration updates:"
echo ""
echo "🔗 API INTEGRATIONS TO UPDATE:"
echo "------------------------------------------------------------"
echo "1. Shipping Carriers:"
echo "   • UPS: Update callback URLs"
echo "   • FedEx: Update notification URLs"
echo "   • USPS: Update tracking webhook URLs"
echo ""
echo "2. Third-party Services:"
echo "   • Cloudinary: Update allowed domains"
echo "   • Sentry: Update allowed origins"
echo "   • Any CRM integrations: Update webhook URLs"
echo ""

# Step 7: Create Service Update Checklist
echo -e "\n${BLUE}📋 Step 7: Creating Service Update Checklist${NC}"
echo "------------------------------------------------------------"

cat > docs/EXTERNAL_SERVICES_CHECKLIST.md << EOF
# 🔗 EXTERNAL SERVICES UPDATE CHECKLIST

## 💳 Payment Services

### Stripe
- [ ] Update webhook endpoint: \`${PRODUCTION_URL}/api/webhooks/stripe\`
- [ ] Verify webhook events are configured
- [ ] Test payment flow with production domain
- [ ] Update return URLs in Stripe dashboard

### PayPal (if used)
- [ ] Update return URLs
- [ ] Update webhook endpoints
- [ ] Test payment integration

## 📧 Email Services

### SendGrid
- [ ] Add domain authentication for ${DOMAIN}
- [ ] Update all email templates with production URLs
- [ ] Configure webhook: \`${PRODUCTION_URL}/api/webhooks/sendgrid\`
- [ ] Test email delivery

### SMTP Configuration
- [ ] Verify SMTP settings for ${DOMAIN}
- [ ] Test email sending functionality
- [ ] Update email signatures and footers

## 📊 Analytics and Monitoring

### Google Analytics
- [ ] Update property URL to ${PRODUCTION_URL}
- [ ] Configure enhanced ecommerce tracking
- [ ] Set up conversion goals
- [ ] Test tracking implementation

### Sentry
- [ ] Update allowed origins to include ${DOMAIN}
- [ ] Test error reporting
- [ ] Configure release tracking

## 🚚 Shipping and Logistics

### UPS
- [ ] Update callback URLs
- [ ] Test shipping label generation
- [ ] Verify tracking integration

### FedEx
- [ ] Update notification URLs
- [ ] Test shipping integration
- [ ] Verify rate calculation

### USPS
- [ ] Update tracking webhook URLs
- [ ] Test shipping functionality

## 🌐 Social Media and Marketing

### Social Media Profiles
- [ ] Facebook: Update website URL
- [ ] Twitter: Update bio link
- [ ] LinkedIn: Update company website
- [ ] Instagram: Update bio link

### Business Listings
- [ ] Google My Business: Update website URL
- [ ] Yelp: Update business website
- [ ] Better Business Bureau: Update website

## 🔧 Technical Services

### DNS and Domain
- [ ] Verify DNS propagation
- [ ] Test SSL certificate
- [ ] Configure domain redirects

### CDN and Storage
- [ ] Cloudinary: Update allowed domains
- [ ] AWS S3: Update CORS configuration
- [ ] Test image delivery

## ✅ Validation Steps

### Functional Testing
- [ ] Test complete purchase flow
- [ ] Test repair service booking
- [ ] Test admin dashboard access
- [ ] Test email notifications
- [ ] Test payment processing
- [ ] Test shipping calculations

### Performance Testing
- [ ] SSL Labs test (target: A+ rating)
- [ ] Page speed test (target: <3 seconds)
- [ ] Mobile responsiveness test
- [ ] Cross-browser compatibility

### Security Testing
- [ ] Security headers validation
- [ ] HTTPS enforcement test
- [ ] API endpoint security test
- [ ] Authentication flow test

---

**📅 Completion Date:** ___________  
**Verified By:** ___________  
**Notes:** ___________
EOF

print_status "Created external services checklist"

# Step 8: Create Automated Testing Script
echo -e "\n${BLUE}🧪 Step 8: Creating Automated Testing Script${NC}"
echo "------------------------------------------------------------"

cat > Scripts/test-production-domain.sh << 'EOF'
#!/bin/bash

# =====================================================
# MIDAS TECHNICAL PRODUCTION DOMAIN TESTING SCRIPT
# =====================================================

DOMAIN="midastechnical.com"
PRODUCTION_URL="https://${DOMAIN}"

echo "🧪 TESTING PRODUCTION DOMAIN CONFIGURATION"
echo "============================================================"

# Test SSL certificate
echo "🔒 Testing SSL Certificate..."
SSL_RESULT=$(curl -s -I "${PRODUCTION_URL}" | head -n 1)
if echo "$SSL_RESULT" | grep -q "200"; then
    echo "✅ SSL certificate working"
else
    echo "❌ SSL certificate issue: $SSL_RESULT"
fi

# Test security headers
echo "🛡️  Testing Security Headers..."
HEADERS=$(curl -s -I "${PRODUCTION_URL}")

if echo "$HEADERS" | grep -q "Strict-Transport-Security"; then
    echo "✅ HSTS header present"
else
    echo "❌ HSTS header missing"
fi

if echo "$HEADERS" | grep -q "X-Frame-Options"; then
    echo "✅ X-Frame-Options header present"
else
    echo "❌ X-Frame-Options header missing"
fi

if echo "$HEADERS" | grep -q "X-Content-Type-Options"; then
    echo "✅ X-Content-Type-Options header present"
else
    echo "❌ X-Content-Type-Options header missing"
fi

# Test HTTP to HTTPS redirect
echo "🔄 Testing HTTP to HTTPS Redirect..."
REDIRECT_CODE=$(curl -s -o /dev/null -w "%{http_code}" "http://${DOMAIN}")
if [ "$REDIRECT_CODE" = "301" ]; then
    echo "✅ HTTP to HTTPS redirect working (301)"
else
    echo "❌ HTTP to HTTPS redirect not working (got $REDIRECT_CODE)"
fi

# Test key application endpoints
echo "🌐 Testing Application Endpoints..."
ENDPOINTS=("/" "/products" "/repair" "/admin" "/api/health")

for endpoint in "${ENDPOINTS[@]}"; do
    STATUS_CODE=$(curl -s -o /dev/null -w "%{http_code}" "${PRODUCTION_URL}${endpoint}")
    if [ "$STATUS_CODE" = "200" ]; then
        echo "✅ ${endpoint} responding (200)"
    else
        echo "❌ ${endpoint} not responding properly (${STATUS_CODE})"
    fi
done

# Test API endpoints
echo "🔌 Testing API Endpoints..."
API_ENDPOINTS=("/api/products" "/api/categories" "/api/repair/services")

for endpoint in "${API_ENDPOINTS[@]}"; do
    STATUS_CODE=$(curl -s -o /dev/null -w "%{http_code}" "${PRODUCTION_URL}${endpoint}")
    if [ "$STATUS_CODE" = "200" ]; then
        echo "✅ ${endpoint} responding (200)"
    else
        echo "❌ ${endpoint} not responding properly (${STATUS_CODE})"
    fi
done

# Test page load times
echo "⚡ Testing Page Load Times..."
LOAD_TIME=$(curl -o /dev/null -s -w "%{time_total}" "${PRODUCTION_URL}")
if (( $(echo "$LOAD_TIME < 3.0" | bc -l) )); then
    echo "✅ Page load time: ${LOAD_TIME}s (< 3s target)"
else
    echo "⚠️  Page load time: ${LOAD_TIME}s (> 3s target)"
fi

echo ""
echo "🎯 TESTING COMPLETE"
echo "============================================================"
echo "Review results above and address any issues before going live."
echo ""
echo "🔗 Additional Testing:"
echo "• SSL Labs: https://www.ssllabs.com/ssltest/analyze.html?d=${DOMAIN}"
echo "• PageSpeed: https://pagespeed.web.dev/report?url=${PRODUCTION_URL}"
echo "• Security Headers: https://securityheaders.com/?q=${PRODUCTION_URL}"
EOF

chmod +x Scripts/test-production-domain.sh

print_status "Created automated testing script"

echo -e "\n${GREEN}🎉 EXTERNAL SERVICES UPDATE GUIDE COMPLETED!${NC}"
echo "============================================================"
echo -e "${YELLOW}📋 SUMMARY OF UPDATES NEEDED:${NC}"
echo "• Stripe webhook URLs"
echo "• SendGrid domain authentication and templates"
echo "• Google Analytics property settings"
echo "• Social media profile links"
echo "• Business listing websites"
echo "• Shipping carrier callback URLs"
echo ""
echo -e "${BLUE}📚 DOCUMENTATION CREATED:${NC}"
echo "• External services checklist: docs/EXTERNAL_SERVICES_CHECKLIST.md"
echo "• Automated testing script: Scripts/test-production-domain.sh"
echo "• Email template updater: Scripts/update-email-templates.js"
echo ""
echo -e "${YELLOW}📋 NEXT STEPS:${NC}"
echo "1. Review and complete the external services checklist"
echo "2. Run automated tests: bash Scripts/test-production-domain.sh"
echo "3. Test SSL rating at SSL Labs"
echo "4. Verify all payment and email integrations"
echo "5. Complete functional testing of all features"

#!/bin/bash

# =====================================================
# MIDAS TECHNICAL PRODUCTION DATABASE VALIDATION SCRIPT
# =====================================================
# This script validates the production database setup
# Run after: bash Scripts/setup-database-monitoring.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 MIDAS TECHNICAL DATABASE VALIDATION${NC}"
echo "============================================================"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Initialize counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    print_info "Testing: $test_name"
    
    if eval "$test_command"; then
        if [ -n "$expected_result" ]; then
            local result=$(eval "$test_command")
            if [[ "$result" == *"$expected_result"* ]]; then
                print_status "$test_name: PASSED"
                PASSED_TESTS=$((PASSED_TESTS + 1))
                return 0
            else
                print_error "$test_name: FAILED (unexpected result: $result)"
                FAILED_TESTS=$((FAILED_TESTS + 1))
                return 1
            fi
        else
            print_status "$test_name: PASSED"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        fi
    else
        print_error "$test_name: FAILED"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Check if credentials file exists
if [ ! -f /root/database_credentials.txt ]; then
    print_error "Database credentials file not found. Run production-database-setup.sh first."
    exit 1
fi

# Extract database credentials
DB_NAME=$(grep "Database Name:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_USER=$(grep "Database User:" /root/database_credentials.txt | cut -d: -f2 | xargs)
DB_PASSWORD=$(grep "Database Password:" /root/database_credentials.txt | cut -d: -f2 | xargs)

export PGPASSWORD="${DB_PASSWORD}"

# Step 1: Basic Connectivity Tests
echo -e "\n${BLUE}🔌 Step 1: Database Connectivity Tests${NC}"
echo "------------------------------------------------------------"

run_test "PostgreSQL Service Status" "systemctl is-active postgresql" "active"
run_test "Database Connection" "psql -h localhost -U ${DB_USER} -d ${DB_NAME} -c 'SELECT 1;' > /dev/null 2>&1"
run_test "SSL Connection" "psql 'postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}?sslmode=require' -c 'SELECT 1;' > /dev/null 2>&1"

# Step 2: Schema Validation Tests
echo -e "\n${BLUE}📋 Step 2: Database Schema Validation${NC}"
echo "------------------------------------------------------------"

# Check required tables
REQUIRED_TABLES=(
    "users" "products" "categories" "orders" "order_items" "cart_items"
    "repair_tickets" "repair_services" "repair_service_categories" 
    "device_types" "repair_technicians" "repair_parts"
    "inventory_transactions" "stock_alerts" "webhook_logs" "payment_sessions"
)

for table in "${REQUIRED_TABLES[@]}"; do
    run_test "Table exists: $table" "psql -h localhost -U ${DB_USER} -d ${DB_NAME} -c '\dt $table' | grep -q '$table'"
done

# Step 3: Data Validation Tests
echo -e "\n${BLUE}📊 Step 3: Data Validation Tests${NC}"
echo "------------------------------------------------------------"

# Check product count
PRODUCT_COUNT=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT COUNT(*) FROM products WHERE is_active = true;" | xargs)
if [ "$PRODUCT_COUNT" -ge 500 ]; then
    print_status "Product count: $PRODUCT_COUNT (meets requirement ≥500)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Product count: $PRODUCT_COUNT (below target of 500)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Check category count
CATEGORY_COUNT=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT COUNT(*) FROM categories WHERE is_active = true;" | xargs)
if [ "$CATEGORY_COUNT" -ge 20 ]; then
    print_status "Category count: $CATEGORY_COUNT (meets requirement ≥20)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Category count: $CATEGORY_COUNT (below target of 20)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Check repair service categories
REPAIR_CATEGORIES=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT COUNT(*) FROM repair_service_categories;" | xargs)
if [ "$REPAIR_CATEGORIES" -ge 10 ]; then
    print_status "Repair categories: $REPAIR_CATEGORIES (meets requirement ≥10)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Repair categories: $REPAIR_CATEGORIES (below target of 10)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Check device types
DEVICE_TYPES=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SELECT COUNT(*) FROM device_types;" | xargs)
if [ "$DEVICE_TYPES" -ge 30 ]; then
    print_status "Device types: $DEVICE_TYPES (meets requirement ≥30)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Device types: $DEVICE_TYPES (below target of 30)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Step 4: Performance Configuration Tests
echo -e "\n${BLUE}⚡ Step 4: Performance Configuration Tests${NC}"
echo "------------------------------------------------------------"

# Check shared_buffers setting
SHARED_BUFFERS=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SHOW shared_buffers;" | xargs)
print_info "Shared buffers: $SHARED_BUFFERS"

# Check effective_cache_size setting
CACHE_SIZE=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SHOW effective_cache_size;" | xargs)
print_info "Effective cache size: $CACHE_SIZE"

# Check max_connections
MAX_CONNECTIONS=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SHOW max_connections;" | xargs)
if [ "$MAX_CONNECTIONS" -eq 100 ]; then
    print_status "Max connections: $MAX_CONNECTIONS (configured correctly)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Max connections: $MAX_CONNECTIONS (expected 100)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Check pg_stat_statements extension
run_test "pg_stat_statements extension" "psql -h localhost -U ${DB_USER} -d ${DB_NAME} -c '\dx pg_stat_statements' | grep -q 'pg_stat_statements'"

# Step 5: Security Configuration Tests
echo -e "\n${BLUE}🔒 Step 5: Security Configuration Tests${NC}"
echo "------------------------------------------------------------"

# Check SSL configuration
run_test "SSL enabled" "psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c 'SHOW ssl;' | grep -q 'on'"

# Check SSL minimum version
SSL_MIN_VERSION=$(psql -h localhost -U ${DB_USER} -d ${DB_NAME} -t -c "SHOW ssl_min_protocol_version;" | xargs)
if [[ "$SSL_MIN_VERSION" == "TLSv1.2" ]]; then
    print_status "SSL minimum version: $SSL_MIN_VERSION (secure)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "SSL minimum version: $SSL_MIN_VERSION (should be TLSv1.2)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Step 6: Backup System Tests
echo -e "\n${BLUE}💾 Step 6: Backup System Tests${NC}"
echo "------------------------------------------------------------"

run_test "Backup script exists" "[ -f /usr/local/bin/midas-db-backup.sh ]"
run_test "Backup script executable" "[ -x /usr/local/bin/midas-db-backup.sh ]"

# Check if backup cron job exists
if crontab -l | grep -q "midas-db-backup.sh"; then
    print_status "Backup cron job: Configured"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_error "Backup cron job: Not configured"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Step 7: Monitoring System Tests
echo -e "\n${BLUE}📊 Step 7: Monitoring System Tests${NC}"
echo "------------------------------------------------------------"

run_test "Monitoring script exists" "[ -f /usr/local/bin/midas-db-monitor.sh ]"
run_test "Monitoring script executable" "[ -x /usr/local/bin/midas-db-monitor.sh ]"
run_test "Report script exists" "[ -f /usr/local/bin/midas-db-report.sh ]"

# Check monitoring cron job
if crontab -l | grep -q "midas-db-monitor.sh"; then
    print_status "Monitoring cron job: Configured"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_error "Monitoring cron job: Not configured"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Step 8: Connection Pooling Tests
echo -e "\n${BLUE}🏊 Step 8: Connection Pooling Tests${NC}"
echo "------------------------------------------------------------"

run_test "PgBouncer service" "systemctl is-active pgbouncer" "active"
run_test "PgBouncer connection" "psql -h localhost -p 6432 -U ${DB_USER} -d ${DB_NAME} -c 'SELECT 1;' > /dev/null 2>&1"

# Step 9: System Resource Tests
echo -e "\n${BLUE}💻 Step 9: System Resource Tests${NC}"
echo "------------------------------------------------------------"

# Check RAM
TOTAL_RAM=$(free -g | awk '/^Mem:/{print $2}')
if [ "$TOTAL_RAM" -ge 4 ]; then
    print_status "RAM: ${TOTAL_RAM}GB (meets requirement ≥4GB)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_error "RAM: ${TOTAL_RAM}GB (below requirement of 4GB)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Check CPU cores
CPU_CORES=$(nproc)
if [ "$CPU_CORES" -ge 2 ]; then
    print_status "CPU cores: $CPU_CORES (meets requirement ≥2)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_error "CPU cores: $CPU_CORES (below requirement of 2)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Check disk space
DISK_SPACE=$(df -h /var/lib/postgresql | awk 'NR==2{print $4}' | sed 's/G//')
if [ "${DISK_SPACE%.*}" -ge 10 ]; then
    print_status "Disk space: ${DISK_SPACE}GB available"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Disk space: ${DISK_SPACE}GB (low space warning)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Final Report
echo -e "\n${BLUE}📋 VALIDATION SUMMARY${NC}"
echo "============================================================"

PASS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))

echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"
echo -e "Pass Rate: $PASS_RATE%"

if [ "$FAILED_TESTS" -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL TESTS PASSED! DATABASE IS PRODUCTION READY!${NC}"
    exit 0
elif [ "$PASS_RATE" -ge 90 ]; then
    echo -e "\n${YELLOW}⚠️  MOSTLY READY - Minor issues detected${NC}"
    echo -e "Review failed tests and address issues before production deployment"
    exit 1
else
    echo -e "\n${RED}❌ CRITICAL ISSUES DETECTED${NC}"
    echo -e "Address all failed tests before production deployment"
    exit 2
fi

# Cleanup
unset PGPASSWORD

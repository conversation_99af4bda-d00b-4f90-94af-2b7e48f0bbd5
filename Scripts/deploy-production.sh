#!/bin/bash

# =====================================================
# MIDAS TECHNICAL PRODUCTION DEPLOYMENT SCRIPT
# =====================================================
# This script prepares and deploys the midastechnical.com platform

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="Midas Technical E-commerce Platform"
DOMAIN="midastechnical.com"
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"

echo -e "${BLUE}🚀 Starting Production Deployment for ${PROJECT_NAME}${NC}"
echo -e "${BLUE}🌐 Target Domain: ${DOMAIN}${NC}"
echo ""

# =====================================================
# STEP 1: PRE-DEPLOYMENT CHECKS
# =====================================================
echo -e "${YELLOW}📋 Step 1: Pre-deployment Checks${NC}"

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo -e "${RED}❌ .env.local file not found${NC}"
    echo "Please create .env.local with production environment variables"
    exit 1
fi

# Check if NODE_ENV is set to production
if ! grep -q "NODE_ENV=production" .env.local; then
    echo -e "${YELLOW}⚠️ NODE_ENV is not set to production in .env.local${NC}"
    echo "Please update NODE_ENV=production for production deployment"
fi

# Check required environment variables
echo "🔍 Checking required environment variables..."
required_vars=(
    "DATABASE_URL"
    "NEXTAUTH_SECRET"
    "STRIPE_SECRET_KEY"
    "SENDGRID_API_KEY"
    "UPS_ACCESS_KEY"
    "FEDEX_KEY"
    "SENTRY_DSN"
    "NEXT_PUBLIC_GA_ID"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if ! grep -q "^${var}=" .env.local; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo -e "${RED}❌ Missing required environment variables:${NC}"
    printf '%s\n' "${missing_vars[@]}"
    echo "Please configure all required variables before deployment"
    exit 1
fi

echo -e "${GREEN}✅ Environment variables check passed${NC}"

# =====================================================
# STEP 2: DEPENDENCY INSTALLATION
# =====================================================
echo -e "${YELLOW}📦 Step 2: Installing Dependencies${NC}"

# Install production dependencies
echo "Installing Node.js dependencies..."
npm ci --production

# Install additional production packages
echo "Installing production-specific packages..."
npm install --save-prod @sentry/nextjs axios @sendgrid/mail redis bcrypt

echo -e "${GREEN}✅ Dependencies installed${NC}"

# =====================================================
# STEP 3: DATABASE PREPARATION
# =====================================================
echo -e "${YELLOW}🗄️ Step 3: Database Preparation${NC}"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Backup existing database (if exists)
echo "Creating database backup..."
if command -v pg_dump &> /dev/null; then
    pg_dump "$DATABASE_URL" > "$BACKUP_DIR/database_backup.sql" 2>/dev/null || echo "⚠️ Database backup failed (database may not exist yet)"
    echo -e "${GREEN}✅ Database backup created: $BACKUP_DIR/database_backup.sql${NC}"
else
    echo -e "${YELLOW}⚠️ pg_dump not found, skipping database backup${NC}"
fi

# Apply database schema
echo "Applying database schema..."
if command -v psql &> /dev/null; then
    psql "$DATABASE_URL" -f database/repair_system_schema.sql
    echo -e "${GREEN}✅ Database schema applied${NC}"
else
    echo -e "${RED}❌ psql not found, cannot apply database schema${NC}"
    exit 1
fi

# Run database optimizations
echo "Running database optimizations..."
node scripts/performance-optimization.js

echo -e "${GREEN}✅ Database preparation completed${NC}"

# =====================================================
# STEP 4: BUILD APPLICATION
# =====================================================
echo -e "${YELLOW}🔨 Step 4: Building Application${NC}"

# Clean previous builds
echo "Cleaning previous builds..."
rm -rf .next
rm -rf out

# Build the application
echo "Building Next.js application..."
npm run build

# Verify build success
if [ ! -d ".next" ]; then
    echo -e "${RED}❌ Build failed - .next directory not found${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Application built successfully${NC}"

# =====================================================
# STEP 5: SECURITY CONFIGURATION
# =====================================================
echo -e "${YELLOW}🔒 Step 5: Security Configuration${NC}"

# Generate secure secrets if needed
echo "Checking security configuration..."

# Check NextAuth secret strength
nextauth_secret=$(grep "NEXTAUTH_SECRET=" .env.local | cut -d'=' -f2)
if [ ${#nextauth_secret} -lt 32 ]; then
    echo -e "${YELLOW}⚠️ NEXTAUTH_SECRET should be at least 32 characters${NC}"
fi

# Set secure file permissions
echo "Setting secure file permissions..."
chmod 600 .env.local
chmod 755 scripts/*.sh
chmod 644 package.json

echo -e "${GREEN}✅ Security configuration completed${NC}"

# =====================================================
# STEP 6: TESTING
# =====================================================
echo -e "${YELLOW}🧪 Step 6: Running Production Tests${NC}"

# Run production tests
echo "Running production validation tests..."
node scripts/production-testing.js

# Check test results
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Production tests passed${NC}"
else
    echo -e "${RED}❌ Production tests failed${NC}"
    echo "Please fix all issues before deploying to production"
    exit 1
fi

# =====================================================
# STEP 7: SSL AND DOMAIN CONFIGURATION
# =====================================================
echo -e "${YELLOW}🔐 Step 7: SSL and Domain Configuration${NC}"

echo "SSL Certificate Configuration:"
echo "1. Ensure SSL certificate is installed for ${DOMAIN}"
echo "2. Configure auto-renewal for SSL certificates"
echo "3. Set up HTTPS redirects"
echo "4. Verify domain DNS settings"

# Create SSL configuration reminder
cat > "$BACKUP_DIR/ssl_checklist.txt" << EOF
SSL Configuration Checklist for ${DOMAIN}:

1. SSL Certificate Installation:
   - Install SSL certificate for ${DOMAIN}
   - Install SSL certificate for www.${DOMAIN}
   - Configure certificate auto-renewal

2. Web Server Configuration:
   - Enable HTTPS redirects (HTTP -> HTTPS)
   - Configure HSTS headers
   - Set up proper SSL ciphers

3. DNS Configuration:
   - Verify A record points to server IP
   - Set up CNAME for www subdomain
   - Configure CAA records for certificate authority

4. Security Headers:
   - Content Security Policy (CSP)
   - X-Frame-Options
   - X-Content-Type-Options
   - Strict-Transport-Security

5. Testing:
   - Test SSL configuration with SSL Labs
   - Verify all pages load over HTTPS
   - Test certificate auto-renewal
EOF

echo -e "${GREEN}✅ SSL checklist created: $BACKUP_DIR/ssl_checklist.txt${NC}"

# =====================================================
# STEP 8: MONITORING SETUP
# =====================================================
echo -e "${YELLOW}📊 Step 8: Monitoring Setup${NC}"

# Create monitoring configuration
echo "Setting up monitoring and alerts..."

# Create monitoring checklist
cat > "$BACKUP_DIR/monitoring_checklist.txt" << EOF
Production Monitoring Checklist:

1. Sentry Error Tracking:
   - Verify SENTRY_DSN is configured
   - Test error reporting
   - Set up alert rules

2. Google Analytics:
   - Verify GA4 tracking ID
   - Test e-commerce events
   - Set up conversion goals

3. Uptime Monitoring:
   - Set up external uptime monitoring
   - Configure downtime alerts
   - Test notification channels

4. Performance Monitoring:
   - Monitor page load times
   - Track API response times
   - Set up performance alerts

5. Business Metrics:
   - Order volume tracking
   - Revenue monitoring
   - Inventory alerts

6. System Health:
   - Database performance
   - Server resource usage
   - Application logs
EOF

echo -e "${GREEN}✅ Monitoring checklist created: $BACKUP_DIR/monitoring_checklist.txt${NC}"

# =====================================================
# STEP 9: DEPLOYMENT SUMMARY
# =====================================================
echo -e "${YELLOW}📋 Step 9: Deployment Summary${NC}"

# Create deployment summary
cat > "$BACKUP_DIR/deployment_summary.txt" << EOF
MIDAS TECHNICAL PRODUCTION DEPLOYMENT SUMMARY
=============================================

Deployment Date: $(date)
Domain: ${DOMAIN}
Project: ${PROJECT_NAME}

✅ COMPLETED STEPS:
1. Pre-deployment checks passed
2. Dependencies installed
3. Database schema applied
4. Application built successfully
5. Security configuration completed
6. Production tests passed
7. SSL configuration prepared
8. Monitoring setup prepared

📊 SYSTEM STATISTICS:
- Database Tables: $(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null || echo "N/A")
- Product Count: $(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM products WHERE is_active = true;" 2>/dev/null || echo "N/A")
- Total Inventory Value: \$$(psql "$DATABASE_URL" -t -c "SELECT ROUND(SUM(price * stock_quantity), 2) FROM products WHERE is_active = true;" 2>/dev/null || echo "N/A")

🔧 NEXT STEPS:
1. Deploy application to production server
2. Configure web server (Nginx/Apache)
3. Set up SSL certificates
4. Configure domain DNS
5. Test all functionality
6. Monitor system performance

📁 BACKUP LOCATION: $BACKUP_DIR

🚨 IMPORTANT REMINDERS:
- Test all payment flows with real transactions
- Verify all email notifications are working
- Test shipping label generation
- Monitor error rates and performance
- Set up automated backups
EOF

echo -e "${GREEN}✅ Deployment summary created: $BACKUP_DIR/deployment_summary.txt${NC}"

# =====================================================
# FINAL OUTPUT
# =====================================================
echo ""
echo -e "${GREEN}🎉 PRODUCTION DEPLOYMENT PREPARATION COMPLETED!${NC}"
echo ""
echo -e "${BLUE}📋 DEPLOYMENT SUMMARY:${NC}"
echo -e "   ✅ All pre-deployment checks passed"
echo -e "   ✅ Application built and tested"
echo -e "   ✅ Database schema applied"
echo -e "   ✅ Security configuration completed"
echo -e "   ✅ Monitoring prepared"
echo ""
echo -e "${YELLOW}📁 Backup and documentation: $BACKUP_DIR${NC}"
echo ""
echo -e "${BLUE}🚀 READY FOR PRODUCTION DEPLOYMENT!${NC}"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Deploy to production server"
echo "2. Configure SSL certificates"
echo "3. Set up domain DNS"
echo "4. Test all functionality"
echo "5. Monitor system performance"
echo ""
echo -e "${GREEN}The ${PROJECT_NAME} is ready for live deployment! 🎉${NC}"

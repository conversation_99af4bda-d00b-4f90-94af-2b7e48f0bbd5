-- Repair Desk System Database Schema
-- Comprehensive repair service management for midastechnical.com

-- Repair Service Categories Table
CREATE TABLE IF NOT EXISTS repair_service_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Services Table
CREATE TABLE IF NOT EXISTS repair_services (
    id SERIAL PRIMARY KEY,
    category_id INTEGER REFERENCES repair_service_categories(id),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    short_description VARCHAR(500),
    base_price DECIMAL(10, 2) NOT NULL,
    estimated_time_hours INTEGER DEFAULT 24, -- in hours
    difficulty_level VARCHAR(20) DEFAULT 'medium', -- 'easy', 'medium', 'hard', 'expert'
    warranty_days INTEGER DEFAULT 90,
    is_active BOOLEAN DEFAULT TRUE,
    requires_diagnosis BOOLEAN DEFAULT FALSE,
    diagnosis_fee DECIMAL(10, 2) DEFAULT 0.00,
    image_url VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Device Types Table
CREATE TABLE IF NOT EXISTS device_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    brand VARCHAR(100),
    model VARCHAR(100),
    category VARCHAR(50), -- 'phone', 'tablet', 'laptop', 'desktop', 'gaming', 'other'
    image_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Service Pricing Table (device-specific pricing)
CREATE TABLE IF NOT EXISTS repair_service_pricing (
    id SERIAL PRIMARY KEY,
    service_id INTEGER REFERENCES repair_services(id) ON DELETE CASCADE,
    device_type_id INTEGER REFERENCES device_types(id) ON DELETE CASCADE,
    price DECIMAL(10, 2) NOT NULL,
    estimated_time_hours INTEGER DEFAULT 24,
    parts_cost DECIMAL(10, 2) DEFAULT 0.00,
    labor_cost DECIMAL(10, 2) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(service_id, device_type_id)
);

-- Repair Tickets Table
CREATE TABLE IF NOT EXISTS repair_tickets (
    id SERIAL PRIMARY KEY,
    ticket_number VARCHAR(50) NOT NULL UNIQUE,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20),
    device_type_id INTEGER REFERENCES device_types(id),
    device_brand VARCHAR(100),
    device_model VARCHAR(100),
    device_serial VARCHAR(100),
    device_imei VARCHAR(50),
    device_condition TEXT,
    problem_description TEXT NOT NULL,
    requested_services INTEGER[], -- Array of service IDs
    status VARCHAR(20) NOT NULL DEFAULT 'submitted', 
    -- Status: 'submitted', 'received', 'diagnosed', 'approved', 'in_progress', 'testing', 'completed', 'ready_pickup', 'delivered', 'cancelled'
    priority VARCHAR(20) DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
    estimated_cost DECIMAL(10, 2),
    final_cost DECIMAL(10, 2),
    estimated_completion TIMESTAMP WITH TIME ZONE,
    actual_completion TIMESTAMP WITH TIME ZONE,
    technician_id INTEGER REFERENCES users(id),
    diagnosis_notes TEXT,
    repair_notes TEXT,
    customer_notes TEXT,
    internal_notes TEXT,
    warranty_expires TIMESTAMP WITH TIME ZONE,
    payment_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'partial', 'paid', 'refunded'
    payment_method VARCHAR(50),
    payment_id VARCHAR(100),
    is_warranty_repair BOOLEAN DEFAULT FALSE,
    original_ticket_id INTEGER REFERENCES repair_tickets(id), -- For warranty repairs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Ticket Services Table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS repair_ticket_services (
    id SERIAL PRIMARY KEY,
    ticket_id INTEGER REFERENCES repair_tickets(id) ON DELETE CASCADE,
    service_id INTEGER REFERENCES repair_services(id),
    pricing_id INTEGER REFERENCES repair_service_pricing(id),
    quantity INTEGER DEFAULT 1,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'in_progress', 'completed', 'cancelled'
    technician_notes TEXT,
    parts_used TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Ticket Status History Table
CREATE TABLE IF NOT EXISTS repair_ticket_status_history (
    id SERIAL PRIMARY KEY,
    ticket_id INTEGER REFERENCES repair_tickets(id) ON DELETE CASCADE,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    changed_by INTEGER REFERENCES users(id),
    notes TEXT,
    customer_notified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Ticket Files Table (for photos, documents, etc.)
CREATE TABLE IF NOT EXISTS repair_ticket_files (
    id SERIAL PRIMARY KEY,
    ticket_id INTEGER REFERENCES repair_tickets(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50), -- 'image', 'document', 'video'
    file_size INTEGER,
    uploaded_by INTEGER REFERENCES users(id),
    description TEXT,
    is_before_photo BOOLEAN DEFAULT FALSE,
    is_after_photo BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Technicians Table
CREATE TABLE IF NOT EXISTS repair_technicians (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    employee_id VARCHAR(50) UNIQUE,
    specializations TEXT[], -- Array of specialization areas
    skill_level VARCHAR(20) DEFAULT 'intermediate', -- 'beginner', 'intermediate', 'advanced', 'expert'
    hourly_rate DECIMAL(10, 2),
    is_active BOOLEAN DEFAULT TRUE,
    max_concurrent_tickets INTEGER DEFAULT 5,
    certifications TEXT,
    hire_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Parts Inventory Table
CREATE TABLE IF NOT EXISTS repair_parts (
    id SERIAL PRIMARY KEY,
    part_number VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    compatible_devices INTEGER[], -- Array of device_type IDs
    cost_price DECIMAL(10, 2) NOT NULL,
    selling_price DECIMAL(10, 2) NOT NULL,
    stock_quantity INTEGER DEFAULT 0,
    minimum_stock INTEGER DEFAULT 5,
    supplier VARCHAR(255),
    supplier_part_number VARCHAR(100),
    warranty_days INTEGER DEFAULT 90,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Ticket Parts Used Table
CREATE TABLE IF NOT EXISTS repair_ticket_parts (
    id SERIAL PRIMARY KEY,
    ticket_id INTEGER REFERENCES repair_tickets(id) ON DELETE CASCADE,
    part_id INTEGER REFERENCES repair_parts(id),
    quantity_used INTEGER NOT NULL,
    unit_cost DECIMAL(10, 2) NOT NULL,
    total_cost DECIMAL(10, 2) NOT NULL,
    used_by INTEGER REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Estimates Table
CREATE TABLE IF NOT EXISTS repair_estimates (
    id SERIAL PRIMARY KEY,
    ticket_id INTEGER REFERENCES repair_tickets(id) ON DELETE CASCADE,
    estimate_number VARCHAR(50) NOT NULL UNIQUE,
    labor_cost DECIMAL(10, 2) DEFAULT 0.00,
    parts_cost DECIMAL(10, 2) DEFAULT 0.00,
    additional_fees DECIMAL(10, 2) DEFAULT 0.00,
    discount_amount DECIMAL(10, 2) DEFAULT 0.00,
    tax_amount DECIMAL(10, 2) DEFAULT 0.00,
    total_amount DECIMAL(10, 2) NOT NULL,
    estimated_completion TIMESTAMP WITH TIME ZONE,
    valid_until TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'expired'
    customer_approved_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Warranty Claims Table
CREATE TABLE IF NOT EXISTS repair_warranty_claims (
    id SERIAL PRIMARY KEY,
    original_ticket_id INTEGER REFERENCES repair_tickets(id),
    claim_ticket_id INTEGER REFERENCES repair_tickets(id),
    claim_reason TEXT NOT NULL,
    claim_status VARCHAR(20) DEFAULT 'submitted', -- 'submitted', 'approved', 'rejected', 'completed'
    resolution_notes TEXT,
    processed_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Customer Communications Table
CREATE TABLE IF NOT EXISTS repair_communications (
    id SERIAL PRIMARY KEY,
    ticket_id INTEGER REFERENCES repair_tickets(id) ON DELETE CASCADE,
    communication_type VARCHAR(20) NOT NULL, -- 'email', 'sms', 'call', 'note'
    direction VARCHAR(10) NOT NULL, -- 'inbound', 'outbound'
    subject VARCHAR(255),
    message TEXT NOT NULL,
    sent_by INTEGER REFERENCES users(id),
    sent_to VARCHAR(255), -- email or phone
    delivery_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'failed'
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Repair Service Reviews Table
CREATE TABLE IF NOT EXISTS repair_service_reviews (
    id SERIAL PRIMARY KEY,
    ticket_id INTEGER REFERENCES repair_tickets(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
    service_quality_rating INTEGER CHECK (service_quality_rating BETWEEN 1 AND 5),
    communication_rating INTEGER CHECK (communication_rating BETWEEN 1 AND 5),
    timeliness_rating INTEGER CHECK (timeliness_rating BETWEEN 1 AND 5),
    value_rating INTEGER CHECK (value_rating BETWEEN 1 AND 5),
    title VARCHAR(255),
    comment TEXT,
    would_recommend BOOLEAN,
    is_verified BOOLEAN DEFAULT TRUE,
    is_public BOOLEAN DEFAULT TRUE,
    technician_response TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_repair_tickets_status ON repair_tickets(status);
CREATE INDEX IF NOT EXISTS idx_repair_tickets_user_id ON repair_tickets(user_id);
CREATE INDEX IF NOT EXISTS idx_repair_tickets_technician_id ON repair_tickets(technician_id);
CREATE INDEX IF NOT EXISTS idx_repair_tickets_created_at ON repair_tickets(created_at);
CREATE INDEX IF NOT EXISTS idx_repair_ticket_services_ticket_id ON repair_ticket_services(ticket_id);
CREATE INDEX IF NOT EXISTS idx_repair_ticket_status_history_ticket_id ON repair_ticket_status_history(ticket_id);
CREATE INDEX IF NOT EXISTS idx_repair_service_pricing_service_device ON repair_service_pricing(service_id, device_type_id);

-- Insert default repair service categories
INSERT INTO repair_service_categories (name, slug, description, icon, display_order) VALUES
('Screen Repair', 'screen-repair', 'LCD, OLED, and touchscreen repairs for all devices', 'screen', 1),
('Battery Replacement', 'battery-replacement', 'Battery replacement and power-related issues', 'battery', 2),
('Water Damage', 'water-damage', 'Liquid damage assessment and repair services', 'water-drop', 3),
('Charging Port', 'charging-port', 'Charging port and connector repairs', 'plug', 4),
('Camera Repair', 'camera-repair', 'Front and rear camera repairs and replacements', 'camera', 5),
('Speaker/Audio', 'speaker-audio', 'Speaker, microphone, and audio component repairs', 'volume-up', 6),
('Button Repair', 'button-repair', 'Power, volume, and home button repairs', 'radio-button-on', 7),
('Software Issues', 'software-issues', 'Software troubleshooting and OS repairs', 'code', 8),
('Data Recovery', 'data-recovery', 'Data recovery and transfer services', 'folder-open', 9),
('Motherboard Repair', 'motherboard-repair', 'Logic board and motherboard component repairs', 'hardware-chip', 10)
ON CONFLICT (slug) DO NOTHING;

-- Enhanced Users Table with repair system roles
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_technician BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_repair_admin BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS technician_level VARCHAR(20) DEFAULT 'beginner'; -- 'beginner', 'intermediate', 'advanced', 'expert'
ALTER TABLE users ADD COLUMN IF NOT EXISTS max_concurrent_repairs INTEGER DEFAULT 3;

-- Repair System Configuration Table
CREATE TABLE IF NOT EXISTS repair_system_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert default configuration values
INSERT INTO repair_system_config (config_key, config_value, description) VALUES
('business_hours', '{"monday": "9:00-17:00", "tuesday": "9:00-17:00", "wednesday": "9:00-17:00", "thursday": "9:00-17:00", "friday": "9:00-17:00", "saturday": "10:00-15:00", "sunday": "closed"}', 'Business operating hours'),
('emergency_contact', '+1-555-REPAIR', 'Emergency repair contact number'),
('max_file_size_mb', '10', 'Maximum file upload size in MB'),
('supported_file_types', '["jpg", "jpeg", "png", "pdf", "doc", "docx"]', 'Supported file types for uploads'),
('default_warranty_days', '90', 'Default warranty period in days'),
('auto_assign_technicians', 'true', 'Automatically assign technicians to new tickets'),
('email_notifications', 'true', 'Send email notifications for status updates'),
('sms_notifications', 'false', 'Send SMS notifications for status updates')
ON CONFLICT (config_key) DO NOTHING;

-- Repair Ticket Notifications Table
CREATE TABLE IF NOT EXISTS repair_ticket_notifications (
    id SERIAL PRIMARY KEY,
    ticket_id INTEGER REFERENCES repair_tickets(id) ON DELETE CASCADE,
    notification_type VARCHAR(20) NOT NULL, -- 'email', 'sms', 'push'
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'failed'
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Additional indexes for performance
CREATE INDEX IF NOT EXISTS idx_repair_tickets_customer_email ON repair_tickets(customer_email);
CREATE INDEX IF NOT EXISTS idx_repair_tickets_ticket_number ON repair_tickets(ticket_number);
CREATE INDEX IF NOT EXISTS idx_repair_ticket_notifications_ticket_id ON repair_ticket_notifications(ticket_id);
CREATE INDEX IF NOT EXISTS idx_repair_ticket_notifications_status ON repair_ticket_notifications(status);
CREATE INDEX IF NOT EXISTS idx_repair_communications_ticket_id ON repair_communications(ticket_id);
CREATE INDEX IF NOT EXISTS idx_repair_parts_part_number ON repair_parts(part_number);
CREATE INDEX IF NOT EXISTS idx_repair_estimates_ticket_id ON repair_estimates(ticket_id);

-- =====================================================
-- INVENTORY MANAGEMENT SYSTEM TABLES
-- =====================================================

-- Inventory Transactions Table
CREATE TABLE IF NOT EXISTS inventory_transactions (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    transaction_type VARCHAR(50) NOT NULL, -- 'sold', 'restocked', 'reserved', 'released', 'adjustment_increase', 'adjustment_decrease'
    quantity INTEGER NOT NULL,
    cost_per_unit DECIMAL(10,2),
    reference_type VARCHAR(50), -- 'order', 'cart', 'purchase', 'adjustment', 'system'
    reference_id INTEGER,
    supplier VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Stock Alerts Table
CREATE TABLE IF NOT EXISTS stock_alerts (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    alert_type VARCHAR(20) NOT NULL, -- 'low', 'critical', 'out_of_stock'
    current_stock INTEGER NOT NULL,
    threshold INTEGER NOT NULL,
    message TEXT NOT NULL,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    notified_at TIMESTAMP WITH TIME ZONE
);

-- Inventory Adjustments Table
CREATE TABLE IF NOT EXISTS inventory_adjustments (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    old_quantity INTEGER NOT NULL,
    new_quantity INTEGER NOT NULL,
    adjustment_reason VARCHAR(100) NOT NULL, -- 'damaged', 'lost', 'found', 'correction', 'theft', 'expired'
    notes TEXT,
    adjusted_by INTEGER REFERENCES users(id),
    approved_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    approved_at TIMESTAMP WITH TIME ZONE
);

-- Supplier Information Table
CREATE TABLE IF NOT EXISTS suppliers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    website VARCHAR(255),
    payment_terms VARCHAR(100),
    lead_time_days INTEGER DEFAULT 7,
    minimum_order_amount DECIMAL(10,2),
    is_active BOOLEAN DEFAULT TRUE,
    rating DECIMAL(3,2) DEFAULT 5.00,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Product Supplier Relationships
CREATE TABLE IF NOT EXISTS product_suppliers (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    supplier_id INTEGER REFERENCES suppliers(id) ON DELETE CASCADE,
    supplier_sku VARCHAR(100),
    cost_price DECIMAL(10,2) NOT NULL,
    minimum_order_quantity INTEGER DEFAULT 1,
    lead_time_days INTEGER DEFAULT 7,
    is_preferred BOOLEAN DEFAULT FALSE,
    last_order_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, supplier_id)
);

-- Purchase Orders Table
CREATE TABLE IF NOT EXISTS purchase_orders (
    id SERIAL PRIMARY KEY,
    po_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id INTEGER REFERENCES suppliers(id),
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'confirmed', 'shipped', 'received', 'cancelled'
    order_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expected_delivery TIMESTAMP WITH TIME ZONE,
    actual_delivery TIMESTAMP WITH TIME ZONE,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_cost DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    received_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Purchase Order Items Table
CREATE TABLE IF NOT EXISTS purchase_order_items (
    id SERIAL PRIMARY KEY,
    purchase_order_id INTEGER REFERENCES purchase_orders(id) ON DELETE CASCADE,
    product_id INTEGER REFERENCES products(id),
    quantity_ordered INTEGER NOT NULL,
    quantity_received INTEGER DEFAULT 0,
    unit_cost DECIMAL(10,2) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add inventory-related columns to products table if they don't exist
ALTER TABLE products ADD COLUMN IF NOT EXISTS reserved_quantity INTEGER DEFAULT 0;
ALTER TABLE products ADD COLUMN IF NOT EXISTS reorder_level INTEGER DEFAULT 10;
ALTER TABLE products ADD COLUMN IF NOT EXISTS reorder_quantity INTEGER DEFAULT 50;
ALTER TABLE products ADD COLUMN IF NOT EXISTS total_sold INTEGER DEFAULT 0;
ALTER TABLE products ADD COLUMN IF NOT EXISTS last_sold_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS last_restocked_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS supplier_id INTEGER REFERENCES suppliers(id);

-- Indexes for inventory management
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_product_id ON inventory_transactions(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_type ON inventory_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_created_at ON inventory_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_stock_alerts_product_id ON stock_alerts(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_alerts_type ON stock_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_stock_alerts_resolved ON stock_alerts(resolved_at);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier_id ON purchase_orders(supplier_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_status ON purchase_orders(status);
CREATE INDEX IF NOT EXISTS idx_products_stock_quantity ON products(stock_quantity);
CREATE INDEX IF NOT EXISTS idx_products_reserved_quantity ON products(reserved_quantity);

-- =====================================================
-- WEBHOOK AND PAYMENT TRACKING TABLES
-- =====================================================

-- Webhook Logs Table
CREATE TABLE IF NOT EXISTS webhook_logs (
    id SERIAL PRIMARY KEY,
    provider VARCHAR(50) NOT NULL, -- 'stripe', 'paypal', etc.
    event_id VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    data JSONB,
    UNIQUE(provider, event_id)
);

-- Webhook Errors Table
CREATE TABLE IF NOT EXISTS webhook_errors (
    id SERIAL PRIMARY KEY,
    provider VARCHAR(50) NOT NULL,
    event_type VARCHAR(100),
    error_message TEXT NOT NULL,
    raw_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Payment Sessions Table
CREATE TABLE IF NOT EXISTS payment_sessions (
    id SERIAL PRIMARY KEY,
    stripe_session_id VARCHAR(255) UNIQUE,
    user_id INTEGER REFERENCES users(id),
    cart_id VARCHAR(255),
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'completed', 'failed', 'expired'
    amount_total INTEGER, -- in cents
    currency VARCHAR(3) DEFAULT 'usd',
    payment_status VARCHAR(50), -- 'unpaid', 'paid', 'no_payment_required'
    metadata JSONB,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Payment Attempts Table
CREATE TABLE IF NOT EXISTS payment_attempts (
    id SERIAL PRIMARY KEY,
    stripe_payment_intent_id VARCHAR(255) UNIQUE,
    user_id INTEGER REFERENCES users(id),
    order_id INTEGER REFERENCES orders(id),
    amount INTEGER NOT NULL, -- in cents
    currency VARCHAR(3) DEFAULT 'usd',
    status VARCHAR(50) NOT NULL, -- 'requires_payment_method', 'succeeded', 'failed', etc.
    payment_method_types TEXT[],
    error_message TEXT,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Subscriptions Table (for future use)
CREATE TABLE IF NOT EXISTS subscriptions (
    id SERIAL PRIMARY KEY,
    stripe_subscription_id VARCHAR(255) UNIQUE NOT NULL,
    customer_id VARCHAR(255) NOT NULL,
    user_id INTEGER REFERENCES users(id),
    status VARCHAR(50) NOT NULL, -- 'active', 'canceled', 'incomplete', etc.
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    canceled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for webhook and payment tables
CREATE INDEX IF NOT EXISTS idx_webhook_logs_provider ON webhook_logs(provider);
CREATE INDEX IF NOT EXISTS idx_webhook_logs_event_type ON webhook_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_webhook_logs_processed_at ON webhook_logs(processed_at);
CREATE INDEX IF NOT EXISTS idx_payment_sessions_stripe_session_id ON payment_sessions(stripe_session_id);
CREATE INDEX IF NOT EXISTS idx_payment_sessions_user_id ON payment_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_sessions_status ON payment_sessions(status);
CREATE INDEX IF NOT EXISTS idx_payment_attempts_stripe_payment_intent_id ON payment_attempts(stripe_payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_payment_attempts_user_id ON payment_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_attempts_order_id ON payment_attempts(order_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription_id ON subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);

-- =====================================================
-- ORDER FULFILLMENT AND SHIPPING TABLES
-- =====================================================

-- Shipping Labels Table
CREATE TABLE IF NOT EXISTS shipping_labels (
    id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    carrier VARCHAR(50) NOT NULL, -- 'UPS', 'FedEx', 'USPS'
    tracking_number VARCHAR(255) NOT NULL,
    shipping_method VARCHAR(50) NOT NULL, -- 'standard', 'expedited', 'overnight'
    label_url TEXT,
    cost DECIMAL(10,2),
    weight DECIMAL(8,2), -- in kg
    dimensions JSONB, -- {length, width, height} in cm
    from_address JSONB NOT NULL,
    to_address JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'created', -- 'created', 'printed', 'shipped', 'delivered', 'exception'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tracking_number)
);

-- Order Status History Table
CREATE TABLE IF NOT EXISTS order_status_history (
    id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    old_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    notes TEXT,
    tracking_number VARCHAR(255),
    changed_by INTEGER REFERENCES users(id), -- admin user who made the change
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Fulfillment Queue Table
CREATE TABLE IF NOT EXISTS fulfillment_queue (
    id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    priority VARCHAR(20) DEFAULT 'standard', -- 'urgent', 'expedited', 'standard'
    assigned_to INTEGER REFERENCES users(id), -- fulfillment staff
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'in_progress', 'completed', 'on_hold'
    notes TEXT,
    estimated_ship_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(order_id)
);

-- Shipping Rates Table (for dynamic pricing)
CREATE TABLE IF NOT EXISTS shipping_rates (
    id SERIAL PRIMARY KEY,
    carrier VARCHAR(50) NOT NULL,
    service_type VARCHAR(50) NOT NULL, -- 'standard', 'expedited', 'overnight'
    zone VARCHAR(10) NOT NULL, -- shipping zone
    weight_min DECIMAL(8,2) NOT NULL, -- minimum weight in kg
    weight_max DECIMAL(8,2) NOT NULL, -- maximum weight in kg
    base_rate DECIMAL(10,2) NOT NULL,
    per_kg_rate DECIMAL(10,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Package Tracking Events Table
CREATE TABLE IF NOT EXISTS package_tracking_events (
    id SERIAL PRIMARY KEY,
    tracking_number VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL, -- 'label_created', 'picked_up', 'in_transit', 'delivered', etc.
    event_description TEXT,
    location VARCHAR(255),
    event_timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    carrier VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Fulfillment Metrics Table
CREATE TABLE IF NOT EXISTS fulfillment_metrics (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    orders_processed INTEGER DEFAULT 0,
    orders_shipped INTEGER DEFAULT 0,
    orders_delivered INTEGER DEFAULT 0,
    avg_processing_time_hours DECIMAL(8,2),
    avg_shipping_time_days DECIMAL(8,2),
    total_shipping_cost DECIMAL(12,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date)
);

-- Indexes for fulfillment tables
CREATE INDEX IF NOT EXISTS idx_shipping_labels_order_id ON shipping_labels(order_id);
CREATE INDEX IF NOT EXISTS idx_shipping_labels_tracking_number ON shipping_labels(tracking_number);
CREATE INDEX IF NOT EXISTS idx_shipping_labels_carrier ON shipping_labels(carrier);
CREATE INDEX IF NOT EXISTS idx_shipping_labels_status ON shipping_labels(status);

CREATE INDEX IF NOT EXISTS idx_order_status_history_order_id ON order_status_history(order_id);
CREATE INDEX IF NOT EXISTS idx_order_status_history_new_status ON order_status_history(new_status);
CREATE INDEX IF NOT EXISTS idx_order_status_history_created_at ON order_status_history(created_at);

CREATE INDEX IF NOT EXISTS idx_fulfillment_queue_order_id ON fulfillment_queue(order_id);
CREATE INDEX IF NOT EXISTS idx_fulfillment_queue_status ON fulfillment_queue(status);
CREATE INDEX IF NOT EXISTS idx_fulfillment_queue_priority ON fulfillment_queue(priority);
CREATE INDEX IF NOT EXISTS idx_fulfillment_queue_assigned_to ON fulfillment_queue(assigned_to);

CREATE INDEX IF NOT EXISTS idx_shipping_rates_carrier_service ON shipping_rates(carrier, service_type);
CREATE INDEX IF NOT EXISTS idx_shipping_rates_active ON shipping_rates(is_active);

CREATE INDEX IF NOT EXISTS idx_package_tracking_tracking_number ON package_tracking_events(tracking_number);
CREATE INDEX IF NOT EXISTS idx_package_tracking_event_timestamp ON package_tracking_events(event_timestamp);

CREATE INDEX IF NOT EXISTS idx_fulfillment_metrics_date ON fulfillment_metrics(date);

-- Add missing columns to orders table for fulfillment
ALTER TABLE orders ADD COLUMN IF NOT EXISTS tracking_number VARCHAR(255);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS carrier VARCHAR(50);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipping_method VARCHAR(50);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipped_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivered_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS estimated_delivery_date DATE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS fulfillment_priority VARCHAR(20) DEFAULT 'standard';

-- Add indexes for new order columns
CREATE INDEX IF NOT EXISTS idx_orders_tracking_number ON orders(tracking_number);
CREATE INDEX IF NOT EXISTS idx_orders_carrier ON orders(carrier);
CREATE INDEX IF NOT EXISTS idx_orders_shipped_at ON orders(shipped_at);
CREATE INDEX IF NOT EXISTS idx_orders_delivered_at ON orders(delivered_at);
CREATE INDEX IF NOT EXISTS idx_orders_fulfillment_priority ON orders(fulfillment_priority);

-- =====================================================
-- EMAIL TRACKING AND DELIVERY TABLES
-- =====================================================

-- Email Logs Table
CREATE TABLE IF NOT EXISTS email_logs (
    id SERIAL PRIMARY KEY,
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    message_id VARCHAR(255),
    status VARCHAR(50) NOT NULL, -- 'sent', 'delivered', 'bounced', 'failed', 'opened', 'clicked'
    provider VARCHAR(50) NOT NULL, -- 'sendgrid', 'mailgun', 'smtp'
    template_id VARCHAR(100),
    error_message TEXT,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Email Webhook Events Table
CREATE TABLE IF NOT EXISTS email_webhook_events (
    id SERIAL PRIMARY KEY,
    provider VARCHAR(50) NOT NULL,
    message_id VARCHAR(255),
    event_type VARCHAR(100) NOT NULL, -- 'delivered', 'bounced', 'opened', 'clicked', etc.
    event_data JSONB,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Email Templates Table
CREATE TABLE IF NOT EXISTS email_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    subject VARCHAR(500) NOT NULL,
    html_content TEXT NOT NULL,
    text_content TEXT,
    template_variables JSONB,
    provider_template_id VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Email Campaigns Table (for marketing emails)
CREATE TABLE IF NOT EXISTS email_campaigns (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    template_id INTEGER REFERENCES email_templates(id),
    recipient_list JSONB, -- Array of email addresses
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'scheduled', 'sending', 'sent', 'cancelled'
    total_recipients INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    bounced_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for email tables
CREATE INDEX IF NOT EXISTS idx_email_logs_recipient ON email_logs(recipient);
CREATE INDEX IF NOT EXISTS idx_email_logs_message_id ON email_logs(message_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);
CREATE INDEX IF NOT EXISTS idx_email_logs_provider ON email_logs(provider);
CREATE INDEX IF NOT EXISTS idx_email_logs_created_at ON email_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_email_webhook_events_message_id ON email_webhook_events(message_id);
CREATE INDEX IF NOT EXISTS idx_email_webhook_events_event_type ON email_webhook_events(event_type);
CREATE INDEX IF NOT EXISTS idx_email_webhook_events_provider ON email_webhook_events(provider);

CREATE INDEX IF NOT EXISTS idx_email_templates_name ON email_templates(name);
CREATE INDEX IF NOT EXISTS idx_email_templates_active ON email_templates(is_active);

CREATE INDEX IF NOT EXISTS idx_email_campaigns_status ON email_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_email_campaigns_scheduled_at ON email_campaigns(scheduled_at);

-- =====================================================
-- AUTHENTICATION AND SECURITY TABLES
-- =====================================================

-- User Activity Logs Table
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    activity_type VARCHAR(100) NOT NULL, -- 'sign_in', 'sign_out', 'password_change', etc.
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Admin Access Logs Table
CREATE TABLE IF NOT EXISTS admin_access_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    access_type VARCHAR(50) NOT NULL, -- 'admin', 'repair_admin'
    method VARCHAR(10) NOT NULL,
    url TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- API Keys Table
CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    key_hash VARCHAR(255) NOT NULL UNIQUE,
    permissions JSONB, -- Array of allowed permissions
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- API Key Usage Logs Table
CREATE TABLE IF NOT EXISTS api_key_usage_logs (
    id SERIAL PRIMARY KEY,
    api_key_id INTEGER REFERENCES api_keys(id),
    method VARCHAR(10) NOT NULL,
    url TEXT NOT NULL,
    ip_address INET,
    response_status INTEGER,
    response_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Rate Limit Logs Table
CREATE TABLE IF NOT EXISTS rate_limit_logs (
    id SERIAL PRIMARY KEY,
    key VARCHAR(255) NOT NULL, -- IP address or user ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- ANALYTICS AND MONITORING TABLES
-- =====================================================

-- Analytics Events Table
CREATE TABLE IF NOT EXISTS analytics_events (
    id SERIAL PRIMARY KEY,
    event_name VARCHAR(100) NOT NULL,
    properties JSONB,
    user_id INTEGER REFERENCES users(id),
    session_id VARCHAR(255),
    page_url TEXT,
    referrer TEXT,
    user_agent TEXT,
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- E-commerce Events Table
CREATE TABLE IF NOT EXISTS ecommerce_events (
    id SERIAL PRIMARY KEY,
    action VARCHAR(50) NOT NULL, -- 'purchase', 'add_to_cart', 'begin_checkout', etc.
    order_id INTEGER REFERENCES orders(id),
    user_id INTEGER REFERENCES users(id),
    value DECIMAL(12,2),
    currency VARCHAR(3) DEFAULT 'USD',
    items JSONB, -- Array of items
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Performance Metrics Table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    value DECIMAL(12,4) NOT NULL,
    tags JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Business Metrics Table
CREATE TABLE IF NOT EXISTS business_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    value DECIMAL(15,4) NOT NULL,
    metadata JSONB,
    date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(metric_name, date)
);

-- Error Logs Table
CREATE TABLE IF NOT EXISTS error_logs (
    id SERIAL PRIMARY KEY,
    error_type VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    user_id INTEGER REFERENCES users(id),
    request_url TEXT,
    request_method VARCHAR(10),
    request_data JSONB,
    user_agent TEXT,
    ip_address INET,
    severity VARCHAR(20) DEFAULT 'error', -- 'info', 'warning', 'error', 'critical'
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- System Health Metrics Table
CREATE TABLE IF NOT EXISTS system_health_metrics (
    id SERIAL PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL, -- 'cpu', 'memory', 'disk', 'database', etc.
    value DECIMAL(10,4) NOT NULL,
    unit VARCHAR(20), -- '%', 'MB', 'ms', etc.
    threshold_warning DECIMAL(10,4),
    threshold_critical DECIMAL(10,4),
    status VARCHAR(20) DEFAULT 'ok', -- 'ok', 'warning', 'critical'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for authentication and security tables
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_activity_type ON user_activity_logs(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_created_at ON user_activity_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_admin_access_logs_user_id ON admin_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_access_logs_access_type ON admin_access_logs(access_type);
CREATE INDEX IF NOT EXISTS idx_admin_access_logs_created_at ON admin_access_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(is_active);

CREATE INDEX IF NOT EXISTS idx_api_key_usage_logs_api_key_id ON api_key_usage_logs(api_key_id);
CREATE INDEX IF NOT EXISTS idx_api_key_usage_logs_created_at ON api_key_usage_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_rate_limit_logs_key ON rate_limit_logs(key);
CREATE INDEX IF NOT EXISTS idx_rate_limit_logs_created_at ON rate_limit_logs(created_at);

-- Indexes for analytics and monitoring tables
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_name ON analytics_events(event_name);
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON analytics_events(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON analytics_events(created_at);

CREATE INDEX IF NOT EXISTS idx_ecommerce_events_action ON ecommerce_events(action);
CREATE INDEX IF NOT EXISTS idx_ecommerce_events_order_id ON ecommerce_events(order_id);
CREATE INDEX IF NOT EXISTS idx_ecommerce_events_user_id ON ecommerce_events(user_id);
CREATE INDEX IF NOT EXISTS idx_ecommerce_events_created_at ON ecommerce_events(created_at);

CREATE INDEX IF NOT EXISTS idx_performance_metrics_metric_name ON performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_created_at ON performance_metrics(created_at);

CREATE INDEX IF NOT EXISTS idx_business_metrics_metric_name ON business_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_business_metrics_date ON business_metrics(date);

CREATE INDEX IF NOT EXISTS idx_error_logs_error_type ON error_logs(error_type);
CREATE INDEX IF NOT EXISTS idx_error_logs_severity ON error_logs(severity);
CREATE INDEX IF NOT EXISTS idx_error_logs_resolved ON error_logs(resolved);
CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON error_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_system_health_metrics_metric_type ON system_health_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_system_health_metrics_status ON system_health_metrics(status);
CREATE INDEX IF NOT EXISTS idx_system_health_metrics_created_at ON system_health_metrics(created_at);

-- Add missing columns to users table for authentication
ALTER TABLE users ADD COLUMN IF NOT EXISTS provider VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS provider_id VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_repair_admin BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS loyalty_points INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS loyalty_tier VARCHAR(20) DEFAULT 'bronze';
ALTER TABLE users ADD COLUMN IF NOT EXISTS store_credit DECIMAL(10,2) DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS marketing_consent BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS device_interests JSONB;

-- =====================================================
-- CUSTOMER ONBOARDING SYSTEM TABLES
-- =====================================================

-- Customer Onboarding Table
CREATE TABLE IF NOT EXISTS customer_onboarding (
    id SERIAL PRIMARY KEY,
    user_id INTEGER UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active', -- 'active', 'completed', 'paused'
    current_step INTEGER DEFAULT 1,
    completed_steps TEXT[], -- Array of completed step names
    preferences JSONB,
    device_interests JSONB,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_email_sent TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Email Campaigns Queue Table
CREATE TABLE IF NOT EXISTS email_campaigns_queue (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    template_type VARCHAR(100) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE,
    onboarding_step INTEGER,
    status VARCHAR(50) DEFAULT 'scheduled', -- 'scheduled', 'sent', 'failed'
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User Profiles Table
CREATE TABLE IF NOT EXISTS user_profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    device_interests JSONB,
    marketing_preferences JSONB,
    shipping_addresses JSONB,
    billing_addresses JSONB,
    onboarding_completed BOOLEAN DEFAULT FALSE,
    profile_completion_percentage INTEGER DEFAULT 0,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Email Verification Tokens Table
CREATE TABLE IF NOT EXISTS email_verification_tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- SALES OPTIMIZATION SYSTEM TABLES
-- =====================================================

-- Abandoned Carts Table
CREATE TABLE IF NOT EXISTS abandoned_carts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    cart_data JSONB NOT NULL,
    cart_value DECIMAL(10,2) NOT NULL,
    items_count INTEGER NOT NULL,
    recovery_emails_sent INTEGER DEFAULT 0,
    last_recovery_email TIMESTAMP WITH TIME ZONE,
    recovered BOOLEAN DEFAULT FALSE,
    recovered_at TIMESTAMP WITH TIME ZONE,
    recovery_order_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Discount Codes Table
CREATE TABLE IF NOT EXISTS discount_codes (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    type VARCHAR(20) NOT NULL, -- 'percentage', 'fixed_amount', 'free_shipping'
    value DECIMAL(10,2) NOT NULL,
    min_order_amount DECIMAL(10,2) DEFAULT 0,
    max_uses INTEGER DEFAULT 1,
    current_uses INTEGER DEFAULT 0,
    user_id INTEGER REFERENCES users(id), -- NULL for public codes
    usage_type VARCHAR(50), -- 'cart_recovery', 'referral_welcome', 'loyalty', etc.
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Product Recommendations Table
CREATE TABLE IF NOT EXISTS product_recommendations (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    recommended_product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    recommendation_type VARCHAR(50) NOT NULL, -- 'frequently_bought_together', 'customers_also_viewed', etc.
    score DECIMAL(5,4) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, product_id, recommended_product_id, recommendation_type)
);

-- Seasonal Promotions Table
CREATE TABLE IF NOT EXISTS seasonal_promotions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    discount_percentage DECIMAL(5,2) NOT NULL,
    category_ids INTEGER[],
    product_ids INTEGER[],
    min_order_amount DECIMAL(10,2) DEFAULT 0,
    max_discount_amount DECIMAL(10,2),
    is_active BOOLEAN DEFAULT TRUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- REFERRAL PROGRAM TABLES
-- =====================================================

-- User Referrals Table
CREATE TABLE IF NOT EXISTS user_referrals (
    id SERIAL PRIMARY KEY,
    user_id INTEGER UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    referral_code VARCHAR(20) NOT NULL UNIQUE,
    total_referrals INTEGER DEFAULT 0,
    total_rewards_earned DECIMAL(10,2) DEFAULT 0,
    last_referral_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Referral Signups Table
CREATE TABLE IF NOT EXISTS referral_signups (
    id SERIAL PRIMARY KEY,
    referrer_user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    referred_user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    referral_code VARCHAR(20) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'completed', 'cancelled'
    first_order_id INTEGER,
    first_order_value DECIMAL(10,2),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(referred_user_id)
);

-- Referral Rewards Table
CREATE TABLE IF NOT EXISTS referral_rewards (
    id SERIAL PRIMARY KEY,
    referral_signup_id INTEGER REFERENCES referral_signups(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    reward_type VARCHAR(50) NOT NULL, -- 'credit', 'discount', 'points'
    reward_amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    claimed BOOLEAN DEFAULT FALSE,
    claimed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- CUSTOMER SUPPORT TABLES
-- =====================================================

-- Support Tickets Table
CREATE TABLE IF NOT EXISTS support_tickets (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    ticket_number VARCHAR(20) NOT NULL UNIQUE,
    subject VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100), -- 'order', 'product', 'technical', 'billing', etc.
    priority VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    status VARCHAR(50) DEFAULT 'open', -- 'open', 'in_progress', 'waiting_customer', 'resolved', 'closed'
    assigned_to INTEGER REFERENCES users(id),
    order_id INTEGER REFERENCES orders(id),
    product_id INTEGER REFERENCES products(id),
    customer_email VARCHAR(255),
    customer_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Support Ticket Messages Table
CREATE TABLE IF NOT EXISTS support_ticket_messages (
    id SERIAL PRIMARY KEY,
    ticket_id INTEGER REFERENCES support_tickets(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id),
    message TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    attachments JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- FAQ Table
CREATE TABLE IF NOT EXISTS faq_items (
    id SERIAL PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    view_count INTEGER DEFAULT 0,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR CUSTOMER ONBOARDING AND SALES OPTIMIZATION
-- =====================================================

-- Customer Onboarding Indexes
CREATE INDEX IF NOT EXISTS idx_customer_onboarding_user_id ON customer_onboarding(user_id);
CREATE INDEX IF NOT EXISTS idx_customer_onboarding_status ON customer_onboarding(status);
CREATE INDEX IF NOT EXISTS idx_customer_onboarding_started_at ON customer_onboarding(started_at);

CREATE INDEX IF NOT EXISTS idx_email_campaigns_queue_user_id ON email_campaigns_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_email_campaigns_queue_scheduled_at ON email_campaigns_queue(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_email_campaigns_queue_status ON email_campaigns_queue(status);

CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_onboarding_completed ON user_profiles(onboarding_completed);

CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_token ON email_verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_user_id ON email_verification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires_at ON email_verification_tokens(expires_at);

-- Sales Optimization Indexes
CREATE INDEX IF NOT EXISTS idx_abandoned_carts_user_id ON abandoned_carts(user_id);
CREATE INDEX IF NOT EXISTS idx_abandoned_carts_cart_value ON abandoned_carts(cart_value);
CREATE INDEX IF NOT EXISTS idx_abandoned_carts_last_updated ON abandoned_carts(last_updated);
CREATE INDEX IF NOT EXISTS idx_abandoned_carts_recovery_emails_sent ON abandoned_carts(recovery_emails_sent);

CREATE INDEX IF NOT EXISTS idx_discount_codes_code ON discount_codes(code);
CREATE INDEX IF NOT EXISTS idx_discount_codes_user_id ON discount_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_discount_codes_expires_at ON discount_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_discount_codes_is_active ON discount_codes(is_active);

CREATE INDEX IF NOT EXISTS idx_product_recommendations_user_id ON product_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_product_recommendations_product_id ON product_recommendations(product_id);
CREATE INDEX IF NOT EXISTS idx_product_recommendations_type ON product_recommendations(recommendation_type);

CREATE INDEX IF NOT EXISTS idx_seasonal_promotions_active ON seasonal_promotions(is_active);
CREATE INDEX IF NOT EXISTS idx_seasonal_promotions_dates ON seasonal_promotions(start_date, end_date);

-- Referral Program Indexes
CREATE INDEX IF NOT EXISTS idx_user_referrals_user_id ON user_referrals(user_id);
CREATE INDEX IF NOT EXISTS idx_user_referrals_referral_code ON user_referrals(referral_code);

CREATE INDEX IF NOT EXISTS idx_referral_signups_referrer_user_id ON referral_signups(referrer_user_id);
CREATE INDEX IF NOT EXISTS idx_referral_signups_referred_user_id ON referral_signups(referred_user_id);
CREATE INDEX IF NOT EXISTS idx_referral_signups_status ON referral_signups(status);

CREATE INDEX IF NOT EXISTS idx_referral_rewards_user_id ON referral_rewards(user_id);
CREATE INDEX IF NOT EXISTS idx_referral_rewards_claimed ON referral_rewards(claimed);

-- Customer Support Indexes
CREATE INDEX IF NOT EXISTS idx_support_tickets_user_id ON support_tickets(user_id);
CREATE INDEX IF NOT EXISTS idx_support_tickets_ticket_number ON support_tickets(ticket_number);
CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON support_tickets(status);
CREATE INDEX IF NOT EXISTS idx_support_tickets_category ON support_tickets(category);
CREATE INDEX IF NOT EXISTS idx_support_tickets_created_at ON support_tickets(created_at);

CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_ticket_id ON support_ticket_messages(ticket_id);
CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_user_id ON support_ticket_messages(user_id);

CREATE INDEX IF NOT EXISTS idx_faq_items_category ON faq_items(category);
CREATE INDEX IF NOT EXISTS idx_faq_items_active ON faq_items(is_active);
CREATE INDEX IF NOT EXISTS idx_faq_items_order_index ON faq_items(order_index);

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { 
  ChartBarIcon, 
  UsersIcon, 
  ShoppingCartIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import AdminLayout from '../../components/AdminLayout';

export default function SalesAnalyticsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState('overview');
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    to: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session?.user && !session.user.isAdmin) {
      router.push('/');
      return;
    }

    if (session?.user?.isAdmin) {
      fetchAnalyticsData();
    }
  }, [session, status, selectedMetric, dateRange]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        metric: selectedMetric,
        dateFrom: dateRange.from,
        dateTo: dateRange.to
      });

      const response = await fetch(`/api/admin/sales-analytics?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAnalyticsData(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshData = () => {
    fetchAnalyticsData();
  };

  if (loading && !analyticsData) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <>
      <Head>
        <title>Sales Analytics - Admin Dashboard</title>
        <meta name="description" content="Comprehensive sales analytics and conversion metrics for Midas Technical." />
      </Head>

      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Sales Analytics</h1>
              <p className="text-gray-600">Monitor conversion rates and customer journey metrics</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={refreshData}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>

          {/* Date Range and Metric Selector */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div className="flex items-center space-x-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">From</label>
                  <input
                    type="date"
                    value={dateRange.from}
                    onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">To</label>
                  <input
                    type="date"
                    value={dateRange.to}
                    onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
              
              <div className="flex space-x-2">
                {[
                  { key: 'overview', label: 'Overview' },
                  { key: 'funnel', label: 'Funnel' },
                  { key: 'abandonment', label: 'Cart Recovery' },
                  { key: 'referrals', label: 'Referrals' },
                  { key: 'onboarding', label: 'Onboarding' }
                ].map((metric) => (
                  <button
                    key={metric.key}
                    onClick={() => setSelectedMetric(metric.key)}
                    className={`px-3 py-2 text-sm font-medium rounded-md ${
                      selectedMetric === metric.key
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {metric.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Overview Metrics */}
          {selectedMetric === 'overview' && analyticsData?.kpi && (
            <>
              {/* KPI Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <KPICard
                  title="Total Revenue"
                  value={`$${analyticsData.kpi.totalRevenue.toLocaleString()}`}
                  change={12.5}
                  icon={CurrencyDollarIcon}
                  color="green"
                />
                <KPICard
                  title="Total Orders"
                  value={analyticsData.kpi.totalOrders.toLocaleString()}
                  change={8.2}
                  icon={ShoppingCartIcon}
                  color="blue"
                />
                <KPICard
                  title="Unique Customers"
                  value={analyticsData.kpi.uniqueCustomers.toLocaleString()}
                  change={15.3}
                  icon={UsersIcon}
                  color="purple"
                />
                <KPICard
                  title="Avg Order Value"
                  value={`$${analyticsData.kpi.avgOrderValue.toFixed(2)}`}
                  change={-2.1}
                  icon={TrendingUpIcon}
                  color="orange"
                />
              </div>

              {/* Conversion Metrics */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Conversion Rates</h3>
                  <div className="space-y-4">
                    <ConversionMetric
                      label="Visitor to Customer"
                      rate={analyticsData.kpi.visitorToCustomerRate}
                      total={analyticsData.kpi.totalVisitors}
                      converted={analyticsData.kpi.uniqueCustomers}
                    />
                    <ConversionMetric
                      label="Cart to Checkout"
                      rate={analyticsData.kpi.cartToCheckoutRate}
                      total={analyticsData.kpi.cartUsers}
                      converted={analyticsData.kpi.checkoutUsers}
                    />
                    <ConversionMetric
                      label="Cart Recovery"
                      rate={analyticsData.kpi.cartRecoveryRate}
                      total={analyticsData.kpi.abandonedCarts}
                      converted={analyticsData.kpi.recoveredCarts}
                    />
                    <ConversionMetric
                      label="Onboarding Completion"
                      rate={analyticsData.kpi.onboardingCompletionRate}
                      total={analyticsData.kpi.newSignups}
                      converted={analyticsData.kpi.completedOnboarding}
                    />
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Top Performing Products</h3>
                  <div className="space-y-3">
                    {analyticsData.topProducts?.slice(0, 5).map((product, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {product.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {product.unitsSold} units • {product.ordersCount} orders
                          </p>
                        </div>
                        <div className="text-sm font-medium text-gray-900">
                          ${product.revenue.toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Funnel Analysis */}
          {selectedMetric === 'funnel' && analyticsData?.funnel && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-6">Sales Funnel Analysis</h3>
              <div className="space-y-4">
                {analyticsData.funnel.map((stage, index) => (
                  <FunnelStage
                    key={stage.stage}
                    stage={stage}
                    isFirst={index === 0}
                    previousStage={index > 0 ? analyticsData.funnel[index - 1] : null}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Cart Abandonment */}
          {selectedMetric === 'abandonment' && analyticsData && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Cart Abandonment Overview</h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Abandoned Carts</span>
                    <span className="font-medium">{analyticsData.totalAbandonedCarts}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Abandoned Value</span>
                    <span className="font-medium">${analyticsData.totalAbandonedValue?.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Recovery Rate</span>
                    <span className="font-medium text-green-600">{analyticsData.recoveryRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Recovered Value</span>
                    <span className="font-medium text-green-600">${analyticsData.recoveredValue?.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Recovery by Email Sequence</h3>
                <div className="space-y-3">
                  {analyticsData.recoveryByEmail?.map((email, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-gray-600">Email {email.recovery_emails_sent + 1}</span>
                      <div className="text-right">
                        <div className="text-sm font-medium">{email.recovery_rate_by_email}% recovery</div>
                        <div className="text-xs text-gray-500">{email.recovered_count}/{email.carts_count} carts</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Referral Analytics */}
          {selectedMetric === 'referrals' && analyticsData && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Referral Program Performance</h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Referrers</span>
                    <span className="font-medium">{analyticsData.totalReferrers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Referrals</span>
                    <span className="font-medium">{analyticsData.totalReferrals}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Conversion Rate</span>
                    <span className="font-medium text-green-600">{analyticsData.referralConversionRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Referral Revenue</span>
                    <span className="font-medium">${analyticsData.referralRevenue?.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">ROI Ratio</span>
                    <span className="font-medium text-green-600">{analyticsData.roiRatio}:1</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Top Referrers</h3>
                <div className="space-y-3">
                  {analyticsData.topReferrers?.slice(0, 5).map((referrer, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {referrer.first_name} {referrer.last_name}
                        </p>
                        <p className="text-xs text-gray-500">{referrer.email}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{referrer.total_referrals} referrals</div>
                        <div className="text-xs text-gray-500">${referrer.total_rewards_earned} earned</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Onboarding Analytics */}
          {selectedMetric === 'onboarding' && analyticsData?.onboardingFunnel && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Onboarding Funnel</h3>
                <div className="space-y-4">
                  <OnboardingStep
                    label="Total Signups"
                    count={analyticsData.onboardingFunnel.totalSignups}
                    rate={100}
                  />
                  <OnboardingStep
                    label="Email Verified"
                    count={analyticsData.onboardingFunnel.emailVerified}
                    rate={analyticsData.onboardingFunnel.emailVerificationRate}
                  />
                  <OnboardingStep
                    label="Profile Completed"
                    count={analyticsData.onboardingFunnel.profileCompleted}
                    rate={analyticsData.onboardingFunnel.profileCompletionRate}
                  />
                  <OnboardingStep
                    label="First Browse"
                    count={analyticsData.onboardingFunnel.firstBrowse}
                    rate={analyticsData.onboardingFunnel.firstBrowseRate}
                  />
                  <OnboardingStep
                    label="Completed Onboarding"
                    count={analyticsData.onboardingFunnel.completedOnboarding}
                    rate={analyticsData.onboardingFunnel.onboardingCompletionRate}
                  />
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Email Campaign Performance</h3>
                <div className="space-y-3">
                  {analyticsData.emailCampaigns?.map((campaign, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 capitalize">
                          {campaign.templateType.replace('_', ' ')}
                        </p>
                        <p className="text-xs text-gray-500">
                          {campaign.emailsSent} sent • {campaign.deliveryRate}% delivered
                        </p>
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        Step {campaign.avgStep}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </AdminLayout>
    </>
  );
}

// KPI Card Component
function KPICard({ title, value, change, icon: Icon, color }) {
  const isPositive = change > 0;
  const colorClasses = {
    green: 'bg-green-100 text-green-600',
    blue: 'bg-blue-100 text-blue-600',
    purple: 'bg-purple-100 text-purple-600',
    orange: 'bg-orange-100 text-orange-600'
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          <div className="flex items-center mt-1">
            {isPositive ? (
              <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
            ) : (
              <TrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
            )}
            <span className={`text-sm font-medium ${
              isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              {Math.abs(change)}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Conversion Metric Component
function ConversionMetric({ label, rate, total, converted }) {
  return (
    <div>
      <div className="flex justify-between items-center mb-1">
        <span className="text-sm text-gray-600">{label}</span>
        <span className="text-sm font-medium">{rate}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-600 h-2 rounded-full"
          style={{ width: `${Math.min(rate, 100)}%` }}
        ></div>
      </div>
      <div className="flex justify-between text-xs text-gray-500 mt-1">
        <span>{converted} converted</span>
        <span>{total} total</span>
      </div>
    </div>
  );
}

// Funnel Stage Component
function FunnelStage({ stage, isFirst, previousStage }) {
  const dropoffRate = previousStage 
    ? ((previousStage.unique_users - stage.unique_users) / previousStage.unique_users * 100).toFixed(1)
    : 0;

  return (
    <div className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
      <div className="flex-1">
        <h4 className="font-medium text-gray-900 capitalize">
          {stage.stage.replace('_', ' ')}
        </h4>
        <p className="text-sm text-gray-600">
          {stage.unique_users.toLocaleString()} users
        </p>
      </div>
      <div className="text-right">
        <div className="text-lg font-semibold text-gray-900">
          {stage.conversion_rate}%
        </div>
        {!isFirst && (
          <div className="text-sm text-red-600">
            -{dropoffRate}% dropoff
          </div>
        )}
      </div>
    </div>
  );
}

// Onboarding Step Component
function OnboardingStep({ label, count, rate }) {
  return (
    <div>
      <div className="flex justify-between items-center mb-1">
        <span className="text-sm text-gray-600">{label}</span>
        <span className="text-sm font-medium">{rate}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-purple-600 h-2 rounded-full"
          style={{ width: `${Math.min(rate, 100)}%` }}
        ></div>
      </div>
      <div className="text-xs text-gray-500 mt-1">
        {count} users
      </div>
    </div>
  );
}

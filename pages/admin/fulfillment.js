import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/head';
import Link from 'next/link';
import { 
  TruckIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PrinterIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

export default function FulfillmentDashboard() {
  const [dashboardData, setDashboardData] = useState(null);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [bulkAction, setBulkAction] = useState('');

  useEffect(() => {
    fetchDashboardData();
    fetchOrders();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/admin/fulfillment?action=dashboard', {
        headers: {
          'Authorization': 'Bearer admin-token' // In production, use real auth
        }
      });
      const data = await response.json();
      if (data.success) {
        setDashboardData(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    }
  };

  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/admin/fulfillment?action=orders', {
        headers: {
          'Authorization': 'Bearer admin-token'
        }
      });
      const data = await response.json();
      if (data.success) {
        setOrders(data.data.orders);
      }
    } catch (error) {
      console.error('Failed to fetch orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProcessOrder = async (orderId) => {
    try {
      const response = await fetch('/api/admin/fulfillment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({
          action: 'process-order',
          orderId
        })
      });

      const data = await response.json();
      if (data.success) {
        alert('Order processed successfully!');
        fetchOrders();
      } else {
        alert('Failed to process order: ' + data.message);
      }
    } catch (error) {
      console.error('Process order error:', error);
      alert('Failed to process order');
    }
  };

  const handleShipOrder = async (orderId) => {
    try {
      const response = await fetch('/api/admin/fulfillment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({
          action: 'ship-order',
          orderId,
          shippingMethod: 'STANDARD'
        })
      });

      const data = await response.json();
      if (data.success) {
        alert(`Order shipped! Tracking: ${data.data.trackingNumber}`);
        fetchOrders();
      } else {
        alert('Failed to ship order: ' + data.message);
      }
    } catch (error) {
      console.error('Ship order error:', error);
      alert('Failed to ship order');
    }
  };

  const handleBulkAction = async () => {
    if (!bulkAction || selectedOrders.length === 0) {
      alert('Please select orders and an action');
      return;
    }

    try {
      const response = await fetch('/api/admin/fulfillment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({
          action: 'bulk-update',
          orderIds: selectedOrders,
          status: bulkAction
        })
      });

      const data = await response.json();
      if (data.success) {
        alert(data.message);
        setSelectedOrders([]);
        setBulkAction('');
        fetchOrders();
      } else {
        alert('Bulk action failed: ' + data.message);
      }
    } catch (error) {
      console.error('Bulk action error:', error);
      alert('Bulk action failed');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'ready_to_ship':
        return 'bg-green-100 text-green-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading fulfillment dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Fulfillment Dashboard - Midas Technical Admin</title>
        <meta name="description" content="Order fulfillment management dashboard" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-4">
                <Link href="/" className="text-2xl font-bold text-gray-900">
                  Midas Technical
                </Link>
                <span className="text-gray-400">|</span>
                <span className="text-lg font-medium text-gray-700">Fulfillment Dashboard</span>
              </div>
              <nav className="flex space-x-4">
                <Link href="/admin" className="text-gray-700 hover:text-gray-900">Admin Home</Link>
                <Link href="/admin/orders" className="text-gray-700 hover:text-gray-900">Orders</Link>
                <Link href="/admin/inventory" className="text-gray-700 hover:text-gray-900">Inventory</Link>
              </nav>
            </div>
          </div>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Dashboard Stats */}
          {dashboardData && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <ClockIcon className="w-8 h-8 text-yellow-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Ready to Ship</p>
                    <p className="text-2xl font-bold text-gray-900">{dashboardData.dashboard.orders_ready_to_ship}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <TruckIcon className="w-8 h-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Processing</p>
                    <p className="text-2xl font-bold text-gray-900">{dashboardData.dashboard.orders_processing}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <CheckCircleIcon className="w-8 h-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Shipped Today</p>
                    <p className="text-2xl font-bold text-gray-900">{dashboardData.dashboard.orders_shipped}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <ExclamationTriangleIcon className="w-8 h-8 text-red-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Avg Processing</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {Math.round(dashboardData.dashboard.avg_processing_hours || 0)}h
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Bulk Actions */}
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Bulk Actions</h2>
              <div className="flex items-center space-x-4">
                <select
                  value={bulkAction}
                  onChange={(e) => setBulkAction(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">Select Action</option>
                  <option value="processing">Mark as Processing</option>
                  <option value="ready_to_ship">Mark as Ready to Ship</option>
                  <option value="shipped">Mark as Shipped</option>
                </select>
                <button
                  onClick={handleBulkAction}
                  disabled={selectedOrders.length === 0 || !bulkAction}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  Apply to {selectedOrders.length} orders
                </button>
              </div>
            </div>
          </div>

          {/* Orders Table */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Orders for Fulfillment</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedOrders(orders.map(o => o.id));
                          } else {
                            setSelectedOrders([]);
                          }
                        }}
                        checked={selectedOrders.length === orders.length && orders.length > 0}
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Items
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orders.map((order) => (
                    <tr key={order.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedOrders.includes(order.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedOrders([...selectedOrders, order.id]);
                            } else {
                              setSelectedOrders(selectedOrders.filter(id => id !== order.id));
                            }
                          }}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{order.order_number}</div>
                          <div className="text-sm text-gray-500">
                            {new Date(order.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{order.customer_name}</div>
                        <div className="text-sm text-gray-500">{order.customer_email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                          {order.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.total_items} items
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${parseFloat(order.total_amount).toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        {order.status === 'confirmed' && (
                          <button
                            onClick={() => handleProcessOrder(order.id)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Process
                          </button>
                        )}
                        {order.status === 'ready_to_ship' && (
                          <button
                            onClick={() => handleShipOrder(order.id)}
                            className="text-green-600 hover:text-green-900"
                          >
                            Ship
                          </button>
                        )}
                        <button className="text-gray-600 hover:text-gray-900">
                          <EyeIcon className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {orders.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500">No orders ready for fulfillment</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { 
  GiftIcon, 
  UserGroupIcon, 
  CurrencyDollarIcon,
  ClipboardDocumentIcon,
  ShareIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import AccountLayout from '../../components/AccountLayout';

export default function ReferralsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [referralData, setReferralData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session?.user) {
      fetchReferralData();
    }
  }, [session, status]);

  const fetchReferralData = async () => {
    try {
      const response = await fetch('/api/referrals');
      if (response.ok) {
        const data = await response.json();
        setReferralData(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch referral data:', error);
    } finally {
      setLoading(false);
    }
  };

  const copyReferralLink = async () => {
    if (referralData?.shareUrl) {
      try {
        await navigator.clipboard.writeText(referralData.shareUrl);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy link:', error);
      }
    }
  };

  const shareViaEmail = () => {
    const subject = encodeURIComponent('Join Midas Technical - Premium Electronic Components');
    const body = encodeURIComponent(`Hi there!

I wanted to share Midas Technical with you - they have an amazing selection of premium electronic components and professional repair services.

Use my referral link to get 15% off your first order:
${referralData?.shareUrl}

You'll love their quality products and fast shipping!

Best regards,
${session?.user?.name || 'Your friend'}`);
    
    window.open(`mailto:?subject=${subject}&body=${body}`);
  };

  const shareViaSocial = (platform) => {
    const url = encodeURIComponent(referralData?.shareUrl);
    const text = encodeURIComponent('Check out Midas Technical for premium electronic components! Get 15% off your first order with my referral link.');
    
    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?text=${text}&url=${url}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${url}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${url}`
    };
    
    window.open(shareUrls[platform], '_blank', 'width=600,height=400');
  };

  if (loading) {
    return (
      <AccountLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </AccountLayout>
    );
  }

  return (
    <>
      <Head>
        <title>Referral Program - Midas Technical</title>
        <meta name="description" content="Refer friends to Midas Technical and earn rewards for every successful referral." />
      </Head>

      <AccountLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-purple-800 rounded-lg shadow-xl p-8 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold mb-2">
                  Referral Program 👥
                </h1>
                <p className="text-purple-100 text-lg">
                  Earn $25 store credit for every friend you refer!
                </p>
              </div>
              <div className="hidden md:block">
                <GiftIcon className="h-24 w-24 text-purple-200" />
              </div>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatCard
              title="Total Referrals"
              value={referralData?.totalReferrals || 0}
              icon={UserGroupIcon}
              color="blue"
            />
            <StatCard
              title="Pending Referrals"
              value={referralData?.pendingReferrals || 0}
              icon={ClockIcon}
              color="yellow"
            />
            <StatCard
              title="Total Earned"
              value={`$${(referralData?.totalRewardsEarned || 0).toFixed(2)}`}
              icon={CurrencyDollarIcon}
              color="green"
            />
            <StatCard
              title="Success Rate"
              value={referralData?.totalReferrals > 0 
                ? `${Math.round(((referralData.totalReferrals - referralData.pendingReferrals) / referralData.totalReferrals) * 100)}%`
                : '0%'
              }
              icon={CheckCircleIcon}
              color="purple"
            />
          </div>

          {/* How It Works */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              How It Works
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ShareIcon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-2">1. Share Your Link</h3>
                <p className="text-sm text-gray-600">
                  Share your unique referral link with friends and family
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <UserGroupIcon className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-2">2. Friend Signs Up</h3>
                <p className="text-sm text-gray-600">
                  Your friend creates an account and gets 15% off their first order
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <GiftIcon className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-2">3. You Earn Rewards</h3>
                <p className="text-sm text-gray-600">
                  Get $25 store credit when they make their first purchase over $50
                </p>
              </div>
            </div>
          </div>

          {/* Referral Link */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Your Referral Link
            </h2>
            <div className="flex items-center space-x-4 mb-4">
              <div className="flex-1">
                <input
                  type="text"
                  value={referralData?.shareUrl || ''}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                />
              </div>
              <button
                onClick={copyReferralLink}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  copied 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {copied ? (
                  <>
                    <CheckCircleIcon className="h-4 w-4 inline mr-1" />
                    Copied!
                  </>
                ) : (
                  <>
                    <ClipboardDocumentIcon className="h-4 w-4 inline mr-1" />
                    Copy
                  </>
                )}
              </button>
            </div>

            {/* Share Buttons */}
            <div className="flex flex-wrap gap-3">
              <button
                onClick={shareViaEmail}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm font-medium"
              >
                📧 Share via Email
              </button>
              <button
                onClick={() => shareViaSocial('twitter')}
                className="px-4 py-2 bg-blue-400 text-white rounded-md hover:bg-blue-500 text-sm font-medium"
              >
                🐦 Share on Twitter
              </button>
              <button
                onClick={() => shareViaSocial('facebook')}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium"
              >
                📘 Share on Facebook
              </button>
              <button
                onClick={() => shareViaSocial('linkedin')}
                className="px-4 py-2 bg-blue-700 text-white rounded-md hover:bg-blue-800 text-sm font-medium"
              >
                💼 Share on LinkedIn
              </button>
            </div>
          </div>

          {/* Recent Referrals */}
          {referralData?.recentReferrals && referralData.recentReferrals.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Recent Referrals
              </h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Friend
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Signup Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Completed
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {referralData.recentReferrals.map((referral) => (
                      <tr key={referral.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {referral.referredUserName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            referral.status === 'completed' 
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {referral.status === 'completed' ? 'Completed' : 'Pending'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(referral.signupDate).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {referral.completedDate 
                            ? new Date(referral.completedDate).toLocaleDateString()
                            : '-'
                          }
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Terms */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">
              Referral Program Terms
            </h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>• Referrer earns $25 store credit when referee makes first purchase over $50</li>
              <li>• Referee gets 15% off their first order</li>
              <li>• Referral rewards are automatically added to your account</li>
              <li>• Store credit never expires and can be used on any future purchase</li>
              <li>• Self-referrals and fraudulent activity are prohibited</li>
              <li>• Midas Technical reserves the right to modify or terminate the program</li>
            </ul>
          </div>
        </div>
      </AccountLayout>
    </>
  );
}

// Stat Card Component
function StatCard({ title, value, icon: Icon, color }) {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    yellow: 'bg-yellow-100 text-yellow-600',
    green: 'bg-green-100 text-green-600',
    purple: 'bg-purple-100 text-purple-600'
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
        </div>
      </div>
    </div>
  );
}

import React from 'react';
import Head from 'next/head';
import { RepairLayout } from '../../components/Layout/Layout';
import {
  ShieldCheckIcon,
  ClockIcon,
  DocumentTextIcon,
  PhoneIcon,
  EnvelopeIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline';

export default function RepairWarranty() {
  return (
    <RepairLayout
      title="Repair Warranty Information | Midas Technical"
      description="Comprehensive warranty coverage for all repair services. Learn about our 90-day warranty, what's covered, and how to make a warranty claim."
      keywords="repair warranty, device repair warranty, warranty coverage, warranty claim, repair guarantee"
    >
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <ShieldCheckIcon className="h-16 w-16 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Comprehensive Repair Warranty
            </h1>
            <p className="text-xl md:text-2xl mb-8">
              90-day warranty on all repairs with genuine parts guarantee and expert workmanship
            </p>
          </div>
        </section>

        {/* Warranty Overview */}
        <section className="py-16 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="bg-blue-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <ClockIcon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">90-Day Coverage</h3>
                <p className="text-gray-600">Full warranty protection for 90 days from repair completion</p>
              </div>
              
              <div className="text-center">
                <div className="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <WrenchScrewdriverIcon className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Genuine Parts</h3>
                <p className="text-gray-600">Only authentic, high-quality replacement parts used</p>
              </div>
              
              <div className="text-center">
                <div className="bg-purple-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <CheckCircleIcon className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Expert Service</h3>
                <p className="text-gray-600">Certified technicians with years of experience</p>
              </div>
            </div>
          </div>
        </section>

        {/* What's Covered */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center mb-12">What's Covered Under Warranty</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-xl font-semibold mb-4 text-green-600">✅ Covered Items</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Defective replacement parts</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Workmanship issues related to the repair</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Functionality problems with repaired components</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Installation-related issues</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Software issues caused by repair process</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Free re-repair if same issue recurs</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-xl font-semibold mb-4 text-red-600">❌ Not Covered</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Physical damage after repair completion</span>
                  </li>
                  <li className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Water damage occurring after repair</span>
                  </li>
                  <li className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Issues unrelated to the original repair</span>
                  </li>
                  <li className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Normal wear and tear</span>
                  </li>
                  <li className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Damage from unauthorized repairs</span>
                  </li>
                  <li className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span>Software issues not caused by repair</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Warranty Terms */}
        <section className="py-16 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center mb-12">Warranty Terms & Conditions</h2>
            
            <div className="prose max-w-none">
              <div className="bg-blue-50 border-l-4 border-blue-400 p-6 mb-8">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">Important Notice</h3>
                <p className="text-blue-700">
                  This warranty is provided in addition to your statutory rights and does not affect your legal rights as a consumer.
                </p>
              </div>

              <h3 className="text-xl font-semibold mb-4">1. Warranty Period</h3>
              <p className="mb-6">
                The warranty period begins on the date of repair completion and lasts for 90 days. For certain complex repairs, 
                an extended warranty period may be offered at the discretion of Midas Technical.
              </p>

              <h3 className="text-xl font-semibold mb-4">2. Warranty Coverage</h3>
              <p className="mb-6">
                This warranty covers defects in materials and workmanship related to the specific repair performed. 
                It includes free repair or replacement of defective parts and correction of workmanship issues.
              </p>

              <h3 className="text-xl font-semibold mb-4">3. Warranty Claims</h3>
              <p className="mb-6">
                To make a warranty claim, contact our repair department within the warranty period. 
                You must provide your original repair ticket number and describe the issue. 
                We may require the device to be brought in for inspection.
              </p>

              <h3 className="text-xl font-semibold mb-4">4. Warranty Limitations</h3>
              <p className="mb-6">
                This warranty does not cover damage caused by misuse, accidents, normal wear and tear, 
                or issues unrelated to the original repair. The warranty is void if the device has been 
                repaired by another service provider after our repair.
              </p>

              <h3 className="text-xl font-semibold mb-4">5. Remedy Limitation</h3>
              <p className="mb-6">
                Our liability under this warranty is limited to repair or replacement of the defective part or service. 
                We are not liable for any consequential damages, data loss, or other indirect damages.
              </p>
            </div>
          </div>
        </section>

        {/* How to Make a Claim */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center mb-12">How to Make a Warranty Claim</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  1
                </div>
                <h3 className="text-lg font-semibold mb-2">Contact Us</h3>
                <p className="text-gray-600">
                  Call or email our repair department with your ticket number and issue description
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  2
                </div>
                <h3 className="text-lg font-semibold mb-2">Assessment</h3>
                <p className="text-gray-600">
                  We'll assess your claim and may request device inspection if necessary
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  3
                </div>
                <h3 className="text-lg font-semibold mb-2">Resolution</h3>
                <p className="text-gray-600">
                  If claim is valid, we'll repair or replace the defective component at no charge
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-16 bg-blue-600 text-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-8">Need to Make a Warranty Claim?</h2>
            <p className="text-xl mb-8">Our repair support team is here to help with your warranty needs</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto">
              <div className="flex items-center justify-center">
                <PhoneIcon className="h-6 w-6 mr-3" />
                <div>
                  <p className="font-semibold">Phone Support</p>
                  <p>+****************</p>
                </div>
              </div>
              
              <div className="flex items-center justify-center">
                <EnvelopeIcon className="h-6 w-6 mr-3" />
                <div>
                  <p className="font-semibold">Email Support</p>
                  <p><EMAIL></p>
                </div>
              </div>
            </div>
            
            <div className="mt-8">
              <p className="text-lg">
                <ClockIcon className="h-5 w-5 inline mr-2" />
                Support Hours: Monday-Friday 9AM-10PM EST
              </p>
            </div>
          </div>
        </section>
      </div>
    </RepairLayout>
  );
}

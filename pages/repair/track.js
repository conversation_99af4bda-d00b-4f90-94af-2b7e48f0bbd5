import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  PhoneIcon,
  EnvelopeIcon,
  DocumentTextIcon,
  WrenchScrewdriverIcon,
  TruckIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';

export default function RepairTrack() {
  const router = useRouter();
  const [ticketNumber, setTicketNumber] = useState('');
  const [ticket, setTicket] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    // Pre-fill ticket number from URL parameter
    if (router.query.ticket) {
      setTicketNumber(router.query.ticket);
      searchTicket(router.query.ticket);
    }
  }, [router.query.ticket]);

  const searchTicket = async (ticketNum = ticketNumber) => {
    if (!ticketNum.trim()) {
      setError('Please enter a ticket number');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/repair/tickets?ticket_number=${encodeURIComponent(ticketNum)}`);
      const data = await response.json();

      if (data.success && data.data.length > 0) {
        setTicket(data.data[0]);
      } else {
        setError('Ticket not found. Please check your ticket number and try again.');
        setTicket(null);
      }
    } catch (error) {
      console.error('Error searching ticket:', error);
      setError('Failed to search for ticket. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const statusColors = {
      'submitted': 'bg-blue-100 text-blue-800',
      'received': 'bg-yellow-100 text-yellow-800',
      'diagnosed': 'bg-purple-100 text-purple-800',
      'approved': 'bg-green-100 text-green-800',
      'in_progress': 'bg-orange-100 text-orange-800',
      'testing': 'bg-indigo-100 text-indigo-800',
      'completed': 'bg-green-100 text-green-800',
      'ready_pickup': 'bg-blue-100 text-blue-800',
      'delivered': 'bg-green-100 text-green-800',
      'cancelled': 'bg-red-100 text-red-800'
    };
    return statusColors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status) => {
    const statusIcons = {
      'submitted': DocumentTextIcon,
      'received': CheckCircleIcon,
      'diagnosed': MagnifyingGlassIcon,
      'approved': CheckCircleIcon,
      'in_progress': WrenchScrewdriverIcon,
      'testing': ClockIcon,
      'completed': CheckCircleIcon,
      'ready_pickup': TruckIcon,
      'delivered': CheckCircleIcon,
      'cancelled': ExclamationTriangleIcon
    };
    return statusIcons[status] || ClockIcon;
  };

  const getStatusSteps = () => {
    const allSteps = [
      { key: 'submitted', label: 'Submitted', description: 'Repair request submitted' },
      { key: 'received', label: 'Received', description: 'Device received at our facility' },
      { key: 'diagnosed', label: 'Diagnosed', description: 'Initial diagnosis completed' },
      { key: 'approved', label: 'Approved', description: 'Repair approved and scheduled' },
      { key: 'in_progress', label: 'In Progress', description: 'Repair work in progress' },
      { key: 'testing', label: 'Testing', description: 'Quality testing and verification' },
      { key: 'completed', label: 'Completed', description: 'Repair completed successfully' },
      { key: 'ready_pickup', label: 'Ready', description: 'Ready for pickup/delivery' },
      { key: 'delivered', label: 'Delivered', description: 'Device returned to customer' }
    ];

    if (!ticket) return allSteps;

    const currentStatusIndex = allSteps.findIndex(step => step.key === ticket.status);
    
    return allSteps.map((step, index) => ({
      ...step,
      completed: index <= currentStatusIndex,
      current: index === currentStatusIndex
    }));
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <>
      <Head>
        <title>Track Your Repair | Midas Technical</title>
        <meta name="description" content="Track the status of your device repair with real-time updates and detailed progress information." />
      </Head>

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Track Your Repair</h1>
            <p className="text-lg text-gray-600">Enter your ticket number to check the status of your repair</p>
          </div>

          {/* Search Form */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <label htmlFor="ticketNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  Ticket Number
                </label>
                <input
                  type="text"
                  id="ticketNumber"
                  value={ticketNumber}
                  onChange={(e) => setTicketNumber(e.target.value)}
                  placeholder="Enter your ticket number (e.g., RPR-1234567890-123)"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  onKeyPress={(e) => e.key === 'Enter' && searchTicket()}
                />
              </div>
              <div className="flex items-end">
                <button
                  onClick={() => searchTicket()}
                  disabled={loading}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Searching...
                    </>
                  ) : (
                    <>
                      <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                      Track Repair
                    </>
                  )}
                </button>
              </div>
            </div>

            {error && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  <p className="ml-2 text-sm text-red-600">{error}</p>
                </div>
              </div>
            )}
          </div>

          {/* Ticket Details */}
          {ticket && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* Ticket Overview */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                      Ticket #{ticket.ticket_number}
                    </h2>
                    <p className="text-gray-600">
                      {ticket.device_brand} {ticket.device_model} • Created {formatDate(ticket.created_at)}
                    </p>
                  </div>
                  <div className="mt-4 lg:mt-0">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(ticket.status)}`}>
                      {ticket.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Customer</h3>
                    <p className="text-gray-900">{ticket.customer_name}</p>
                    <div className="flex items-center mt-1 text-sm text-gray-600">
                      <EnvelopeIcon className="h-4 w-4 mr-1" />
                      {ticket.customer_email}
                    </div>
                    {ticket.customer_phone && (
                      <div className="flex items-center mt-1 text-sm text-gray-600">
                        <PhoneIcon className="h-4 w-4 mr-1" />
                        {ticket.customer_phone}
                      </div>
                    )}
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Device</h3>
                    <p className="text-gray-900">{ticket.device_name || `${ticket.device_brand} ${ticket.device_model}`}</p>
                    {ticket.device_serial && (
                      <p className="text-sm text-gray-600">Serial: {ticket.device_serial}</p>
                    )}
                    {ticket.device_imei && (
                      <p className="text-sm text-gray-600">IMEI: {ticket.device_imei}</p>
                    )}
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Pricing</h3>
                    {ticket.estimated_cost && (
                      <p className="text-gray-900">
                        Estimated: {formatPrice(ticket.estimated_cost)}
                      </p>
                    )}
                    {ticket.final_cost && (
                      <p className="text-gray-900">
                        Final: {formatPrice(ticket.final_cost)}
                      </p>
                    )}
                    <p className="text-sm text-gray-600">
                      Payment: {ticket.payment_status.replace('_', ' ')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Progress Timeline */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-6">Repair Progress</h3>
                
                <div className="space-y-4">
                  {getStatusSteps().map((step, index) => {
                    const IconComponent = getStatusIcon(step.key);
                    return (
                      <div key={step.key} className="flex items-center">
                        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                          step.completed ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
                        }`}>
                          {step.completed ? (
                            <CheckCircleIcon className="w-5 h-5" />
                          ) : (
                            <IconComponent className="w-5 h-5" />
                          )}
                        </div>
                        
                        <div className="ml-4 flex-1">
                          <p className={`text-sm font-medium ${
                            step.current ? 'text-blue-600' : step.completed ? 'text-gray-900' : 'text-gray-500'
                          }`}>
                            {step.label}
                          </p>
                          <p className="text-sm text-gray-600">{step.description}</p>
                        </div>

                        {step.current && (
                          <div className="flex-shrink-0">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Current
                            </span>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Services and Timeline */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Services */}
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Services</h3>
                  <div className="space-y-3">
                    {ticket.services && ticket.services.map((service, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                        <div>
                          <p className="font-medium text-gray-900">{service.service_name}</p>
                          <p className="text-sm text-gray-600">Qty: {service.quantity}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">{formatPrice(service.total_price)}</p>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                            {service.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Timeline and Notes */}
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Timeline & Notes</h3>
                  
                  {ticket.estimated_completion && (
                    <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center">
                        <ClockIcon className="h-5 w-5 text-blue-600 mr-2" />
                        <div>
                          <p className="font-medium text-gray-900">Estimated Completion</p>
                          <p className="text-sm text-gray-600">{formatDate(ticket.estimated_completion)}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {ticket.warranty_expires && (
                    <div className="mb-4 p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center">
                        <ShieldCheckIcon className="h-5 w-5 text-green-600 mr-2" />
                        <div>
                          <p className="font-medium text-gray-900">Warranty Expires</p>
                          <p className="text-sm text-gray-600">{formatDate(ticket.warranty_expires)}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {ticket.problem_description && (
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-900 mb-2">Problem Description</h4>
                      <p className="text-sm text-gray-600">{ticket.problem_description}</p>
                    </div>
                  )}

                  {ticket.diagnosis_notes && (
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-900 mb-2">Diagnosis Notes</h4>
                      <p className="text-sm text-gray-600">{ticket.diagnosis_notes}</p>
                    </div>
                  )}

                  {ticket.repair_notes && (
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-900 mb-2">Repair Notes</h4>
                      <p className="text-sm text-gray-600">{ticket.repair_notes}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Need Help?</h3>
                <div className="flex flex-col sm:flex-row gap-4">
                  <a
                    href={`mailto:<EMAIL>?subject=Repair Ticket ${ticket.ticket_number}`}
                    className="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <EnvelopeIcon className="h-4 w-4 mr-2" />
                    Email Support
                  </a>
                  <a
                    href="tel:+1234567890"
                    className="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <PhoneIcon className="h-4 w-4 mr-2" />
                    Call Support
                  </a>
                  {ticket.status === 'ready_pickup' && (
                    <button className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                      <TruckIcon className="h-4 w-4 mr-2" />
                      Schedule Pickup
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </>
  );
}

import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import {
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon,
  CpuChipIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export default function RepairQuote() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    device_type_id: '',
    services: [],
    customer_name: '',
    customer_email: '',
    customer_phone: '',
    device_brand: '',
    device_model: '',
    device_serial: '',
    device_condition: '',
    problem_description: '',
    rush_service: false,
    warranty_extension: false
  });
  const [devices, setDevices] = useState([]);
  const [services, setServices] = useState([]);
  const [estimate, setEstimate] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    fetchDeviceTypes();
  }, []);

  useEffect(() => {
    if (formData.device_type_id) {
      fetchRepairServices();
    }
  }, [formData.device_type_id]);

  useEffect(() => {
    // Pre-select service from URL parameter
    if (router.query.service && services.length > 0) {
      const serviceSlug = router.query.service;
      const service = services.find(s => s.slug === serviceSlug);
      if (service && !formData.services.includes(service.id)) {
        setFormData(prev => ({
          ...prev,
          services: [...prev.services, service.id]
        }));
      }
    }
  }, [router.query.service, services]);

  const fetchDeviceTypes = async () => {
    try {
      const response = await fetch('/api/repair/devices');
      const data = await response.json();
      if (data.success) {
        setDevices(data.data);
      }
    } catch (error) {
      console.error('Error fetching device types:', error);
    }
  };

  const fetchRepairServices = async () => {
    try {
      const selectedDevice = devices.find(d => d.id === parseInt(formData.device_type_id));
      const params = new URLSearchParams();
      if (selectedDevice) {
        params.append('device_type', selectedDevice.slug);
      }

      const response = await fetch(`/api/repair/services?${params}`);
      const data = await response.json();
      if (data.success) {
        setServices(data.data);
      }
    } catch (error) {
      console.error('Error fetching repair services:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleServiceToggle = (serviceId) => {
    setFormData(prev => ({
      ...prev,
      services: prev.services.includes(serviceId)
        ? prev.services.filter(id => id !== serviceId)
        : [...prev.services, serviceId]
    }));
  };

  const validateStep = (stepNumber) => {
    const newErrors = {};

    switch (stepNumber) {
      case 1:
        if (!formData.device_type_id) newErrors.device_type_id = 'Please select a device type';
        if (formData.services.length === 0) newErrors.services = 'Please select at least one service';
        break;
      case 2:
        if (!formData.customer_name.trim()) newErrors.customer_name = 'Name is required';
        if (!formData.customer_email.trim()) newErrors.customer_email = 'Email is required';
        if (!formData.problem_description.trim()) newErrors.problem_description = 'Problem description is required';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const generateEstimate = async () => {
    if (!validateStep(2)) return;

    setLoading(true);
    try {
      const response = await fetch('/api/repair/estimate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device_type_id: parseInt(formData.device_type_id),
          services: formData.services,
          rush_service: formData.rush_service,
          warranty_extension: formData.warranty_extension
        }),
      });

      const data = await response.json();
      if (data.success) {
        setEstimate(data.data);
        setStep(3);
      } else {
        setErrors({ general: data.message || 'Failed to generate estimate' });
      }
    } catch (error) {
      console.error('Error generating estimate:', error);
      setErrors({ general: 'Failed to generate estimate. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const createRepairTicket = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/repair/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          device_type_id: parseInt(formData.device_type_id),
          requested_services: formData.services
        }),
      });

      const data = await response.json();
      if (data.success) {
        router.push(`/repair/track?ticket=${data.data.ticket_number}`);
      } else {
        setErrors({ general: data.message || 'Failed to create repair ticket' });
      }
    } catch (error) {
      console.error('Error creating repair ticket:', error);
      setErrors({ general: 'Failed to create repair ticket. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const getDeviceIcon = (category) => {
    const iconMap = {
      'phone': DevicePhoneMobileIcon,
      'tablet': DeviceTabletIcon,
      'laptop': ComputerDesktopIcon,
      'desktop': ComputerDesktopIcon,
      'gaming': CpuChipIcon
    };
    return iconMap[category] || ComputerDesktopIcon;
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  return (
    <>
      <Head>
        <title>Get Repair Quote | Midas Technical</title>
        <meta name="description" content="Get an instant quote for your device repair. Fast, accurate estimates with transparent pricing." />
      </Head>

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {[1, 2, 3].map((stepNumber) => (
                <div key={stepNumber} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step >= stepNumber ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
                  }`}>
                    {step > stepNumber ? (
                      <CheckCircleIcon className="w-5 h-5" />
                    ) : (
                      stepNumber
                    )}
                  </div>
                  {stepNumber < 3 && (
                    <div className={`w-24 h-1 mx-2 ${
                      step > stepNumber ? 'bg-blue-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-600">
              <span>Device & Services</span>
              <span>Contact Info</span>
              <span>Quote & Booking</span>
            </div>
          </div>

          {/* Step 1: Device and Services Selection */}
          {step === 1 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Select Your Device and Services</h2>

              {/* Device Selection */}
              <div className="mb-8">
                <label className="block text-sm font-medium text-gray-700 mb-3">Device Type</label>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {devices.map((device) => {
                    const IconComponent = getDeviceIcon(device.category);
                    return (
                      <div
                        key={device.id}
                        className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                          formData.device_type_id === device.id.toString()
                            ? 'border-blue-600 bg-blue-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                        onClick={() => handleInputChange('device_type_id', device.id.toString())}
                      >
                        <div className="flex items-center">
                          <IconComponent className="h-6 w-6 text-blue-600 mr-3" />
                          <div>
                            <p className="font-medium text-gray-900">{device.name}</p>
                            <p className="text-sm text-gray-500">{device.brand}</p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                {errors.device_type_id && (
                  <p className="mt-2 text-sm text-red-600">{errors.device_type_id}</p>
                )}
              </div>

              {/* Services Selection */}
              {formData.device_type_id && (
                <div className="mb-8">
                  <label className="block text-sm font-medium text-gray-700 mb-3">Select Services</label>
                  <div className="space-y-4">
                    {services.map((service) => {
                      const isSelected = formData.services.includes(service.id);
                      const pricing = service.pricing.find(p => p.device_type_id === parseInt(formData.device_type_id));
                      const price = pricing ? pricing.price : service.base_price;

                      return (
                        <div
                          key={service.id}
                          className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                            isSelected ? 'border-blue-600 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                          }`}
                          onClick={() => handleServiceToggle(service.id)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <h3 className="font-medium text-gray-900">{service.name}</h3>
                              <p className="text-sm text-gray-600 mt-1">{service.short_description}</p>
                              <div className="flex items-center mt-2 text-sm text-gray-500">
                                <ClockIcon className="h-4 w-4 mr-1" />
                                {service.estimated_time_hours}h
                                <ShieldCheckIcon className="h-4 w-4 ml-4 mr-1" />
                                {service.warranty_days} days warranty
                              </div>
                            </div>
                            <div className="text-right ml-4">
                              <p className="text-lg font-bold text-blue-600">{formatPrice(price)}</p>
                              {service.requires_diagnosis && (
                                <p className="text-xs text-gray-500">+ diagnosis fee</p>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  {errors.services && (
                    <p className="mt-2 text-sm text-red-600">{errors.services}</p>
                  )}
                </div>
              )}

              <div className="flex justify-end">
                <button
                  onClick={handleNext}
                  disabled={!formData.device_type_id || formData.services.length === 0}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  Next Step
                </button>
              </div>
            </motion.div>
          )}

          {/* Step 2: Contact Information */}
          {step === 2 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact Information & Device Details</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Contact Information */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                      <input
                        type="text"
                        value={formData.customer_name}
                        onChange={(e) => handleInputChange('customer_name', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter your full name"
                      />
                      {errors.customer_name && (
                        <p className="mt-1 text-sm text-red-600">{errors.customer_name}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                      <input
                        type="email"
                        value={formData.customer_email}
                        onChange={(e) => handleInputChange('customer_email', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter your email address"
                      />
                      {errors.customer_email && (
                        <p className="mt-1 text-sm text-red-600">{errors.customer_email}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                      <input
                        type="tel"
                        value={formData.customer_phone}
                        onChange={(e) => handleInputChange('customer_phone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter your phone number"
                      />
                    </div>
                  </div>
                </div>

                {/* Device Details */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Device Details</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Device Brand</label>
                      <input
                        type="text"
                        value={formData.device_brand}
                        onChange={(e) => handleInputChange('device_brand', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., Apple, Samsung, Dell"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Device Model</label>
                      <input
                        type="text"
                        value={formData.device_model}
                        onChange={(e) => handleInputChange('device_model', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., iPhone 15 Pro, Galaxy S24"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Serial Number (if available)</label>
                      <input
                        type="text"
                        value={formData.device_serial}
                        onChange={(e) => handleInputChange('device_serial', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Device serial number"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Problem Description */}
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">Problem Description *</label>
                <textarea
                  value={formData.problem_description}
                  onChange={(e) => handleInputChange('problem_description', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Please describe the issue with your device in detail..."
                />
                {errors.problem_description && (
                  <p className="mt-1 text-sm text-red-600">{errors.problem_description}</p>
                )}
              </div>

              {/* Additional Options */}
              <div className="mt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Options</h3>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.rush_service}
                      onChange={(e) => handleInputChange('rush_service', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Rush Service (+50% fee, faster completion)
                    </span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.warranty_extension}
                      onChange={(e) => handleInputChange('warranty_extension', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Extended Warranty (+15% fee, 6 months additional coverage)
                    </span>
                  </label>
                </div>
              </div>

              {errors.general && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                    <p className="ml-2 text-sm text-red-600">{errors.general}</p>
                  </div>
                </div>
              )}

              <div className="flex justify-between mt-8">
                <button
                  onClick={handleBack}
                  className="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400 transition-colors"
                >
                  Back
                </button>
                <button
                  onClick={generateEstimate}
                  disabled={loading}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Generating...
                    </>
                  ) : (
                    'Generate Quote'
                  )}
                </button>
              </div>
            </motion.div>
          )}

          {/* Step 3: Quote and Booking */}
          {step === 3 && estimate && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Repair Quote</h2>

              {/* Device Summary */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 className="font-medium text-gray-900 mb-2">Device Information</h3>
                <p className="text-gray-600">{estimate.device.name} - {estimate.device.brand} {estimate.device.model}</p>
              </div>

              {/* Services Summary */}
              <div className="mb-6">
                <h3 className="font-medium text-gray-900 mb-3">Selected Services</h3>
                <div className="space-y-3">
                  {estimate.services.map((service, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200">
                      <div>
                        <p className="font-medium text-gray-900">{service.name}</p>
                        <p className="text-sm text-gray-600">
                          Est. {service.estimated_time_hours}h • {service.warranty_days} days warranty
                        </p>
                      </div>
                      <p className="font-medium text-gray-900">{formatPrice(service.total_cost)}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Cost Breakdown */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 className="font-medium text-gray-900 mb-3">Cost Breakdown</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Labor Cost:</span>
                    <span>{formatPrice(estimate.cost_breakdown.labor_cost)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Parts Cost:</span>
                    <span>{formatPrice(estimate.cost_breakdown.parts_cost)}</span>
                  </div>
                  {estimate.cost_breakdown.additional_fees > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Additional Fees:</span>
                      <span>{formatPrice(estimate.cost_breakdown.additional_fees)}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subtotal:</span>
                    <span>{formatPrice(estimate.cost_breakdown.subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tax ({(estimate.cost_breakdown.tax_rate * 100).toFixed(1)}%):</span>
                    <span>{formatPrice(estimate.cost_breakdown.tax_amount)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t border-gray-300 pt-2">
                    <span>Total:</span>
                    <span className="text-blue-600">{formatPrice(estimate.cost_breakdown.total_amount)}</span>
                  </div>
                </div>
              </div>

              {/* Timeline */}
              <div className="bg-blue-50 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <ClockIcon className="h-5 w-5 text-blue-600 mr-2" />
                  <div>
                    <p className="font-medium text-gray-900">Estimated Completion</p>
                    <p className="text-gray-600">
                      {new Date(estimate.estimated_completion).toLocaleDateString()} at{' '}
                      {new Date(estimate.estimated_completion).toLocaleTimeString()}
                    </p>
                    <p className="text-sm text-gray-500">
                      Total estimated time: {estimate.estimated_time_hours} hours
                    </p>
                  </div>
                </div>
              </div>

              {errors.general && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                    <p className="ml-2 text-sm text-red-600">{errors.general}</p>
                  </div>
                </div>
              )}

              <div className="flex justify-between">
                <button
                  onClick={handleBack}
                  className="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400 transition-colors"
                >
                  Back
                </button>
                <button
                  onClick={createRepairTicket}
                  disabled={loading}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating Ticket...
                    </>
                  ) : (
                    'Book Repair Service'
                  )}
                </button>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </>
  );
}

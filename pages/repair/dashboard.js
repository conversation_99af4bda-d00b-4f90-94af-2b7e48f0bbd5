import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import Layout from '../../components/Layout/Layout';
import TicketSubmissionForm from '../../components/Repair/TicketSubmissionForm';
import RepairStatusTracker from '../../components/Repair/RepairStatusTracker';
import AdminDashboard from '../../components/Repair/AdminDashboard';
import {
  WrenchScrewdriverIcon,
  ClockIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import styles from '../../styles/RepairPage.module.css';

const RepairDashboard = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(router.query.tab || 'overview');

  // Update URL when tab changes
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    router.push(`/repair/dashboard?tab=${tab}`, undefined, { shallow: true });
  };

  const services = [
    {
      icon: <DevicePhoneMobileIcon className={styles.serviceIcon} />,
      title: 'Phone Repair',
      description: 'Screen replacement, battery, charging port, camera, and more',
      features: ['Same-day service available', 'Genuine parts', '90-day warranty'],
      startingPrice: '$49'
    },
    {
      icon: <DeviceTabletIcon className={styles.serviceIcon} />,
      title: 'Tablet Repair',
      description: 'iPad and Android tablet repairs with expert precision',
      features: ['Screen digitizer repair', 'Battery replacement', 'Port cleaning'],
      startingPrice: '$79'
    },
    {
      icon: <ComputerDesktopIcon className={styles.serviceIcon} />,
      title: 'Laptop Repair',
      description: 'Hardware and software issues for all laptop brands',
      features: ['Motherboard repair', 'Data recovery', 'Performance optimization'],
      startingPrice: '$99'
    }
  ];

  const features = [
    {
      icon: <ClockIcon className={styles.featureIcon} />,
      title: 'Fast Turnaround',
      description: 'Most repairs completed within 24-48 hours'
    },
    {
      icon: <ShieldCheckIcon className={styles.featureIcon} />,
      title: 'Warranty Included',
      description: '90-day warranty on all repairs and parts'
    },
    {
      icon: <CurrencyDollarIcon className={styles.featureIcon} />,
      title: 'Competitive Pricing',
      description: 'Fair, transparent pricing with no hidden fees'
    },
    {
      icon: <CheckCircleIcon className={styles.featureIcon} />,
      title: 'Expert Technicians',
      description: 'Certified professionals with years of experience'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'submit':
        return <TicketSubmissionForm />;
      case 'track':
        return <RepairStatusTracker />;
      case 'admin':
        if (session?.user?.is_admin || session?.user?.is_repair_admin) {
          return <AdminDashboard />;
        } else {
          return (
            <div className={styles.unauthorized}>
              <ExclamationTriangleIcon className={styles.unauthorizedIcon} />
              <h2>Access Denied</h2>
              <p>You don't have permission to access the admin dashboard.</p>
            </div>
          );
        }
      default:
        return (
          <div className={styles.overview}>
            {/* Hero Section */}
            <div className={styles.hero}>
              <div className={styles.heroContent}>
                <h1>Professional Device Repair Services</h1>
                <p>Expert technicians, genuine parts, and comprehensive warranties for all your device repair needs.</p>
                <div className={styles.heroActions}>
                  <button 
                    onClick={() => handleTabChange('submit')}
                    className={styles.primaryButton}
                  >
                    <WrenchScrewdriverIcon className={styles.buttonIcon} />
                    Submit Repair Request
                  </button>
                  <button 
                    onClick={() => handleTabChange('track')}
                    className={styles.secondaryButton}
                  >
                    <ClockIcon className={styles.buttonIcon} />
                    Track Your Repair
                  </button>
                </div>
              </div>
            </div>

            {/* Services Section */}
            <div className={styles.servicesSection}>
              <div className={styles.container}>
                <h2>Our Repair Services</h2>
                <div className={styles.servicesGrid}>
                  {services.map((service, index) => (
                    <div key={index} className={styles.serviceCard}>
                      {service.icon}
                      <h3>{service.title}</h3>
                      <p>{service.description}</p>
                      <div className={styles.servicePrice}>
                        Starting at {service.startingPrice}
                      </div>
                      <ul className={styles.featureList}>
                        {service.features.map((feature, idx) => (
                          <li key={idx}>
                            <CheckCircleIcon className={styles.checkIcon} />
                            {feature}
                          </li>
                        ))}
                      </ul>
                      <button 
                        onClick={() => handleTabChange('submit')}
                        className={styles.serviceButton}
                      >
                        Get Quote
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Features Section */}
            <div className={styles.featuresSection}>
              <div className={styles.container}>
                <h2>Why Choose Our Repair Services?</h2>
                <div className={styles.featuresGrid}>
                  {features.map((feature, index) => (
                    <div key={index} className={styles.featureCard}>
                      {feature.icon}
                      <h3>{feature.title}</h3>
                      <p>{feature.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Process Section */}
            <div className={styles.processSection}>
              <div className={styles.container}>
                <h2>Our Repair Process</h2>
                <div className={styles.processSteps}>
                  <div className={styles.processStep}>
                    <div className={styles.stepNumber}>1</div>
                    <h3>Submit Request</h3>
                    <p>Fill out our repair form with device details and problem description</p>
                  </div>
                  <div className={styles.processStep}>
                    <div className={styles.stepNumber}>2</div>
                    <h3>Free Diagnosis</h3>
                    <p>Our technicians perform a comprehensive diagnosis of your device</p>
                  </div>
                  <div className={styles.processStep}>
                    <div className={styles.stepNumber}>3</div>
                    <h3>Approve Estimate</h3>
                    <p>Review and approve the repair estimate before we begin work</p>
                  </div>
                  <div className={styles.processStep}>
                    <div className={styles.stepNumber}>4</div>
                    <h3>Expert Repair</h3>
                    <p>Certified technicians perform the repair using genuine parts</p>
                  </div>
                  <div className={styles.processStep}>
                    <div className={styles.stepNumber}>5</div>
                    <h3>Quality Testing</h3>
                    <p>Thorough testing ensures your device works perfectly</p>
                  </div>
                  <div className={styles.processStep}>
                    <div className={styles.stepNumber}>6</div>
                    <h3>Pickup/Delivery</h3>
                    <p>Convenient pickup or delivery options with warranty included</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className={styles.quickActions}>
              <div className={styles.container}>
                <h2>Quick Actions</h2>
                <div className={styles.actionsGrid}>
                  <button 
                    onClick={() => handleTabChange('submit')}
                    className={styles.actionCard}
                  >
                    <WrenchScrewdriverIcon className={styles.actionIcon} />
                    <h3>Submit New Repair</h3>
                    <p>Start a new repair request</p>
                  </button>
                  <button 
                    onClick={() => handleTabChange('track')}
                    className={styles.actionCard}
                  >
                    <ClockIcon className={styles.actionIcon} />
                    <h3>Track Existing Repair</h3>
                    <p>Check the status of your repair</p>
                  </button>
                  <button 
                    onClick={() => router.push('/repair/quote')}
                    className={styles.actionCard}
                  >
                    <CurrencyDollarIcon className={styles.actionIcon} />
                    <h3>Get Estimate</h3>
                    <p>Get a quick repair estimate</p>
                  </button>
                  <button 
                    onClick={() => router.push('/contact?department=repair')}
                    className={styles.actionCard}
                  >
                    <CheckCircleIcon className={styles.actionIcon} />
                    <h3>Contact Support</h3>
                    <p>Get help with your repair</p>
                  </button>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  if (status === 'loading') {
    return (
      <Layout title="Loading...">
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading repair dashboard...</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout
      title="Repair Dashboard | Midas Technical"
      description="Comprehensive repair dashboard for submitting tickets, tracking repairs, and managing repair services."
      keywords="repair dashboard, device repair, repair tracking, repair management"
    >
      <div className={styles.repairPage}>
        {/* Navigation Tabs */}
        <div className={styles.tabNavigation}>
          <div className={styles.container}>
            <div className={styles.tabs}>
              <button 
                className={`${styles.tab} ${activeTab === 'overview' ? styles.active : ''}`}
                onClick={() => handleTabChange('overview')}
              >
                Overview
              </button>
              <button 
                className={`${styles.tab} ${activeTab === 'submit' ? styles.active : ''}`}
                onClick={() => handleTabChange('submit')}
              >
                Submit Repair
              </button>
              <button 
                className={`${styles.tab} ${activeTab === 'track' ? styles.active : ''}`}
                onClick={() => handleTabChange('track')}
              >
                Track Repair
              </button>
              {(session?.user?.is_admin || session?.user?.is_repair_admin) && (
                <button 
                  className={`${styles.tab} ${activeTab === 'admin' ? styles.active : ''}`}
                  onClick={() => handleTabChange('admin')}
                >
                  Admin Dashboard
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className={styles.tabContent}>
          {renderTabContent()}
        </div>
      </div>
    </Layout>
  );
};

export default RepairDashboard;

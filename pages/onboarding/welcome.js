import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { 
  CheckCircleIcon, 
  GiftIcon, 
  ShoppingBagIcon,
  CogIcon,
  UserGroupIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';

export default function WelcomePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [onboardingData, setOnboardingData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [recommendations, setRecommendations] = useState([]);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    if (session?.user) {
      fetchOnboardingData();
      fetchRecommendations();
    }
  }, [session, status]);

  const fetchOnboardingData = async () => {
    try {
      const response = await fetch('/api/onboarding/status');
      if (response.ok) {
        const data = await response.json();
        setOnboardingData(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch onboarding data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchRecommendations = async () => {
    try {
      const response = await fetch('/api/sales/recommendations?type=personalized&limit=4');
      if (response.ok) {
        const data = await response.json();
        setRecommendations(data.data.recommendations);
      }
    } catch (error) {
      console.error('Failed to fetch recommendations:', error);
    }
  };

  const completeOnboardingStep = async (stepType) => {
    try {
      await fetch('/api/onboarding/complete-step', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ stepType })
      });
      fetchOnboardingData();
    } catch (error) {
      console.error('Failed to complete step:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const completedSteps = onboardingData?.completedSteps || [];
  const progressPercentage = (completedSteps.length / 5) * 100;

  return (
    <>
      <Head>
        <title>Welcome to Midas Technical - Get Started</title>
        <meta name="description" content="Welcome to Midas Technical! Complete your setup to get personalized recommendations and exclusive offers." />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <Link href="/" className="text-2xl font-bold text-gray-900">
                Midas Technical
              </Link>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  Welcome, {session?.user?.name || session?.user?.email}!
                </span>
                <Link href="/account" className="text-blue-600 hover:text-blue-500">
                  My Account
                </Link>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Welcome Hero */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-xl p-8 text-white mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold mb-2">
                  Welcome to Midas Technical! 🎉
                </h1>
                <p className="text-blue-100 text-lg">
                  Your one-stop shop for premium electronic components and professional repair services.
                </p>
              </div>
              <div className="hidden md:block">
                <GiftIcon className="h-24 w-24 text-blue-200" />
              </div>
            </div>
          </div>

          {/* Progress Section */}
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">
                Complete Your Setup
              </h2>
              <span className="text-sm text-gray-600">
                {completedSteps.length} of 5 steps completed
              </span>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Email Verification */}
              <OnboardingStep
                title="Verify Email"
                description="Confirm your email address"
                icon={CheckCircleIcon}
                completed={completedSteps.includes('email_verified')}
                action={() => completeOnboardingStep('email_verified')}
                actionText="Verify Now"
              />

              {/* Profile Completion */}
              <OnboardingStep
                title="Complete Profile"
                description="Add shipping and billing info"
                icon={UserGroupIcon}
                completed={completedSteps.includes('profile_completed')}
                link="/account/profile"
                actionText="Complete Profile"
              />

              {/* Browse Products */}
              <OnboardingStep
                title="Explore Products"
                description="Discover our premium components"
                icon={ShoppingBagIcon}
                completed={completedSteps.includes('first_browse')}
                link="/products"
                actionText="Browse Products"
              />

              {/* Learn About Services */}
              <OnboardingStep
                title="Repair Services"
                description="Learn about our professional services"
                icon={CogIcon}
                completed={completedSteps.includes('services_viewed')}
                link="/repair"
                actionText="View Services"
              />

              {/* Educational Content */}
              <OnboardingStep
                title="Learning Center"
                description="Access repair guides and tutorials"
                icon={AcademicCapIcon}
                completed={completedSteps.includes('education_accessed')}
                link="/learn"
                actionText="Start Learning"
              />
            </div>
          </div>

          {/* Personalized Recommendations */}
          {recommendations.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Recommended for You
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {recommendations.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
              <div className="mt-6 text-center">
                <Link 
                  href="/products"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  View All Products
                </Link>
              </div>
            </div>
          )}

          {/* Special Offers */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* First Purchase Offer */}
            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow p-6 text-white">
              <h3 className="text-lg font-semibold mb-2">
                🎁 New Customer Special
              </h3>
              <p className="text-green-100 mb-4">
                Get 15% off your first order with code WELCOME15
              </p>
              <Link 
                href="/products"
                className="inline-flex items-center px-4 py-2 bg-white text-green-600 rounded-md hover:bg-green-50 font-medium"
              >
                Shop Now
              </Link>
            </div>

            {/* Referral Program */}
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow p-6 text-white">
              <h3 className="text-lg font-semibold mb-2">
                👥 Refer Friends & Earn
              </h3>
              <p className="text-purple-100 mb-4">
                Earn $25 store credit for each friend you refer
              </p>
              <Link 
                href="/account/referrals"
                className="inline-flex items-center px-4 py-2 bg-white text-purple-600 rounded-md hover:bg-purple-50 font-medium"
              >
                Start Referring
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Onboarding Step Component
function OnboardingStep({ title, description, icon: Icon, completed, action, link, actionText }) {
  const handleClick = () => {
    if (action) {
      action();
    }
  };

  const content = (
    <div className={`p-4 rounded-lg border-2 transition-all duration-200 ${
      completed 
        ? 'border-green-200 bg-green-50' 
        : 'border-gray-200 bg-white hover:border-blue-200 hover:bg-blue-50'
    }`}>
      <div className="flex items-start space-x-3">
        <div className={`flex-shrink-0 ${
          completed ? 'text-green-600' : 'text-gray-400'
        }`}>
          <Icon className="h-6 w-6" />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className={`text-sm font-medium ${
            completed ? 'text-green-900' : 'text-gray-900'
          }`}>
            {title}
          </h3>
          <p className={`text-sm ${
            completed ? 'text-green-700' : 'text-gray-500'
          }`}>
            {description}
          </p>
          {!completed && (
            <button
              onClick={handleClick}
              className="mt-2 text-xs text-blue-600 hover:text-blue-500 font-medium"
            >
              {actionText}
            </button>
          )}
          {completed && (
            <span className="mt-2 inline-flex items-center text-xs text-green-600 font-medium">
              <CheckCircleIcon className="h-4 w-4 mr-1" />
              Completed
            </span>
          )}
        </div>
      </div>
    </div>
  );

  if (link && !completed) {
    return <Link href={link}>{content}</Link>;
  }

  return content;
}

// Product Card Component
function ProductCard({ product }) {
  return (
    <Link href={product.url} className="group">
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
        <div className="aspect-w-1 aspect-h-1 bg-gray-200">
          <img
            src={product.image || '/placeholder-product.jpg'}
            alt={product.name}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
          />
        </div>
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
            {product.name}
          </h3>
          <p className="mt-1 text-lg font-semibold text-gray-900">
            ${product.price.toFixed(2)}
          </p>
          {product.category && (
            <p className="mt-1 text-xs text-gray-500">
              {product.category}
            </p>
          )}
        </div>
      </div>
    </Link>
  );
}

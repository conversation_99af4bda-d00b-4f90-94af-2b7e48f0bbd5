import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { 
  MagnifyingGlassIcon,
  TruckIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export default function TrackOrderPage() {
  const [trackingInput, setTrackingInput] = useState('');
  const [email, setEmail] = useState('');
  const [trackingData, setTrackingData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchType, setSearchType] = useState('order'); // 'order' or 'tracking'

  useEffect(() => {
    // Check URL parameters for auto-tracking
    const urlParams = new URLSearchParams(window.location.search);
    const orderNumber = urlParams.get('order');
    const trackingNumber = urlParams.get('number');
    
    if (orderNumber) {
      setTrackingInput(orderNumber);
      setSearchType('order');
    } else if (trackingNumber) {
      setTrackingInput(trackingNumber);
      setSearchType('tracking');
      handleTrackOrder(trackingNumber, '', 'tracking');
    }
  }, []);

  const handleTrackOrder = async (input = trackingInput, customerEmail = email, type = searchType) => {
    if (!input.trim()) {
      setError('Please enter an order number or tracking number');
      return;
    }

    setLoading(true);
    setError('');
    setTrackingData(null);

    try {
      const params = new URLSearchParams();
      
      if (type === 'tracking') {
        params.append('trackingNumber', input.trim());
      } else {
        params.append('orderNumber', input.trim());
        if (customerEmail.trim()) {
          params.append('email', customerEmail.trim());
        }
      }

      const response = await fetch(`/api/orders/track?${params}`);
      const data = await response.json();

      if (data.success) {
        setTrackingData(data.data);
      } else {
        setError(data.message || 'Order not found');
      }
    } catch (error) {
      console.error('Tracking error:', error);
      setError('Failed to retrieve tracking information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'delivered':
        return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
      case 'shipped':
      case 'out_for_delivery':
        return <TruckIcon className="w-6 h-6 text-blue-500" />;
      case 'cancelled':
        return <ExclamationTriangleIcon className="w-6 h-6 text-red-500" />;
      default:
        return <ClockIcon className="w-6 h-6 text-yellow-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered':
        return 'text-green-600 bg-green-100';
      case 'shipped':
      case 'out_for_delivery':
        return 'text-blue-600 bg-blue-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-yellow-600 bg-yellow-100';
    }
  };

  return (
    <>
      <Head>
        <title>Track Your Order - Midas Technical</title>
        <meta name="description" content="Track your order status and shipping information from Midas Technical Store." />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link href="/" className="text-2xl font-bold text-gray-900">
                Midas Technical
              </Link>
              <nav className="hidden md:flex space-x-8">
                <Link href="/products" className="text-gray-700 hover:text-gray-900">Products</Link>
                <Link href="/repair" className="text-gray-700 hover:text-gray-900">Repair Services</Link>
                <Link href="/track" className="text-blue-600 font-medium">Track Order</Link>
                <Link href="/contact" className="text-gray-700 hover:text-gray-900">Contact</Link>
              </nav>
            </div>
          </div>
        </header>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Track Your Order</h1>
            <p className="text-lg text-gray-600">
              Enter your order number or tracking number to get real-time updates
            </p>
          </div>

          {/* Search Form */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex flex-col space-y-4">
              {/* Search Type Toggle */}
              <div className="flex space-x-4">
                <button
                  onClick={() => setSearchType('order')}
                  className={`px-4 py-2 rounded-md font-medium ${
                    searchType === 'order'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Order Number
                </button>
                <button
                  onClick={() => setSearchType('tracking')}
                  className={`px-4 py-2 rounded-md font-medium ${
                    searchType === 'tracking'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Tracking Number
                </button>
              </div>

              {/* Input Fields */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder={
                      searchType === 'order' 
                        ? 'Enter your order number (e.g., MDT-2024-001)' 
                        : 'Enter your tracking number'
                    }
                    value={trackingInput}
                    onChange={(e) => setTrackingInput(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                {searchType === 'order' && (
                  <div className="flex-1">
                    <input
                      type="email"
                      placeholder="Email address (optional)"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                )}
                
                <button
                  onClick={() => handleTrackOrder()}
                  disabled={loading}
                  className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {loading ? (
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <MagnifyingGlassIcon className="w-5 h-5" />
                  )}
                  <span>{loading ? 'Tracking...' : 'Track Order'}</span>
                </button>
              </div>
            </div>

            {error && (
              <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
                {error}
              </div>
            )}
          </div>

          {/* Tracking Results */}
          {trackingData && (
            <div className="space-y-6">
              {/* Order Summary */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Order {trackingData.order.order_number}
                  </h2>
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(trackingData.order.status)}`}>
                    {trackingData.order.status.replace('_', ' ').toUpperCase()}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div>
                    <p className="text-sm text-gray-600">Customer</p>
                    <p className="font-medium">{trackingData.order.customer_name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Order Date</p>
                    <p className="font-medium">
                      {new Date(trackingData.order.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Total Amount</p>
                    <p className="font-medium">${parseFloat(trackingData.order.total_amount).toFixed(2)}</p>
                  </div>
                </div>

                {trackingData.order.tracking_number && (
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-blue-600 font-medium">Tracking Number</p>
                        <p className="text-lg font-mono">{trackingData.order.tracking_number}</p>
                        {trackingData.order.carrier && (
                          <p className="text-sm text-gray-600">Carrier: {trackingData.order.carrier}</p>
                        )}
                      </div>
                      {trackingData.trackingUrl && (
                        <a
                          href={trackingData.trackingUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                        >
                          Track on Carrier Site
                        </a>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Order Items */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
                <div className="space-y-4">
                  {trackingData.order.items.map((item, index) => (
                    <div key={index} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-md">
                      <div className="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                        {item.image_url ? (
                          <img src={item.image_url} alt={item.product_name} className="w-full h-full object-cover rounded-md" />
                        ) : (
                          <div className="text-gray-400 text-xs">No Image</div>
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{item.product_name}</h4>
                        <p className="text-sm text-gray-600">SKU: {item.product_sku}</p>
                        <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">${parseFloat(item.unit_price).toFixed(2)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Status History */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order History</h3>
                <div className="space-y-4">
                  {trackingData.statusHistory.map((event, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="flex-shrink-0 mt-1">
                        {getStatusIcon(event.new_status)}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">
                          {event.new_status.replace('_', ' ').toUpperCase()}
                        </p>
                        {event.notes && (
                          <p className="text-sm text-gray-600">{event.notes}</p>
                        )}
                        <p className="text-xs text-gray-500">
                          {new Date(event.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Carrier Tracking */}
              {trackingData.carrierTracking && (
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Shipping Updates</h3>
                  <div className="space-y-4">
                    {trackingData.carrierTracking.trackingHistory.map((event, index) => (
                      <div key={index} className="flex items-start space-x-4">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{event.status}</p>
                          <p className="text-sm text-gray-600">{event.location}</p>
                          <p className="text-xs text-gray-500">
                            {new Date(event.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Help Section */}
          <div className="mt-12 bg-gray-100 rounded-lg p-6 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Need Help?</h3>
            <p className="text-gray-600 mb-4">
              Can't find your order or have questions about shipping?
            </p>
            <Link href="/contact" className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}

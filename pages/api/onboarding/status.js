import { withAuth } from '../../../lib/auth-middleware.js';
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const userId = req.user.id;
    const client = await pool.connect();

    try {
      // Get onboarding status
      const onboardingResult = await client.query(`
        SELECT 
          status, current_step, completed_steps, preferences,
          started_at, completed_at, last_email_sent
        FROM customer_onboarding
        WHERE user_id = $1
      `, [userId]);

      // Get user profile completion
      const profileResult = await client.query(`
        SELECT 
          profile_completion_percentage, onboarding_completed,
          shipping_addresses, billing_addresses
        FROM user_profiles
        WHERE user_id = $1
      `, [userId]);

      // Get email verification status
      const emailResult = await client.query(`
        SELECT email_verified FROM users WHERE id = $1
      `, [userId]);

      // Get recent activity
      const activityResult = await client.query(`
        SELECT event_name, created_at
        FROM analytics_events
        WHERE user_id = $1
        AND event_name IN ('product_view', 'add_to_cart', 'begin_checkout')
        ORDER BY created_at DESC
        LIMIT 10
      `, [userId]);

      const onboarding = onboardingResult.rows[0];
      const profile = profileResult.rows[0];
      const user = emailResult.rows[0];
      const recentActivity = activityResult.rows;

      // Calculate completion status
      const completedSteps = onboarding?.completed_steps || [];
      const emailVerified = user?.email_verified || false;
      const profileCompleted = profile?.profile_completion_percentage >= 80;
      const hasActivity = recentActivity.length > 0;

      // Add automatic completions
      if (emailVerified && !completedSteps.includes('email_verified')) {
        completedSteps.push('email_verified');
      }
      if (profileCompleted && !completedSteps.includes('profile_completed')) {
        completedSteps.push('profile_completed');
      }
      if (hasActivity && !completedSteps.includes('first_browse')) {
        completedSteps.push('first_browse');
      }

      // Update completed steps if changed
      if (onboarding && completedSteps.length > (onboarding.completed_steps || []).length) {
        await client.query(`
          UPDATE customer_onboarding 
          SET completed_steps = $1, updated_at = CURRENT_TIMESTAMP
          WHERE user_id = $2
        `, [JSON.stringify(completedSteps), userId]);
      }

      client.release();

      return res.status(200).json({
        success: true,
        data: {
          status: onboarding?.status || 'not_started',
          currentStep: onboarding?.current_step || 1,
          completedSteps,
          progressPercentage: Math.round((completedSteps.length / 5) * 100),
          emailVerified,
          profileCompleted,
          profileCompletionPercentage: profile?.profile_completion_percentage || 0,
          onboardingCompleted: profile?.onboarding_completed || false,
          startedAt: onboarding?.started_at,
          completedAt: onboarding?.completed_at,
          lastEmailSent: onboarding?.last_email_sent,
          recentActivity: recentActivity.map(activity => ({
            eventName: activity.event_name,
            createdAt: activity.created_at
          }))
        }
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('❌ Get onboarding status error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get onboarding status'
    });
  }
}

export default withAuth(handler, { requireAuth: true });

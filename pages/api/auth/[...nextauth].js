import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import EmailProvider from 'next-auth/providers/email';
import CredentialsProvider from 'next-auth/providers/credentials';
import { Pool } from 'pg';
import bcrypt from 'bcrypt';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export default NextAuth({
  providers: [
    // Google OAuth Provider
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),

    // Email Magic Link Provider
    EmailProvider({
      server: {
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT) || 587,
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      },
      from: process.env.SMTP_FROM || '<EMAIL>',
      maxAge: 24 * 60 * 60, // 24 hours
    }),

    // Credentials Provider for email/password login
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Email and password required');
        }

        try {
          const client = await pool.connect();
          
          const result = await client.query(
            'SELECT id, email, password_hash, first_name, last_name, is_admin, is_repair_admin, email_verified FROM users WHERE email = $1',
            [credentials.email]
          );
          
          client.release();

          if (result.rows.length === 0) {
            throw new Error('No user found with this email');
          }

          const user = result.rows[0];

          // Verify password
          const isValidPassword = await bcrypt.compare(credentials.password, user.password_hash);
          
          if (!isValidPassword) {
            throw new Error('Invalid password');
          }

          return {
            id: user.id.toString(),
            email: user.email,
            name: `${user.first_name} ${user.last_name}`,
            firstName: user.first_name,
            lastName: user.last_name,
            isAdmin: user.is_admin,
            isRepairAdmin: user.is_repair_admin,
            emailVerified: user.email_verified
          };

        } catch (error) {
          console.error('❌ Authentication error:', error);
          throw new Error('Authentication failed');
        }
      }
    })
  ],

  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        const client = await pool.connect();

        // Check if user exists
        const existingUser = await client.query(
          'SELECT id, email_verified FROM users WHERE email = $1',
          [user.email]
        );

        if (existingUser.rows.length === 0) {
          // Create new user
          const result = await client.query(`
            INSERT INTO users (
              email, first_name, last_name, email_verified, 
              provider, provider_id, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
            RETURNING id
          `, [
            user.email,
            user.name?.split(' ')[0] || '',
            user.name?.split(' ').slice(1).join(' ') || '',
            account.provider === 'google' || account.provider === 'email',
            account.provider,
            account.providerAccountId || user.id
          ]);

          user.id = result.rows[0].id.toString();
          user.isAdmin = false;
          user.isRepairAdmin = false;
          user.emailVerified = true;

        } else {
          // Update existing user
          const existingUserData = existingUser.rows[0];
          user.id = existingUserData.id.toString();
          
          // Update email verification if signing in with verified provider
          if ((account.provider === 'google' || account.provider === 'email') && !existingUserData.email_verified) {
            await client.query(
              'UPDATE users SET email_verified = true WHERE id = $1',
              [user.id]
            );
            user.emailVerified = true;
          }
        }

        client.release();
        return true;

      } catch (error) {
        console.error('❌ Sign in callback error:', error);
        return false;
      }
    },

    async jwt({ token, user, account }) {
      // Initial sign in
      if (user) {
        token.id = user.id;
        token.isAdmin = user.isAdmin;
        token.isRepairAdmin = user.isRepairAdmin;
        token.emailVerified = user.emailVerified;
        token.firstName = user.firstName;
        token.lastName = user.lastName;
      }

      // Refresh user data on each request
      if (token.id) {
        try {
          const client = await pool.connect();
          
          const result = await client.query(
            'SELECT is_admin, is_repair_admin, email_verified, first_name, last_name FROM users WHERE id = $1',
            [token.id]
          );
          
          if (result.rows.length > 0) {
            const userData = result.rows[0];
            token.isAdmin = userData.is_admin;
            token.isRepairAdmin = userData.is_repair_admin;
            token.emailVerified = userData.email_verified;
            token.firstName = userData.first_name;
            token.lastName = userData.last_name;
          }
          
          client.release();
        } catch (error) {
          console.error('❌ JWT callback error:', error);
        }
      }

      return token;
    },

    async session({ session, token }) {
      // Send properties to the client
      session.user.id = token.id;
      session.user.isAdmin = token.isAdmin;
      session.user.isRepairAdmin = token.isRepairAdmin;
      session.user.emailVerified = token.emailVerified;
      session.user.firstName = token.firstName;
      session.user.lastName = token.lastName;

      return session;
    },

    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    }
  },

  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
  },

  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },

  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  secret: process.env.NEXTAUTH_SECRET,

  debug: process.env.NODE_ENV === 'development',

  events: {
    async signIn({ user, account, profile, isNewUser }) {
      console.log(`✅ User signed in: ${user.email} via ${account.provider}`);
      
      // Log sign in event
      try {
        const client = await pool.connect();
        await client.query(`
          INSERT INTO user_activity_logs (
            user_id, activity_type, details, ip_address, user_agent, created_at
          ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
        `, [
          user.id,
          'sign_in',
          JSON.stringify({ provider: account.provider, isNewUser }),
          null, // IP address would be captured from request
          null  // User agent would be captured from request
        ]);
        client.release();
      } catch (error) {
        console.error('❌ Failed to log sign in event:', error);
      }
    },

    async signOut({ session, token }) {
      console.log(`✅ User signed out: ${session?.user?.email}`);
    }
  }
});

// Helper function to check if user is admin
export async function isAdmin(userId) {
  try {
    const client = await pool.connect();
    const result = await client.query(
      'SELECT is_admin FROM users WHERE id = $1',
      [userId]
    );
    client.release();
    
    return result.rows.length > 0 && result.rows[0].is_admin;
  } catch (error) {
    console.error('❌ Admin check error:', error);
    return false;
  }
}

// Helper function to check if user is repair admin
export async function isRepairAdmin(userId) {
  try {
    const client = await pool.connect();
    const result = await client.query(
      'SELECT is_repair_admin FROM users WHERE id = $1',
      [userId]
    );
    client.release();
    
    return result.rows.length > 0 && result.rows[0].is_repair_admin;
  } catch (error) {
    console.error('❌ Repair admin check error:', error);
    return false;
  }
}

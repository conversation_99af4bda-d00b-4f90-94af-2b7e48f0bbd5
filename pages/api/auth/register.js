import { Pool } from 'pg';
import bcrypt from 'bcrypt';
import { customerOnboardingService } from '../../../lib/customer-onboarding.js';
import { monitoringService } from '../../../lib/monitoring.js';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const {
      firstName,
      lastName,
      email,
      password,
      deviceInterests = [],
      marketingConsent = false
    } = req.body;

    // Validate required fields
    if (!firstName || !lastName || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Please enter a valid email address'
      });
    }

    // Validate password strength
    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long'
      });
    }

    const client = await pool.connect();

    try {
      // Check if user already exists
      const existingUser = await client.query(
        'SELECT id FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (existingUser.rows.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'An account with this email already exists'
        });
      }

      // Hash password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // Create user account
      const userResult = await client.query(`
        INSERT INTO users (
          email, first_name, last_name, password_hash,
          email_verified, marketing_consent, device_interests,
          loyalty_points, created_at
        ) VALUES ($1, $2, $3, $4, false, $5, $6, 0, CURRENT_TIMESTAMP)
        RETURNING id, email, first_name, last_name
      `, [
        email.toLowerCase(),
        firstName.trim(),
        lastName.trim(),
        passwordHash,
        marketingConsent,
        JSON.stringify(deviceInterests)
      ]);

      const newUser = userResult.rows[0];

      // Create user profile
      await client.query(`
        INSERT INTO user_profiles (
          user_id, device_interests, marketing_preferences,
          onboarding_completed, created_at
        ) VALUES ($1, $2, $3, false, CURRENT_TIMESTAMP)
      `, [
        newUser.id,
        JSON.stringify(deviceInterests),
        JSON.stringify({
          email_notifications: marketingConsent,
          product_updates: marketingConsent,
          promotional_offers: marketingConsent
        })
      ]);

      // Generate email verification token
      const verificationToken = generateVerificationToken();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // 24 hours

      await client.query(`
        INSERT INTO email_verification_tokens (
          user_id, token, expires_at, created_at
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
      `, [newUser.id, verificationToken, expiresAt]);

      // Start onboarding process
      await customerOnboardingService.startOnboarding(
        newUser.id,
        newUser.email,
        `${newUser.first_name} ${newUser.last_name}`
      );

      // Send verification email
      await sendVerificationEmail(newUser, verificationToken);

      // Track registration event
      await trackRegistrationEvent(newUser, deviceInterests, req);

      client.release();

      // Return success (don't include sensitive data)
      res.status(201).json({
        success: true,
        message: 'Account created successfully',
        user: {
          id: newUser.id,
          email: newUser.email,
          firstName: newUser.first_name,
          lastName: newUser.last_name
        },
        nextStep: 'email_verification'
      });

    } catch (dbError) {
      client.release();
      console.error('❌ Database error during registration:', dbError);
      
      if (dbError.code === '23505') { // Unique constraint violation
        return res.status(400).json({
          success: false,
          message: 'An account with this email already exists'
        });
      }
      
      throw dbError;
    }

  } catch (error) {
    console.error('❌ Registration error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Registration failed. Please try again.'
    });
  }
}

/**
 * Generate secure verification token
 */
function generateVerificationToken() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let token = '';
  for (let i = 0; i < 32; i++) {
    token += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return token;
}

/**
 * Send email verification email
 */
async function sendVerificationEmail(user, token) {
  try {
    const { productionEmailService } = await import('../../../lib/email-service-production.js');
    
    const verificationUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/auth/verify-email?token=${token}`;
    
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Verify Your Email Address</h1>
        <p>Hi ${user.first_name},</p>
        <p>Thank you for creating an account with Midas Technical. To complete your registration, please verify your email address by clicking the button below:</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" 
             style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Verify Email Address
          </a>
        </div>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #2563eb;">${verificationUrl}</p>
        
        <p style="margin-top: 30px; font-size: 14px; color: #666;">
          This verification link will expire in 24 hours. If you didn't create an account with Midas Technical, you can safely ignore this email.
        </p>
      </div>
    `;

    const textContent = `
Hi ${user.first_name},

Thank you for creating an account with Midas Technical. To complete your registration, please verify your email address by visiting:

${verificationUrl}

This verification link will expire in 24 hours. If you didn't create an account with Midas Technical, you can safely ignore this email.
    `;

    await productionEmailService.sendEmail(
      user.email,
      'Verify Your Email Address - Midas Technical',
      htmlContent,
      textContent
    );

    console.log(`✅ Verification email sent to ${user.email}`);

  } catch (error) {
    console.error('❌ Failed to send verification email:', error);
    // Don't throw error - registration should still succeed
  }
}

/**
 * Track registration event for analytics
 */
async function trackRegistrationEvent(user, deviceInterests, req) {
  try {
    const client = await pool.connect();
    
    // Log registration event
    await client.query(`
      INSERT INTO analytics_events (
        event_name, user_id, properties, 
        ip_address, user_agent, created_at
      ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
    `, [
      'user_registered',
      user.id,
      JSON.stringify({
        device_interests: deviceInterests,
        registration_method: 'email',
        marketing_consent: req.body.marketingConsent
      }),
      req.headers['x-forwarded-for'] || req.connection.remoteAddress,
      req.headers['user-agent']
    ]);

    client.release();

    // Track with monitoring service
    monitoringService.trackEvent('user_registered', {
      userId: user.id,
      category: 'authentication',
      device_interests: deviceInterests,
      registration_method: 'email'
    });

    console.log(`📊 Registration event tracked for user ${user.id}`);

  } catch (error) {
    console.error('❌ Failed to track registration event:', error);
    // Don't throw error - registration should still succeed
  }
}

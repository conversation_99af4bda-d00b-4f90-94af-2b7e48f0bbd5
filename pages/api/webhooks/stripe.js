import { buffer } from 'micro';
import Stripe from 'stripe';
import { Pool } from 'pg';
import { orderManager } from '../../../lib/order-manager.js';

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

// Disable body parsing, need the raw body for signature verification
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  // Initialize Stripe with production configuration
  const isProduction = process.env.NODE_ENV === 'production';
  const stripeSecretKey = isProduction 
    ? process.env.STRIPE_SECRET_KEY_LIVE || process.env.STRIPE_SECRET_KEY
    : process.env.STRIPE_SECRET_KEY_TEST || process.env.STRIPE_SECRET_KEY;
  
  const webhookSecret = isProduction
    ? process.env.STRIPE_WEBHOOK_SECRET_LIVE || process.env.STRIPE_WEBHOOK_SECRET
    : process.env.STRIPE_WEBHOOK_SECRET_TEST || process.env.STRIPE_WEBHOOK_SECRET;

  if (!stripeSecretKey) {
    console.error('❌ Stripe secret key not configured');
    return res.status(500).json({ success: false, message: 'Stripe configuration error' });
  }

  if (!webhookSecret) {
    console.error('❌ Stripe webhook secret not configured');
    return res.status(500).json({ success: false, message: 'Stripe webhook secret is not set' });
  }

  const stripe = new Stripe(stripeSecretKey, {
    apiVersion: '2023-10-16',
  });

  try {
    // Get the raw body
    const buf = await buffer(req);
    const sig = req.headers['stripe-signature'];

    // Verify the webhook signature
    let event;
    try {
      event = stripe.webhooks.constructEvent(buf, sig, webhookSecret);
      console.log(`✅ Webhook signature verified: ${event.type}`);
    } catch (err) {
      console.error(`❌ Webhook signature verification failed: ${err.message}`);
      return res.status(400).json({ success: false, message: `Webhook Error: ${err.message}` });
    }

    // Log webhook event
    await logWebhookEvent(event);

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;
        
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object);
        break;
        
      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object);
        break;
        
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;
        
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        await handleSubscriptionEvent(event);
        break;
        
      default:
        console.log(`ℹ️  Unhandled event type: ${event.type}`);
    }

    // Return a response to acknowledge receipt of the event
    return res.status(200).json({ 
      success: true, 
      received: true,
      event_type: event.type,
      event_id: event.id
    });

  } catch (error) {
    console.error('❌ Stripe webhook error:', error);
    
    // Log error to database
    try {
      await pool.query(`
        INSERT INTO webhook_errors (
          provider, event_type, error_message, raw_data, created_at
        ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
      `, ['stripe', req.headers['stripe-signature'] || 'unknown', error.message, JSON.stringify(req.body)]);
    } catch (logError) {
      console.error('❌ Failed to log webhook error:', logError);
    }
    
    return res.status(500).json({ 
      success: false, 
      message: 'Error processing webhook',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}

// Handle successful checkout session
async function handleCheckoutSessionCompleted(session) {
  try {
    console.log(`🛒 Processing checkout session: ${session.id}`);

    // Create order using the order manager
    const orderResult = await orderManager.createOrderFromStripeSession(session);

    console.log(`✅ Order created successfully: ${orderResult.orderNumber}`);

    // Update session status in database
    await pool.query(`
      UPDATE payment_sessions
      SET status = 'completed', completed_at = CURRENT_TIMESTAMP
      WHERE stripe_session_id = $1
    `, [session.id]);

    // Automatically process order for fulfillment if payment is confirmed
    try {
      await orderManager.processOrderForFulfillment(orderResult.orderId);
      console.log(`✅ Order ${orderResult.orderNumber} automatically processed for fulfillment`);
    } catch (fulfillmentError) {
      console.error('❌ Auto-fulfillment processing failed:', fulfillmentError);
      // Don't throw here - order creation was successful
    }

  } catch (error) {
    console.error('❌ Failed to process checkout session:', error);

    // Mark session as failed
    await pool.query(`
      UPDATE payment_sessions
      SET status = 'failed', error_message = $1
      WHERE stripe_session_id = $2
    `, [error.message, session.id]);

    throw error;
  }
}

// Handle successful payment intent
async function handlePaymentIntentSucceeded(paymentIntent) {
  try {
    console.log(`💳 Payment succeeded: ${paymentIntent.id}`);
    
    // Update payment record
    await pool.query(`
      UPDATE payment_attempts 
      SET status = 'succeeded', completed_at = CURRENT_TIMESTAMP
      WHERE stripe_payment_intent_id = $1
    `, [paymentIntent.id]);
    
    // If this is a repair service payment, update repair ticket
    if (paymentIntent.metadata?.repair_ticket_id) {
      await pool.query(`
        UPDATE repair_tickets 
        SET payment_status = 'paid', status = 'ready_pickup'
        WHERE id = $1
      `, [paymentIntent.metadata.repair_ticket_id]);
      
      console.log(`🔧 Repair ticket ${paymentIntent.metadata.repair_ticket_id} payment completed`);
    }
    
  } catch (error) {
    console.error('❌ Failed to process payment intent success:', error);
    throw error;
  }
}

// Handle failed payment intent
async function handlePaymentIntentFailed(paymentIntent) {
  try {
    console.log(`❌ Payment failed: ${paymentIntent.id}`);
    
    // Update payment record
    await pool.query(`
      UPDATE payment_attempts 
      SET status = 'failed', error_message = $1
      WHERE stripe_payment_intent_id = $2
    `, [paymentIntent.last_payment_error?.message || 'Payment failed', paymentIntent.id]);
    
    // If this is a repair service payment, update repair ticket
    if (paymentIntent.metadata?.repair_ticket_id) {
      await pool.query(`
        UPDATE repair_tickets 
        SET payment_status = 'failed'
        WHERE id = $1
      `, [paymentIntent.metadata.repair_ticket_id]);
    }
    
  } catch (error) {
    console.error('❌ Failed to process payment intent failure:', error);
    throw error;
  }
}

// Handle invoice payment succeeded
async function handleInvoicePaymentSucceeded(invoice) {
  try {
    console.log(`📄 Invoice payment succeeded: ${invoice.id}`);
    
    // Handle subscription or recurring payment logic here
    if (invoice.subscription) {
      await pool.query(`
        UPDATE subscriptions 
        SET status = 'active', last_payment_at = CURRENT_TIMESTAMP
        WHERE stripe_subscription_id = $1
      `, [invoice.subscription]);
    }
    
  } catch (error) {
    console.error('❌ Failed to process invoice payment:', error);
    throw error;
  }
}

// Handle subscription events
async function handleSubscriptionEvent(event) {
  try {
    const subscription = event.data.object;
    console.log(`📋 Subscription event: ${event.type} for ${subscription.id}`);
    
    // Update subscription status based on event type
    let status;
    switch (event.type) {
      case 'customer.subscription.created':
        status = 'active';
        break;
      case 'customer.subscription.updated':
        status = subscription.status;
        break;
      case 'customer.subscription.deleted':
        status = 'cancelled';
        break;
    }
    
    await pool.query(`
      INSERT INTO subscriptions (
        stripe_subscription_id, customer_id, status, current_period_start,
        current_period_end, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      ON CONFLICT (stripe_subscription_id) 
      DO UPDATE SET
        status = EXCLUDED.status,
        current_period_start = EXCLUDED.current_period_start,
        current_period_end = EXCLUDED.current_period_end,
        updated_at = CURRENT_TIMESTAMP
    `, [
      subscription.id,
      subscription.customer,
      status,
      new Date(subscription.current_period_start * 1000),
      new Date(subscription.current_period_end * 1000)
    ]);
    
  } catch (error) {
    console.error('❌ Failed to process subscription event:', error);
    throw error;
  }
}

// Log webhook event for audit trail
async function logWebhookEvent(event) {
  try {
    await pool.query(`
      INSERT INTO webhook_logs (
        provider, event_id, event_type, processed_at, data
      ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP, $4)
      ON CONFLICT (provider, event_id) DO NOTHING
    `, ['stripe', event.id, event.type, JSON.stringify(event.data)]);
  } catch (error) {
    console.error('❌ Failed to log webhook event:', error);
    // Don't throw here as this is just logging
  }
}

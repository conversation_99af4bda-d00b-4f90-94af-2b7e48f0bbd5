import { referralProgramService } from '../../../lib/referral-program.js';
import { withAuth } from '../../../lib/auth-middleware.js';

async function handler(req, res) {
  try {
    switch (req.method) {
      case 'GET':
        return await handleGetReferralStats(req, res);
      case 'POST':
        return await handleGenerateReferralCode(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    console.error('❌ Referrals API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

/**
 * Get referral statistics for user
 */
async function handleGetReferralStats(req, res) {
  try {
    const userId = req.user.id;
    
    const stats = await referralProgramService.getReferralStats(userId);
    
    return res.status(200).json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Get referral stats error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get referral statistics'
    });
  }
}

/**
 * Generate or get referral code for user
 */
async function handleGenerateReferralCode(req, res) {
  try {
    const userId = req.user.id;
    
    const referralCode = await referralProgramService.generateReferralCode(userId);
    
    return res.status(200).json({
      success: true,
      data: {
        referralCode,
        shareUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/signup?ref=${referralCode}`,
        rewards: {
          referrer: referralProgramService.rewards.referrer,
          referee: referralProgramService.rewards.referee
        }
      }
    });

  } catch (error) {
    console.error('❌ Generate referral code error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate referral code'
    });
  }
}

// Export with authentication required
export default withAuth(handler, { 
  requireAuth: true,
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 50
  }
});

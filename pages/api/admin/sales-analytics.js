import { salesFunnelAnalytics } from '../../../lib/sales-funnel-analytics.js';
import { withAuth } from '../../../lib/auth-middleware.js';
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

async function handler(req, res) {
  try {
    switch (req.method) {
      case 'GET':
        return await handleGetAnalytics(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    console.error('❌ Sales analytics API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

/**
 * Get comprehensive sales analytics
 */
async function handleGetAnalytics(req, res) {
  try {
    const { 
      dateFrom = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      dateTo = new Date().toISOString().split('T')[0],
      metric = 'overview'
    } = req.query;

    let analyticsData = {};

    switch (metric) {
      case 'overview':
        analyticsData = await getOverviewAnalytics(dateFrom, dateTo);
        break;
      case 'funnel':
        analyticsData = await salesFunnelAnalytics.getConversionFunnel(dateFrom, dateTo);
        break;
      case 'clv':
        analyticsData = await salesFunnelAnalytics.getCustomerLifetimeValue(dateFrom, dateTo);
        break;
      case 'abandonment':
        analyticsData = await salesFunnelAnalytics.getCartAbandonmentAnalytics(dateFrom, dateTo);
        break;
      case 'referrals':
        analyticsData = await salesFunnelAnalytics.getReferralAnalytics(dateFrom, dateTo);
        break;
      case 'onboarding':
        analyticsData = await getOnboardingAnalytics(dateFrom, dateTo);
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid metric type'
        });
    }

    return res.status(200).json({
      success: true,
      data: analyticsData,
      dateRange: { from: dateFrom, to: dateTo }
    });

  } catch (error) {
    console.error('❌ Get analytics error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get analytics data'
    });
  }
}

/**
 * Get overview analytics combining all metrics
 */
async function getOverviewAnalytics(dateFrom, dateTo) {
  const client = await pool.connect();
  
  try {
    // Get key performance indicators
    const kpiResult = await client.query(`
      WITH date_range AS (
        SELECT $1::date as start_date, $2::date as end_date
      ),
      order_metrics AS (
        SELECT 
          COUNT(*) as total_orders,
          SUM(total_amount) as total_revenue,
          AVG(total_amount) as avg_order_value,
          COUNT(DISTINCT customer_email) as unique_customers
        FROM orders o, date_range dr
        WHERE o.created_at >= dr.start_date 
        AND o.created_at <= dr.end_date + INTERVAL '1 day'
        AND o.status NOT IN ('cancelled', 'refunded')
      ),
      visitor_metrics AS (
        SELECT 
          COUNT(DISTINCT COALESCE(user_id::text, session_id)) as total_visitors,
          COUNT(DISTINCT CASE WHEN event_name = 'add_to_cart' THEN COALESCE(user_id::text, session_id) END) as cart_users,
          COUNT(DISTINCT CASE WHEN event_name = 'begin_checkout' THEN COALESCE(user_id::text, session_id) END) as checkout_users
        FROM analytics_events ae, date_range dr
        WHERE ae.created_at >= dr.start_date 
        AND ae.created_at <= dr.end_date + INTERVAL '1 day'
      ),
      abandonment_metrics AS (
        SELECT 
          COUNT(*) as abandoned_carts,
          COUNT(CASE WHEN recovered = true THEN 1 END) as recovered_carts,
          SUM(cart_value) as abandoned_value,
          SUM(CASE WHEN recovered = true THEN cart_value ELSE 0 END) as recovered_value
        FROM abandoned_carts ac, date_range dr
        WHERE ac.created_at >= dr.start_date 
        AND ac.created_at <= dr.end_date + INTERVAL '1 day'
      ),
      onboarding_metrics AS (
        SELECT 
          COUNT(*) as new_signups,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_onboarding,
          AVG(EXTRACT(EPOCH FROM (completed_at - started_at))/3600) as avg_onboarding_hours
        FROM customer_onboarding co, date_range dr
        WHERE co.started_at >= dr.start_date 
        AND co.started_at <= dr.end_date + INTERVAL '1 day'
      )
      SELECT 
        om.*,
        vm.*,
        am.*,
        onm.*,
        CASE 
          WHEN vm.total_visitors > 0 
          THEN ROUND((om.unique_customers::decimal / vm.total_visitors) * 100, 2)
          ELSE 0
        END as visitor_to_customer_rate,
        CASE 
          WHEN vm.cart_users > 0 
          THEN ROUND((vm.checkout_users::decimal / vm.cart_users) * 100, 2)
          ELSE 0
        END as cart_to_checkout_rate,
        CASE 
          WHEN am.abandoned_carts > 0 
          THEN ROUND((am.recovered_carts::decimal / am.abandoned_carts) * 100, 2)
          ELSE 0
        END as cart_recovery_rate,
        CASE 
          WHEN onm.new_signups > 0 
          THEN ROUND((onm.completed_onboarding::decimal / onm.new_signups) * 100, 2)
          ELSE 0
        END as onboarding_completion_rate
      FROM order_metrics om, visitor_metrics vm, abandonment_metrics am, onboarding_metrics onm
    `, [dateFrom, dateTo]);

    // Get top performing products
    const topProductsResult = await client.query(`
      SELECT 
        p.name, p.sku, p.price,
        SUM(oi.quantity) as units_sold,
        SUM(oi.quantity * oi.unit_price) as revenue,
        COUNT(DISTINCT oi.order_id) as orders_count
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      JOIN orders o ON oi.order_id = o.id
      WHERE o.created_at >= $1 AND o.created_at <= $2 + INTERVAL '1 day'
      AND o.status NOT IN ('cancelled', 'refunded')
      GROUP BY p.id, p.name, p.sku, p.price
      ORDER BY revenue DESC
      LIMIT 10
    `, [dateFrom, dateTo]);

    // Get recent customer activity
    const recentActivityResult = await client.query(`
      SELECT 
        event_name,
        COUNT(*) as event_count,
        COUNT(DISTINCT user_id) as unique_users,
        DATE(created_at) as event_date
      FROM analytics_events
      WHERE created_at >= $1 AND created_at <= $2 + INTERVAL '1 day'
      AND event_name IN ('user_registered', 'product_view', 'add_to_cart', 'begin_checkout', 'complete_purchase')
      GROUP BY event_name, DATE(created_at)
      ORDER BY event_date DESC, event_count DESC
      LIMIT 50
    `, [dateFrom, dateTo]);

    client.release();

    const kpi = kpiResult.rows[0];

    return {
      kpi: {
        totalOrders: parseInt(kpi.total_orders),
        totalRevenue: parseFloat(kpi.total_revenue || 0),
        avgOrderValue: parseFloat(kpi.avg_order_value || 0),
        uniqueCustomers: parseInt(kpi.unique_customers),
        totalVisitors: parseInt(kpi.total_visitors),
        cartUsers: parseInt(kpi.cart_users),
        checkoutUsers: parseInt(kpi.checkout_users),
        abandonedCarts: parseInt(kpi.abandoned_carts),
        recoveredCarts: parseInt(kpi.recovered_carts),
        abandonedValue: parseFloat(kpi.abandoned_value || 0),
        recoveredValue: parseFloat(kpi.recovered_value || 0),
        newSignups: parseInt(kpi.new_signups),
        completedOnboarding: parseInt(kpi.completed_onboarding),
        avgOnboardingHours: parseFloat(kpi.avg_onboarding_hours || 0),
        visitorToCustomerRate: parseFloat(kpi.visitor_to_customer_rate || 0),
        cartToCheckoutRate: parseFloat(kpi.cart_to_checkout_rate || 0),
        cartRecoveryRate: parseFloat(kpi.cart_recovery_rate || 0),
        onboardingCompletionRate: parseFloat(kpi.onboarding_completion_rate || 0)
      },
      topProducts: topProductsResult.rows.map(product => ({
        name: product.name,
        sku: product.sku,
        price: parseFloat(product.price),
        unitsSold: parseInt(product.units_sold),
        revenue: parseFloat(product.revenue),
        ordersCount: parseInt(product.orders_count)
      })),
      recentActivity: recentActivityResult.rows.map(activity => ({
        eventName: activity.event_name,
        eventCount: parseInt(activity.event_count),
        uniqueUsers: parseInt(activity.unique_users),
        eventDate: activity.event_date
      }))
    };

  } catch (error) {
    client.release();
    throw error;
  }
}

/**
 * Get customer onboarding analytics
 */
async function getOnboardingAnalytics(dateFrom, dateTo) {
  const client = await pool.connect();
  
  try {
    // Get onboarding funnel metrics
    const funnelResult = await client.query(`
      WITH onboarding_funnel AS (
        SELECT 
          COUNT(*) as total_signups,
          COUNT(CASE WHEN 'email_verified' = ANY(completed_steps) THEN 1 END) as email_verified,
          COUNT(CASE WHEN 'profile_completed' = ANY(completed_steps) THEN 1 END) as profile_completed,
          COUNT(CASE WHEN 'first_browse' = ANY(completed_steps) THEN 1 END) as first_browse,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_onboarding,
          AVG(EXTRACT(EPOCH FROM (completed_at - started_at))/3600) as avg_completion_hours
        FROM customer_onboarding
        WHERE started_at >= $1 AND started_at <= $2 + INTERVAL '1 day'
      )
      SELECT 
        of.*,
        CASE WHEN total_signups > 0 THEN ROUND((email_verified::decimal / total_signups) * 100, 2) ELSE 0 END as email_verification_rate,
        CASE WHEN total_signups > 0 THEN ROUND((profile_completed::decimal / total_signups) * 100, 2) ELSE 0 END as profile_completion_rate,
        CASE WHEN total_signups > 0 THEN ROUND((first_browse::decimal / total_signups) * 100, 2) ELSE 0 END as first_browse_rate,
        CASE WHEN total_signups > 0 THEN ROUND((completed_onboarding::decimal / total_signups) * 100, 2) ELSE 0 END as onboarding_completion_rate
      FROM onboarding_funnel of
    `, [dateFrom, dateTo]);

    // Get email campaign performance
    const emailResult = await client.query(`
      SELECT 
        template_type,
        COUNT(*) as emails_sent,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as emails_delivered,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as emails_failed,
        AVG(onboarding_step) as avg_step
      FROM email_campaigns_queue
      WHERE scheduled_at >= $1 AND scheduled_at <= $2 + INTERVAL '1 day'
      GROUP BY template_type
      ORDER BY emails_sent DESC
    `, [dateFrom, dateTo]);

    // Get device interest distribution
    const deviceInterestsResult = await client.query(`
      SELECT 
        interest,
        COUNT(*) as user_count
      FROM (
        SELECT jsonb_array_elements_text(device_interests) as interest
        FROM customer_onboarding
        WHERE started_at >= $1 AND started_at <= $2 + INTERVAL '1 day'
        AND device_interests IS NOT NULL
      ) interests
      GROUP BY interest
      ORDER BY user_count DESC
    `, [dateFrom, dateTo]);

    client.release();

    const funnel = funnelResult.rows[0];

    return {
      onboardingFunnel: {
        totalSignups: parseInt(funnel.total_signups),
        emailVerified: parseInt(funnel.email_verified),
        profileCompleted: parseInt(funnel.profile_completed),
        firstBrowse: parseInt(funnel.first_browse),
        completedOnboarding: parseInt(funnel.completed_onboarding),
        avgCompletionHours: parseFloat(funnel.avg_completion_hours || 0),
        emailVerificationRate: parseFloat(funnel.email_verification_rate || 0),
        profileCompletionRate: parseFloat(funnel.profile_completion_rate || 0),
        firstBrowseRate: parseFloat(funnel.first_browse_rate || 0),
        onboardingCompletionRate: parseFloat(funnel.onboarding_completion_rate || 0)
      },
      emailCampaigns: emailResult.rows.map(email => ({
        templateType: email.template_type,
        emailsSent: parseInt(email.emails_sent),
        emailsDelivered: parseInt(email.emails_delivered),
        emailsFailed: parseInt(email.emails_failed),
        avgStep: parseFloat(email.avg_step),
        deliveryRate: email.emails_sent > 0 
          ? ((email.emails_delivered / email.emails_sent) * 100).toFixed(2)
          : 0
      })),
      deviceInterests: deviceInterestsResult.rows.map(interest => ({
        interest: interest.interest,
        userCount: parseInt(interest.user_count)
      }))
    };

  } catch (error) {
    client.release();
    throw error;
  }
}

// Export with admin authentication required
export default withAuth(handler, { 
  requireAdmin: true,
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100
  }
});

import { Pool } from 'pg';
import { orderManager } from '../../../lib/order-manager.js';
import { shippingService } from '../../../lib/shipping-service.js';
import { withAuth } from '../../../lib/auth-middleware.js';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

async function handler(req, res) {
  try {
    switch (req.method) {
      case 'GET':
        return await handleGetFulfillment(req, res);
      case 'POST':
        return await handlePostFulfillment(req, res);
      case 'PUT':
        return await handlePutFulfillment(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    console.error('❌ Fulfillment API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

// Export with admin authentication required
export default withAuth(handler, {
  requireAdmin: true,
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 200 // Higher limit for admin operations
  }
});

// GET /api/admin/fulfillment - Get fulfillment dashboard data
async function handleGetFulfillment(req, res) {
  const { action, orderId, dateFrom, dateTo } = req.query;

  switch (action) {
    case 'dashboard':
      return await getFulfillmentDashboard(req, res);
    case 'orders':
      return await getOrdersForFulfillment(req, res);
    case 'metrics':
      return await getFulfillmentMetrics(req, res);
    case 'order-details':
      return await getOrderDetails(req, res);
    default:
      return await getFulfillmentDashboard(req, res);
  }
}

// POST /api/admin/fulfillment - Process fulfillment actions
async function handlePostFulfillment(req, res) {
  const { action, orderId, orderIds, data } = req.body;

  switch (action) {
    case 'process-order':
      return await processOrderForFulfillment(req, res);
    case 'ship-order':
      return await shipOrder(req, res);
    case 'bulk-update':
      return await bulkUpdateOrders(req, res);
    case 'generate-labels':
      return await generateShippingLabels(req, res);
    default:
      return res.status(400).json({ success: false, message: 'Invalid action' });
  }
}

// PUT /api/admin/fulfillment - Update fulfillment data
async function handlePutFulfillment(req, res) {
  const { action, orderId, data } = req.body;

  switch (action) {
    case 'update-status':
      return await updateOrderStatus(req, res);
    case 'assign-fulfillment':
      return await assignFulfillmentStaff(req, res);
    case 'update-priority':
      return await updateFulfillmentPriority(req, res);
    default:
      return res.status(400).json({ success: false, message: 'Invalid action' });
  }
}

// Get fulfillment dashboard overview
async function getFulfillmentDashboard(req, res) {
  try {
    const client = await pool.connect();

    // Get today's metrics
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    const dashboardQuery = `
      SELECT 
        -- Today's orders
        COUNT(CASE WHEN DATE(created_at) = $1 THEN 1 END) as orders_today,
        COUNT(CASE WHEN DATE(created_at) = $2 THEN 1 END) as orders_yesterday,
        
        -- Orders by status
        COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as orders_confirmed,
        COUNT(CASE WHEN status = 'processing' THEN 1 END) as orders_processing,
        COUNT(CASE WHEN status = 'ready_to_ship' THEN 1 END) as orders_ready_to_ship,
        COUNT(CASE WHEN status = 'shipped' THEN 1 END) as orders_shipped,
        
        -- Fulfillment metrics
        AVG(CASE WHEN shipped_at IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (shipped_at - created_at))/3600 
            END) as avg_processing_hours,
        
        -- Revenue
        SUM(CASE WHEN DATE(created_at) = $1 THEN total_amount ELSE 0 END) as revenue_today,
        SUM(CASE WHEN DATE(created_at) = $2 THEN total_amount ELSE 0 END) as revenue_yesterday
        
      FROM orders 
      WHERE created_at >= $2 AND payment_status = 'paid'
    `;

    const dashboardResult = await client.query(dashboardQuery, [today, yesterday]);
    const dashboard = dashboardResult.rows[0];

    // Get urgent orders
    const urgentOrdersQuery = `
      SELECT o.id, o.order_number, o.customer_name, o.total_amount, o.created_at,
             fq.priority, fq.estimated_ship_date
      FROM orders o
      LEFT JOIN fulfillment_queue fq ON o.id = fq.order_id
      WHERE o.status IN ('confirmed', 'processing', 'ready_to_ship') 
        AND o.payment_status = 'paid'
        AND (fq.priority = 'urgent' OR o.created_at < NOW() - INTERVAL '2 days')
      ORDER BY 
        CASE WHEN fq.priority = 'urgent' THEN 1 ELSE 2 END,
        o.created_at ASC
      LIMIT 10
    `;

    const urgentOrdersResult = await client.query(urgentOrdersQuery);

    // Get recent activity
    const activityQuery = `
      SELECT osh.order_id, o.order_number, osh.old_status, osh.new_status, 
             osh.notes, osh.created_at, u.first_name, u.last_name
      FROM order_status_history osh
      JOIN orders o ON osh.order_id = o.id
      LEFT JOIN users u ON osh.changed_by = u.id
      ORDER BY osh.created_at DESC
      LIMIT 20
    `;

    const activityResult = await client.query(activityQuery);

    client.release();

    res.json({
      success: true,
      data: {
        dashboard: {
          ...dashboard,
          orders_today: parseInt(dashboard.orders_today),
          orders_yesterday: parseInt(dashboard.orders_yesterday),
          orders_confirmed: parseInt(dashboard.orders_confirmed),
          orders_processing: parseInt(dashboard.orders_processing),
          orders_ready_to_ship: parseInt(dashboard.orders_ready_to_ship),
          orders_shipped: parseInt(dashboard.orders_shipped),
          avg_processing_hours: parseFloat(dashboard.avg_processing_hours || 0),
          revenue_today: parseFloat(dashboard.revenue_today || 0),
          revenue_yesterday: parseFloat(dashboard.revenue_yesterday || 0)
        },
        urgentOrders: urgentOrdersResult.rows,
        recentActivity: activityResult.rows
      }
    });

  } catch (error) {
    console.error('❌ Dashboard query error:', error);
    throw error;
  }
}

// Get orders for fulfillment
async function getOrdersForFulfillment(req, res) {
  try {
    const { status, priority, limit = 50, page = 1 } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    const orders = await orderManager.getOrdersReadyForFulfillment(parseInt(limit));

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: orders.length
        }
      }
    });

  } catch (error) {
    console.error('❌ Get orders error:', error);
    throw error;
  }
}

// Process order for fulfillment
async function processOrderForFulfillment(req, res) {
  try {
    const { orderId } = req.body;

    const result = await orderManager.processOrderForFulfillment(orderId);

    res.json({
      success: true,
      message: 'Order processed for fulfillment',
      data: result
    });

  } catch (error) {
    console.error('❌ Process order error:', error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
}

// Ship order
async function shipOrder(req, res) {
  try {
    const { orderId, shippingMethod = 'STANDARD', carrier } = req.body;

    const result = await orderManager.shipOrder(orderId, shippingMethod, carrier);

    res.json({
      success: true,
      message: 'Order shipped successfully',
      data: result
    });

  } catch (error) {
    console.error('❌ Ship order error:', error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
}

// Bulk update orders
async function bulkUpdateOrders(req, res) {
  try {
    const { orderIds, status, notes } = req.body;

    const results = await orderManager.bulkUpdateOrderStatus(orderIds, status, notes);

    const successCount = results.filter(r => r.success).length;

    res.json({
      success: true,
      message: `${successCount}/${orderIds.length} orders updated successfully`,
      data: results
    });

  } catch (error) {
    console.error('❌ Bulk update error:', error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
}

// Get fulfillment metrics
async function getFulfillmentMetrics(req, res) {
  try {
    const { dateFrom, dateTo } = req.query;
    
    const fromDate = dateFrom || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
    const toDate = dateTo || new Date().toISOString();

    const metrics = await orderManager.getFulfillmentMetrics(fromDate, toDate);

    res.json({
      success: true,
      data: {
        metrics,
        dateRange: { from: fromDate, to: toDate }
      }
    });

  } catch (error) {
    console.error('❌ Get metrics error:', error);
    throw error;
  }
}

// Update order status
async function updateOrderStatus(req, res) {
  try {
    const { orderId, status, notes } = req.body;

    await orderManager.updateOrderStatus(orderId, status, notes);

    res.json({
      success: true,
      message: 'Order status updated successfully'
    });

  } catch (error) {
    console.error('❌ Update status error:', error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
}

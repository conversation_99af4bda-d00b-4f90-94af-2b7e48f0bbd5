import { Pool } from 'pg';
import { shippingService } from '../../../lib/shipping-service.js';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { orderNumber, trackingNumber, email } = req.query;

    if (!orderNumber && !trackingNumber) {
      return res.status(400).json({
        success: false,
        message: 'Order number or tracking number is required'
      });
    }

    let trackingData;

    if (trackingNumber) {
      trackingData = await trackByTrackingNumber(trackingNumber);
    } else {
      trackingData = await trackByOrderNumber(orderNumber, email);
    }

    res.json({
      success: true,
      data: trackingData
    });

  } catch (error) {
    console.error('❌ Order tracking error:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving tracking information',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function trackByTrackingNumber(trackingNumber) {
  const client = await pool.connect();
  
  try {
    // Get order and shipping information
    const orderQuery = `
      SELECT 
        o.id, o.order_number, o.status, o.customer_name, o.customer_email,
        o.total_amount, o.created_at, o.shipped_at, o.delivered_at,
        o.tracking_number, o.carrier, o.shipping_method,
        o.shipping_address, o.estimated_delivery_date,
        sl.label_url, sl.weight, sl.dimensions
      FROM orders o
      LEFT JOIN shipping_labels sl ON o.id = sl.order_id
      WHERE o.tracking_number = $1
    `;

    const orderResult = await client.query(orderQuery, [trackingNumber]);
    
    if (orderResult.rows.length === 0) {
      throw new Error('Tracking number not found');
    }

    const order = orderResult.rows[0];

    // Get order items
    const itemsQuery = `
      SELECT oi.*, p.name as product_name, p.sku as product_sku, p.image_url
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = $1
    `;

    const itemsResult = await client.query(itemsQuery, [order.id]);

    // Get status history
    const historyQuery = `
      SELECT old_status, new_status, notes, created_at
      FROM order_status_history
      WHERE order_id = $1
      ORDER BY created_at ASC
    `;

    const historyResult = await client.query(historyQuery, [order.id]);

    // Get tracking events from carrier (if available)
    let carrierTracking = null;
    if (order.carrier && order.tracking_number) {
      try {
        carrierTracking = await shippingService.trackShipment(order.tracking_number, order.carrier);
      } catch (trackingError) {
        console.warn('❌ Carrier tracking failed:', trackingError.message);
      }
    }

    client.release();

    return {
      order: {
        ...order,
        shipping_address: JSON.parse(order.shipping_address || '{}'),
        items: itemsResult.rows
      },
      statusHistory: historyResult.rows,
      carrierTracking,
      estimatedDelivery: order.estimated_delivery_date,
      trackingUrl: generateTrackingUrl(order.carrier, order.tracking_number)
    };

  } catch (error) {
    client.release();
    throw error;
  }
}

async function trackByOrderNumber(orderNumber, email) {
  const client = await pool.connect();
  
  try {
    // Build query with optional email verification
    let orderQuery = `
      SELECT 
        o.id, o.order_number, o.status, o.customer_name, o.customer_email,
        o.total_amount, o.created_at, o.shipped_at, o.delivered_at,
        o.tracking_number, o.carrier, o.shipping_method,
        o.shipping_address, o.estimated_delivery_date,
        sl.label_url, sl.weight, sl.dimensions
      FROM orders o
      LEFT JOIN shipping_labels sl ON o.id = sl.order_id
      WHERE o.order_number = $1
    `;

    const queryParams = [orderNumber];

    // Add email verification if provided
    if (email) {
      orderQuery += ' AND o.customer_email = $2';
      queryParams.push(email);
    }

    const orderResult = await client.query(orderQuery, queryParams);
    
    if (orderResult.rows.length === 0) {
      throw new Error(email ? 'Order not found or email does not match' : 'Order not found');
    }

    const order = orderResult.rows[0];

    // Get order items
    const itemsQuery = `
      SELECT oi.*, p.name as product_name, p.sku as product_sku, p.image_url
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = $1
    `;

    const itemsResult = await client.query(itemsQuery, [order.id]);

    // Get status history
    const historyQuery = `
      SELECT old_status, new_status, notes, created_at
      FROM order_status_history
      WHERE order_id = $1
      ORDER BY created_at ASC
    `;

    const historyResult = await client.query(historyQuery, [order.id]);

    // Get tracking events from carrier (if available)
    let carrierTracking = null;
    if (order.carrier && order.tracking_number) {
      try {
        carrierTracking = await shippingService.trackShipment(order.tracking_number, order.carrier);
      } catch (trackingError) {
        console.warn('❌ Carrier tracking failed:', trackingError.message);
      }
    }

    client.release();

    return {
      order: {
        ...order,
        shipping_address: JSON.parse(order.shipping_address || '{}'),
        items: itemsResult.rows
      },
      statusHistory: historyResult.rows,
      carrierTracking,
      estimatedDelivery: order.estimated_delivery_date,
      trackingUrl: order.tracking_number ? generateTrackingUrl(order.carrier, order.tracking_number) : null
    };

  } catch (error) {
    client.release();
    throw error;
  }
}

function generateTrackingUrl(carrier, trackingNumber) {
  if (!carrier || !trackingNumber) return null;

  const trackingUrls = {
    'UPS': `https://www.ups.com/track?tracknum=${trackingNumber}`,
    'FEDEX': `https://www.fedex.com/fedextrack/?tracknumbers=${trackingNumber}`,
    'USPS': `https://tools.usps.com/go/TrackConfirmAction?qtc_tLabels1=${trackingNumber}`
  };

  return trackingUrls[carrier.toUpperCase()] || null;
}

// Helper function to get order status progress
function getOrderProgress(status) {
  const statusFlow = [
    'pending',
    'confirmed',
    'processing',
    'ready_to_ship',
    'shipped',
    'out_for_delivery',
    'delivered'
  ];

  const currentIndex = statusFlow.indexOf(status);
  const progress = currentIndex >= 0 ? ((currentIndex + 1) / statusFlow.length) * 100 : 0;

  return {
    currentStep: currentIndex + 1,
    totalSteps: statusFlow.length,
    progressPercentage: Math.round(progress),
    statusFlow: statusFlow.map((step, index) => ({
      status: step,
      completed: index <= currentIndex,
      current: index === currentIndex
    }))
  };
}

// API endpoint for repair ticket file uploads
import { Pool } from 'pg';
import { getSession } from 'next-auth/react';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

// Disable default body parser for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  const { method } = req;

  try {
    switch (method) {
      case 'POST':
        await uploadFiles(req, res);
        break;
      case 'GET':
        await getTicketFiles(req, res);
        break;
      case 'DELETE':
        await deleteFile(req, res);
        break;
      default:
        res.setHeader('Allow', ['POST', 'GET', 'DELETE']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Repair upload API error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function uploadFiles(req, res) {
  const session = await getSession({ req });

  // Create upload directory if it doesn't exist
  const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'repair');
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  const form = formidable({
    uploadDir: uploadDir,
    keepExtensions: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    filter: function ({ name, originalFilename, mimetype }) {
      // Allow only specific file types
      const allowedTypes = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      return allowedTypes.includes(mimetype);
    }
  });

  try {
    const [fields, files] = await form.parse(req);
    const ticketId = fields.ticket_id?.[0];

    if (!ticketId) {
      return res.status(400).json({
        success: false,
        message: 'Missing ticket_id'
      });
    }

    // Verify ticket exists and user has access
    const ticketResult = await pool.query(
      'SELECT * FROM repair_tickets WHERE id = $1',
      [ticketId]
    );

    if (ticketResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    const ticket = ticketResult.rows[0];

    // Check if user owns the ticket or is admin
    if (!session?.user?.is_admin && !session?.user?.is_repair_admin && 
        ticket.user_id !== session?.user?.id) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized: You can only upload files to your own tickets'
      });
    }

    const uploadedFiles = [];
    const fileArray = Array.isArray(files.files) ? files.files : [files.files].filter(Boolean);

    for (const file of fileArray) {
      if (!file) continue;

      // Generate unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const extension = path.extname(file.originalFilename);
      const newFilename = `${timestamp}-${randomString}${extension}`;
      const newPath = path.join(uploadDir, newFilename);

      // Move file to final location
      fs.renameSync(file.filepath, newPath);

      // Get file info
      const stats = fs.statSync(newPath);
      const fileType = getFileType(file.mimetype);

      // Save file info to database
      const fileResult = await pool.query(
        `INSERT INTO repair_ticket_files 
         (ticket_id, file_name, file_path, file_type, file_size, uploaded_by, description)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         RETURNING *`,
        [
          ticketId,
          file.originalFilename,
          `/uploads/repair/${newFilename}`,
          fileType,
          stats.size,
          session?.user?.id,
          fields.description?.[0] || null
        ]
      );

      uploadedFiles.push({
        id: fileResult.rows[0].id,
        original_name: file.originalFilename,
        file_path: `/uploads/repair/${newFilename}`,
        file_type: fileType,
        file_size: stats.size,
        uploaded_at: fileResult.rows[0].created_at
      });
    }

    res.status(200).json({
      success: true,
      message: `${uploadedFiles.length} file(s) uploaded successfully`,
      data: uploadedFiles
    });

  } catch (error) {
    console.error('Error uploading files:', error);
    
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File size too large. Maximum size is 10MB.'
      });
    }

    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Maximum is 5 files per upload.'
      });
    }

    throw error;
  }
}

async function getTicketFiles(req, res) {
  const { ticket_id } = req.query;
  const session = await getSession({ req });

  if (!ticket_id) {
    return res.status(400).json({
      success: false,
      message: 'Missing ticket_id parameter'
    });
  }

  try {
    // Verify ticket exists and user has access
    const ticketResult = await pool.query(
      'SELECT * FROM repair_tickets WHERE id = $1',
      [ticket_id]
    );

    if (ticketResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    const ticket = ticketResult.rows[0];

    // Check if user owns the ticket or is admin
    if (!session?.user?.is_admin && !session?.user?.is_repair_admin && 
        ticket.user_id !== session?.user?.id) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized: You can only view files from your own tickets'
      });
    }

    // Get files for this ticket
    const filesResult = await pool.query(
      `SELECT 
         rtf.*,
         u.first_name as uploaded_by_name,
         u.last_name as uploaded_by_last_name
       FROM repair_ticket_files rtf
       LEFT JOIN users u ON rtf.uploaded_by = u.id
       WHERE rtf.ticket_id = $1
       ORDER BY rtf.created_at DESC`,
      [ticket_id]
    );

    res.status(200).json({
      success: true,
      data: filesResult.rows
    });

  } catch (error) {
    console.error('Error fetching ticket files:', error);
    throw error;
  }
}

async function deleteFile(req, res) {
  const { file_id } = req.query;
  const session = await getSession({ req });

  if (!file_id) {
    return res.status(400).json({
      success: false,
      message: 'Missing file_id parameter'
    });
  }

  try {
    // Get file info
    const fileResult = await pool.query(
      `SELECT rtf.*, rt.user_id as ticket_user_id
       FROM repair_ticket_files rtf
       JOIN repair_tickets rt ON rtf.ticket_id = rt.id
       WHERE rtf.id = $1`,
      [file_id]
    );

    if (fileResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    const file = fileResult.rows[0];

    // Check if user owns the ticket or is admin or uploaded the file
    if (!session?.user?.is_admin && !session?.user?.is_repair_admin && 
        file.ticket_user_id !== session?.user?.id && 
        file.uploaded_by !== session?.user?.id) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized: You can only delete your own files'
      });
    }

    // Delete file from filesystem
    const filePath = path.join(process.cwd(), 'public', file.file_path);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Delete file record from database
    await pool.query('DELETE FROM repair_ticket_files WHERE id = $1', [file_id]);

    res.status(200).json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
}

function getFileType(mimetype) {
  if (mimetype.startsWith('image/')) {
    return 'image';
  } else if (mimetype === 'application/pdf') {
    return 'document';
  } else if (mimetype.includes('word') || mimetype.includes('document')) {
    return 'document';
  } else {
    return 'other';
  }
}

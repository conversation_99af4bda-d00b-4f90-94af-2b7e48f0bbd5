// API endpoint for repair estimates
import { Pool } from 'pg';
import { getSession } from 'next-auth/react';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export default async function handler(req, res) {
  const { method } = req;

  try {
    switch (method) {
      case 'POST':
        if (req.body.action) {
          await handleEstimateAction(req, res);
        } else {
          await createRepairEstimate(req, res);
        }
        break;
      case 'GET':
        await getRepairEstimate(req, res);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Repair estimate API error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function createRepairEstimate(req, res) {
  const {
    device_type_id,
    services, // Array of service IDs
    additional_parts = [],
    rush_service = false,
    warranty_extension = false
  } = req.body;

  if (!device_type_id || !services || !Array.isArray(services) || services.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Missing required fields: device_type_id and services array'
    });
  }

  try {
    const client = await pool.connect();

    try {
      // Get device information
      const deviceResult = await client.query(
        'SELECT * FROM device_types WHERE id = $1 AND is_active = true',
        [device_type_id]
      );

      if (deviceResult.rows.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Device type not found'
        });
      }

      const device = deviceResult.rows[0];
      let totalLaborCost = 0;
      let totalPartsCost = 0;
      let totalEstimatedTime = 0;
      const serviceDetails = [];

      // Calculate costs for each service
      for (const serviceId of services) {
        // Get service pricing for this device type
        const pricingResult = await client.query(`
          SELECT 
            rsp.*,
            rs.name as service_name,
            rs.description,
            rs.warranty_days,
            rs.difficulty_level,
            rs.requires_diagnosis,
            rs.diagnosis_fee
          FROM repair_service_pricing rsp
          JOIN repair_services rs ON rsp.service_id = rs.id
          WHERE rsp.service_id = $1 AND rsp.device_type_id = $2 AND rsp.is_active = true
        `, [serviceId, device_type_id]);

        if (pricingResult.rows.length === 0) {
          // Fallback to base service pricing
          const baseServiceResult = await client.query(`
            SELECT 
              id,
              name,
              description,
              base_price,
              estimated_time_hours,
              warranty_days,
              difficulty_level,
              requires_diagnosis,
              diagnosis_fee
            FROM repair_services 
            WHERE id = $1 AND is_active = true
          `, [serviceId]);

          if (baseServiceResult.rows.length === 0) {
            continue; // Skip invalid service
          }

          const service = baseServiceResult.rows[0];
          totalLaborCost += parseFloat(service.base_price);
          totalEstimatedTime += service.estimated_time_hours;

          serviceDetails.push({
            service_id: serviceId,
            name: service.name,
            description: service.description,
            labor_cost: service.base_price,
            parts_cost: 0,
            total_cost: service.base_price,
            estimated_time_hours: service.estimated_time_hours,
            warranty_days: service.warranty_days,
            difficulty_level: service.difficulty_level,
            requires_diagnosis: service.requires_diagnosis,
            diagnosis_fee: service.diagnosis_fee
          });
        } else {
          const pricing = pricingResult.rows[0];
          totalLaborCost += parseFloat(pricing.labor_cost);
          totalPartsCost += parseFloat(pricing.parts_cost);
          totalEstimatedTime += pricing.estimated_time_hours;

          serviceDetails.push({
            service_id: serviceId,
            name: pricing.service_name,
            description: pricing.description,
            labor_cost: pricing.labor_cost,
            parts_cost: pricing.parts_cost,
            total_cost: pricing.price,
            estimated_time_hours: pricing.estimated_time_hours,
            warranty_days: pricing.warranty_days,
            difficulty_level: pricing.difficulty_level,
            requires_diagnosis: pricing.requires_diagnosis,
            diagnosis_fee: pricing.diagnosis_fee
          });
        }
      }

      // Calculate additional costs for parts
      let additionalPartsCost = 0;
      const additionalPartsDetails = [];

      if (additional_parts.length > 0) {
        for (const partId of additional_parts) {
          const partResult = await client.query(
            'SELECT * FROM repair_parts WHERE id = $1 AND is_active = true',
            [partId]
          );

          if (partResult.rows.length > 0) {
            const part = partResult.rows[0];
            additionalPartsCost += parseFloat(part.selling_price);
            additionalPartsDetails.push({
              part_id: partId,
              name: part.name,
              cost: part.selling_price
            });
          }
        }
      }

      // Calculate additional fees
      let additionalFees = 0;
      const feeDetails = [];

      // Rush service fee (50% surcharge)
      if (rush_service) {
        const rushFee = (totalLaborCost + totalPartsCost) * 0.5;
        additionalFees += rushFee;
        totalEstimatedTime = Math.ceil(totalEstimatedTime * 0.5); // Rush service reduces time
        feeDetails.push({
          type: 'rush_service',
          description: 'Rush Service (50% surcharge)',
          amount: rushFee
        });
      }

      // Extended warranty fee
      if (warranty_extension) {
        const warrantyFee = (totalLaborCost + totalPartsCost) * 0.15;
        additionalFees += warrantyFee;
        feeDetails.push({
          type: 'extended_warranty',
          description: 'Extended Warranty (6 months additional)',
          amount: warrantyFee
        });
      }

      // Calculate tax (8.5% - adjust based on location)
      const subtotal = totalLaborCost + totalPartsCost + additionalPartsCost + additionalFees;
      const taxRate = 0.085;
      const taxAmount = subtotal * taxRate;
      const totalAmount = subtotal + taxAmount;

      // Calculate estimated completion date
      const estimatedCompletion = new Date();
      estimatedCompletion.setHours(estimatedCompletion.getHours() + totalEstimatedTime);

      const estimate = {
        device: {
          id: device.id,
          name: device.name,
          brand: device.brand,
          model: device.model,
          category: device.category
        },
        services: serviceDetails,
        additional_parts: additionalPartsDetails,
        cost_breakdown: {
          labor_cost: totalLaborCost,
          parts_cost: totalPartsCost + additionalPartsCost,
          additional_fees: additionalFees,
          subtotal: subtotal,
          tax_rate: taxRate,
          tax_amount: taxAmount,
          total_amount: totalAmount
        },
        fee_details: feeDetails,
        estimated_time_hours: totalEstimatedTime,
        estimated_completion: estimatedCompletion,
        rush_service: rush_service,
        warranty_extension: warranty_extension,
        created_at: new Date()
      };

      res.status(200).json({
        success: true,
        data: estimate
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error creating repair estimate:', error);
    throw error;
  }
}

async function getRepairEstimate(req, res) {
  const { estimate_id } = req.query;

  if (!estimate_id) {
    return res.status(400).json({
      success: false,
      message: 'Missing estimate_id parameter'
    });
  }

  try {
    const result = await pool.query(`
      SELECT 
        re.*,
        rt.ticket_number,
        rt.customer_name,
        rt.customer_email,
        dt.name as device_name,
        dt.brand as device_brand,
        dt.model as device_model
      FROM repair_estimates re
      LEFT JOIN repair_tickets rt ON re.ticket_id = rt.id
      LEFT JOIN device_types dt ON rt.device_type_id = dt.id
      WHERE re.id = $1
    `, [estimate_id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Estimate not found'
      });
    }

    res.status(200).json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Error fetching repair estimate:', error);
    throw error;
  }
}

async function handleEstimateAction(req, res) {
  const { ticket_id, action } = req.body;
  const session = await getSession({ req });

  if (!ticket_id || !action) {
    return res.status(400).json({
      success: false,
      message: 'Missing required fields: ticket_id, action'
    });
  }

  if (!['approve', 'reject'].includes(action)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid action. Must be "approve" or "reject"'
    });
  }

  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Get ticket details
    const ticketResult = await client.query(
      'SELECT * FROM repair_tickets WHERE id = $1',
      [ticket_id]
    );

    if (ticketResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    const ticket = ticketResult.rows[0];

    // Check if user owns the ticket or is admin
    if (!session?.user?.is_admin && !session?.user?.is_repair_admin &&
        ticket.user_id !== session?.user?.id) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized: You can only approve/reject your own estimates'
      });
    }

    let newStatus;
    let statusNote;

    if (action === 'approve') {
      newStatus = 'approved';
      statusNote = 'Customer approved the repair estimate';
    } else {
      newStatus = 'cancelled';
      statusNote = 'Customer rejected the repair estimate';
    }

    // Update ticket status
    await client.query(
      `UPDATE repair_tickets
       SET status = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2`,
      [newStatus, ticket_id]
    );

    // Add status history
    await client.query(
      `INSERT INTO repair_ticket_status_history
       (ticket_id, old_status, new_status, changed_by, notes, customer_notified)
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [ticket_id, ticket.status, newStatus, session?.user?.id, statusNote, false]
    );

    // If approved, create estimate record
    if (action === 'approve') {
      const estimateNumber = `EST-${Date.now()}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;

      await client.query(
        `INSERT INTO repair_estimates
         (ticket_id, estimate_number, total_amount, status, customer_approved_at, created_by)
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          ticket_id,
          estimateNumber,
          ticket.estimated_cost,
          'approved',
          new Date(),
          session?.user?.id
        ]
      );
    }

    await client.query('COMMIT');

    res.status(200).json({
      success: true,
      message: `Estimate ${action}d successfully`,
      data: {
        ticket_id: ticket_id,
        action: action,
        new_status: newStatus
      }
    });

  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

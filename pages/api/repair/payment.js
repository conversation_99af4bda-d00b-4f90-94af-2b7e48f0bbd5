// API endpoint for repair service payments
import { Pool } from 'pg';
import <PERSON><PERSON> from 'stripe';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export default async function handler(req, res) {
  const { method } = req;

  try {
    switch (method) {
      case 'POST':
        await createPaymentSession(req, res);
        break;
      case 'PUT':
        await updatePaymentStatus(req, res);
        break;
      default:
        res.setHeader('Allow', ['POST', 'PUT']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Repair payment API error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function createPaymentSession(req, res) {
  const { ticket_id, payment_method = 'stripe' } = req.body;

  if (!ticket_id) {
    return res.status(400).json({
      success: false,
      message: 'Missing required field: ticket_id'
    });
  }

  try {
    // Get ticket details
    const ticketResult = await pool.query(`
      SELECT 
        rt.*,
        COALESCE(
          JSON_AGG(
            JSON_BUILD_OBJECT(
              'service_name', rs.name,
              'quantity', rts.quantity,
              'unit_price', rts.unit_price,
              'total_price', rts.total_price
            )
          ) FILTER (WHERE rts.id IS NOT NULL), '[]'
        ) as services
      FROM repair_tickets rt
      LEFT JOIN repair_ticket_services rts ON rt.id = rts.ticket_id
      LEFT JOIN repair_services rs ON rts.service_id = rs.id
      WHERE rt.id = $1
      GROUP BY rt.id
    `, [ticket_id]);

    if (ticketResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Repair ticket not found'
      });
    }

    const ticket = ticketResult.rows[0];

    // Check if payment is already completed
    if (ticket.payment_status === 'paid') {
      return res.status(400).json({
        success: false,
        message: 'Payment already completed for this ticket'
      });
    }

    // Determine payment amount
    const paymentAmount = ticket.final_cost || ticket.estimated_cost;
    if (!paymentAmount || paymentAmount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'No payment amount available for this ticket'
      });
    }

    if (payment_method === 'stripe') {
      // Create Stripe checkout session
      const lineItems = [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `Repair Service - Ticket #${ticket.ticket_number}`,
              description: `${ticket.device_brand} ${ticket.device_model} repair services`,
              metadata: {
                ticket_id: ticket.id,
                ticket_number: ticket.ticket_number
              }
            },
            unit_amount: Math.round(paymentAmount * 100), // Convert to cents
          },
          quantity: 1,
        }
      ];

      // Add individual services as line items
      if (ticket.services && ticket.services.length > 0) {
        ticket.services.forEach(service => {
          if (service.service_name && service.total_price > 0) {
            lineItems.push({
              price_data: {
                currency: 'usd',
                product_data: {
                  name: service.service_name,
                  description: `Repair service for ${ticket.device_brand} ${ticket.device_model}`,
                },
                unit_amount: Math.round(service.total_price * 100),
              },
              quantity: service.quantity || 1,
            });
          }
        });
      }

      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: lineItems,
        mode: 'payment',
        success_url: `${process.env.NEXTAUTH_URL}/repair/payment/success?session_id={CHECKOUT_SESSION_ID}&ticket_id=${ticket.id}`,
        cancel_url: `${process.env.NEXTAUTH_URL}/repair/track?ticket=${ticket.ticket_number}`,
        metadata: {
          ticket_id: ticket.id,
          ticket_number: ticket.ticket_number,
          payment_type: 'repair_service'
        },
        customer_email: ticket.customer_email,
        billing_address_collection: 'auto',
        shipping_address_collection: {
          allowed_countries: ['US', 'CA'],
        },
      });

      // Update ticket with payment session info
      await pool.query(
        `UPDATE repair_tickets 
         SET payment_method = $1, payment_id = $2, updated_at = CURRENT_TIMESTAMP
         WHERE id = $3`,
        ['stripe', session.id, ticket.id]
      );

      res.status(200).json({
        success: true,
        data: {
          session_id: session.id,
          checkout_url: session.url,
          payment_method: 'stripe',
          amount: paymentAmount
        }
      });

    } else if (payment_method === 'paypal') {
      // PayPal integration would go here
      // For now, return a placeholder
      res.status(501).json({
        success: false,
        message: 'PayPal integration not yet implemented'
      });

    } else {
      res.status(400).json({
        success: false,
        message: 'Unsupported payment method'
      });
    }

  } catch (error) {
    console.error('Error creating payment session:', error);
    throw error;
  }
}

async function updatePaymentStatus(req, res) {
  const { ticket_id, payment_status, payment_id, transaction_id } = req.body;

  if (!ticket_id || !payment_status) {
    return res.status(400).json({
      success: false,
      message: 'Missing required fields: ticket_id, payment_status'
    });
  }

  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Update payment status
    const updateResult = await client.query(
      `UPDATE repair_tickets 
       SET payment_status = $1, payment_id = $2, updated_at = CURRENT_TIMESTAMP
       WHERE id = $3
       RETURNING *`,
      [payment_status, payment_id || transaction_id, ticket_id]
    );

    if (updateResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({
        success: false,
        message: 'Repair ticket not found'
      });
    }

    const ticket = updateResult.rows[0];

    // Add status history entry
    await client.query(
      `INSERT INTO repair_ticket_status_history 
       (ticket_id, old_status, new_status, notes, created_at)
       VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)`,
      [
        ticket_id,
        ticket.status,
        payment_status === 'paid' ? 'approved' : ticket.status,
        `Payment status updated to ${payment_status}`
      ]
    );

    // If payment is successful, update ticket status to approved
    if (payment_status === 'paid' && ticket.status === 'submitted') {
      await client.query(
        `UPDATE repair_tickets 
         SET status = 'approved', updated_at = CURRENT_TIMESTAMP
         WHERE id = $1`,
        [ticket_id]
      );
    }

    await client.query('COMMIT');

    // Send confirmation email (implement email service)
    if (payment_status === 'paid') {
      // await sendPaymentConfirmationEmail(ticket.customer_email, ticket.ticket_number);
    }

    res.status(200).json({
      success: true,
      message: 'Payment status updated successfully',
      data: {
        ticket_id: ticket.id,
        payment_status: payment_status,
        ticket_status: payment_status === 'paid' ? 'approved' : ticket.status
      }
    });

  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Webhook handler for Stripe payment confirmations
export async function handleStripeWebhook(event) {
  try {
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object;
        
        if (session.metadata?.payment_type === 'repair_service') {
          const ticketId = session.metadata.ticket_id;
          
          // Update payment status to paid
          await pool.query(
            `UPDATE repair_tickets 
             SET payment_status = 'paid', 
                 payment_id = $1,
                 status = CASE WHEN status = 'submitted' THEN 'approved' ELSE status END,
                 updated_at = CURRENT_TIMESTAMP
             WHERE id = $2`,
            [session.payment_intent, ticketId]
          );

          // Add status history
          await pool.query(
            `INSERT INTO repair_ticket_status_history 
             (ticket_id, old_status, new_status, notes, created_at)
             VALUES ($1, 'submitted', 'approved', 'Payment completed via Stripe', CURRENT_TIMESTAMP)`,
            [ticketId]
          );

          console.log(`Payment completed for repair ticket ${ticketId}`);
        }
        break;

      case 'payment_intent.payment_failed':
        const paymentIntent = event.data.object;
        
        // Find ticket by payment intent ID
        const failedTicketResult = await pool.query(
          'SELECT id FROM repair_tickets WHERE payment_id = $1',
          [paymentIntent.id]
        );

        if (failedTicketResult.rows.length > 0) {
          const ticketId = failedTicketResult.rows[0].id;
          
          await pool.query(
            `UPDATE repair_tickets 
             SET payment_status = 'failed', updated_at = CURRENT_TIMESTAMP
             WHERE id = $1`,
            [ticketId]
          );

          console.log(`Payment failed for repair ticket ${ticketId}`);
        }
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  } catch (error) {
    console.error('Error handling Stripe webhook for repair payment:', error);
    throw error;
  }
}

// API endpoint for repair tickets
import { Pool } from 'pg';
import { getSession } from 'next-auth/react';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export default async function handler(req, res) {
  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        await getRepairTickets(req, res);
        break;
      case 'POST':
        await createRepairTicket(req, res);
        break;
      case 'PUT':
        await updateRepairTicket(req, res);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Repair tickets API error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function getRepairTickets(req, res) {
  const {
    ticket_id,
    ticket_number,
    user_id,
    status,
    technician_id,
    priority,
    search,
    limit = 50,
    offset = 0
  } = req.query;
  const session = await getSession({ req });

  try {
    let query = `
      SELECT
        rt.*,
        dt.name as device_name,
        dt.brand as device_brand,
        dt.model as device_model,
        dt.category as device_category,
        u.first_name as technician_first_name,
        u.last_name as technician_last_name,
        u.technician_level,
        COALESCE(
          JSON_AGG(
            JSON_BUILD_OBJECT(
              'service_id', rts.service_id,
              'service_name', rs.name,
              'quantity', rts.quantity,
              'unit_price', rts.unit_price,
              'total_price', rts.total_price,
              'status', rts.status
            )
          ) FILTER (WHERE rts.id IS NOT NULL), '[]'
        ) as services,
        (
          SELECT COUNT(*)
          FROM repair_ticket_status_history rtsh
          WHERE rtsh.ticket_id = rt.id
        ) as status_changes_count
      FROM repair_tickets rt
      LEFT JOIN device_types dt ON rt.device_type_id = dt.id
      LEFT JOIN users u ON rt.technician_id = u.id
      LEFT JOIN repair_ticket_services rts ON rt.id = rts.ticket_id
      LEFT JOIN repair_services rs ON rts.service_id = rs.id
      WHERE 1=1
    `;

    const params = [];
    let paramCount = 0;

    if (ticket_id) {
      paramCount++;
      query += ` AND rt.id = $${paramCount}`;
      params.push(ticket_id);
    }

    if (ticket_number) {
      paramCount++;
      query += ` AND rt.ticket_number = $${paramCount}`;
      params.push(ticket_number);
    }

    if (user_id) {
      paramCount++;
      query += ` AND rt.user_id = $${paramCount}`;
      params.push(user_id);
    }

    if (status) {
      paramCount++;
      query += ` AND rt.status = $${paramCount}`;
      params.push(status);
    }

    if (technician_id) {
      paramCount++;
      query += ` AND rt.technician_id = $${paramCount}`;
      params.push(technician_id);
    }

    if (priority) {
      paramCount++;
      query += ` AND rt.priority = $${paramCount}`;
      params.push(priority);
    }

    if (search) {
      paramCount++;
      query += ` AND (
        rt.ticket_number ILIKE $${paramCount} OR
        rt.customer_name ILIKE $${paramCount} OR
        rt.customer_email ILIKE $${paramCount} OR
        rt.device_brand ILIKE $${paramCount} OR
        rt.device_model ILIKE $${paramCount} OR
        rt.problem_description ILIKE $${paramCount}
      )`;
      params.push(`%${search}%`);
    }

    // If not admin or repair admin, only show user's own tickets
    if (session?.user && !session.user.is_admin && !session.user.is_repair_admin) {
      paramCount++;
      query += ` AND rt.user_id = $${paramCount}`;
      params.push(session.user.id);
    }

    query += `
      GROUP BY rt.id, dt.name, dt.brand, dt.model, dt.category, u.first_name, u.last_name, u.technician_level
      ORDER BY rt.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    params.push(parseInt(limit), parseInt(offset));

    const result = await pool.query(query, params);

    res.status(200).json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });

  } catch (error) {
    console.error('Error fetching repair tickets:', error);
    throw error;
  }
}

async function createRepairTicket(req, res) {
  const {
    customer_name,
    customer_email,
    customer_phone,
    device_type_id,
    device_brand,
    device_model,
    device_serial,
    device_imei,
    device_condition,
    problem_description,
    requested_services, // Array of service IDs
    priority = 'normal',
    customer_notes
  } = req.body;

  // Validate required fields
  if (!customer_name || !customer_email || !problem_description) {
    return res.status(400).json({
      success: false,
      message: 'Missing required fields: customer_name, customer_email, problem_description'
    });
  }

  const session = await getSession({ req });
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Generate ticket number
    const ticketNumber = `RPR-${Date.now()}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;

    // Insert repair ticket
    const ticketResult = await client.query(
      `INSERT INTO repair_tickets 
       (ticket_number, user_id, customer_name, customer_email, customer_phone,
        device_type_id, device_brand, device_model, device_serial, device_imei,
        device_condition, problem_description, requested_services, priority, customer_notes)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
       RETURNING *`,
      [
        ticketNumber,
        session?.user?.id || null,
        customer_name,
        customer_email,
        customer_phone,
        device_type_id,
        device_brand,
        device_model,
        device_serial,
        device_imei,
        device_condition,
        problem_description,
        requested_services || [],
        priority,
        customer_notes
      ]
    );

    const ticketId = ticketResult.rows[0].id;

    // Add requested services if provided
    if (requested_services && Array.isArray(requested_services)) {
      for (const serviceId of requested_services) {
        // Get service pricing
        const pricingResult = await client.query(
          `SELECT rsp.*, rs.name as service_name
           FROM repair_service_pricing rsp
           JOIN repair_services rs ON rsp.service_id = rs.id
           WHERE rsp.service_id = $1 AND rsp.device_type_id = $2 AND rsp.is_active = true
           LIMIT 1`,
          [serviceId, device_type_id]
        );

        let unitPrice = 0;
        if (pricingResult.rows.length > 0) {
          unitPrice = pricingResult.rows[0].price;
        } else {
          // Fallback to base service price
          const serviceResult = await client.query(
            'SELECT base_price FROM repair_services WHERE id = $1',
            [serviceId]
          );
          if (serviceResult.rows.length > 0) {
            unitPrice = serviceResult.rows[0].base_price;
          }
        }

        await client.query(
          `INSERT INTO repair_ticket_services 
           (ticket_id, service_id, pricing_id, quantity, unit_price, total_price)
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [
            ticketId,
            serviceId,
            pricingResult.rows[0]?.id || null,
            1,
            unitPrice,
            unitPrice
          ]
        );
      }
    }

    // Add initial status history
    await client.query(
      `INSERT INTO repair_ticket_status_history 
       (ticket_id, old_status, new_status, notes)
       VALUES ($1, $2, $3, $4)`,
      [ticketId, null, 'submitted', 'Ticket created by customer']
    );

    await client.query('COMMIT');

    // Send confirmation email (implement email service)
    // await sendTicketConfirmationEmail(customer_email, ticketNumber);

    res.status(201).json({
      success: true,
      message: 'Repair ticket created successfully',
      data: {
        ...ticketResult.rows[0],
        ticket_number: ticketNumber
      }
    });

  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

async function updateRepairTicket(req, res) {
  const { ticket_id } = req.query;
  const {
    status,
    technician_id,
    diagnosis_notes,
    repair_notes,
    internal_notes,
    estimated_cost,
    final_cost,
    estimated_completion,
    actual_completion
  } = req.body;

  const session = await getSession({ req });

  // Check if user is admin or technician
  if (!session?.user?.is_admin && !session?.user?.is_technician) {
    return res.status(403).json({
      success: false,
      message: 'Unauthorized: Admin or technician access required'
    });
  }

  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Get current ticket status
    const currentTicket = await client.query(
      'SELECT status FROM repair_tickets WHERE id = $1',
      [ticket_id]
    );

    if (currentTicket.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Repair ticket not found'
      });
    }

    const oldStatus = currentTicket.rows[0].status;

    // Update ticket
    const updateFields = [];
    const updateValues = [];
    let paramCount = 0;

    if (status !== undefined) {
      paramCount++;
      updateFields.push(`status = $${paramCount}`);
      updateValues.push(status);
    }

    if (technician_id !== undefined) {
      paramCount++;
      updateFields.push(`technician_id = $${paramCount}`);
      updateValues.push(technician_id);
    }

    if (diagnosis_notes !== undefined) {
      paramCount++;
      updateFields.push(`diagnosis_notes = $${paramCount}`);
      updateValues.push(diagnosis_notes);
    }

    if (repair_notes !== undefined) {
      paramCount++;
      updateFields.push(`repair_notes = $${paramCount}`);
      updateValues.push(repair_notes);
    }

    if (internal_notes !== undefined) {
      paramCount++;
      updateFields.push(`internal_notes = $${paramCount}`);
      updateValues.push(internal_notes);
    }

    if (estimated_cost !== undefined) {
      paramCount++;
      updateFields.push(`estimated_cost = $${paramCount}`);
      updateValues.push(estimated_cost);
    }

    if (final_cost !== undefined) {
      paramCount++;
      updateFields.push(`final_cost = $${paramCount}`);
      updateValues.push(final_cost);
    }

    if (estimated_completion !== undefined) {
      paramCount++;
      updateFields.push(`estimated_completion = $${paramCount}`);
      updateValues.push(estimated_completion);
    }

    if (actual_completion !== undefined) {
      paramCount++;
      updateFields.push(`actual_completion = $${paramCount}`);
      updateValues.push(actual_completion);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No fields to update'
      });
    }

    paramCount++;
    updateFields.push(`updated_at = $${paramCount}`);
    updateValues.push(new Date());

    paramCount++;
    updateValues.push(ticket_id);

    const updateQuery = `
      UPDATE repair_tickets 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await client.query(updateQuery, updateValues);

    // Add status history if status changed
    if (status && status !== oldStatus) {
      await client.query(
        `INSERT INTO repair_ticket_status_history 
         (ticket_id, old_status, new_status, changed_by, notes)
         VALUES ($1, $2, $3, $4, $5)`,
        [ticket_id, oldStatus, status, session.user.id, `Status updated by ${session.user.first_name || 'user'}`]
      );

      // Send status update notification to customer
      // await sendStatusUpdateEmail(result.rows[0].customer_email, result.rows[0].ticket_number, status);
    }

    await client.query('COMMIT');

    res.status(200).json({
      success: true,
      message: 'Repair ticket updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

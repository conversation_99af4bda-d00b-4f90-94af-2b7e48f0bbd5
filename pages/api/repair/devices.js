// API endpoint for device types
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export default async function handler(req, res) {
  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        await getDeviceTypes(req, res);
        break;
      case 'POST':
        await createDeviceType(req, res);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Device types API error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function getDeviceTypes(req, res) {
  const { category, brand, search } = req.query;

  try {
    let query = `
      SELECT 
        dt.*,
        COUNT(rsp.id) as available_services
      FROM device_types dt
      LEFT JOIN repair_service_pricing rsp ON dt.id = rsp.device_type_id AND rsp.is_active = true
      WHERE dt.is_active = true
    `;

    const params = [];
    let paramCount = 0;

    if (category) {
      paramCount++;
      query += ` AND dt.category = $${paramCount}`;
      params.push(category);
    }

    if (brand) {
      paramCount++;
      query += ` AND dt.brand ILIKE $${paramCount}`;
      params.push(`%${brand}%`);
    }

    if (search) {
      paramCount++;
      query += ` AND (dt.name ILIKE $${paramCount} OR dt.brand ILIKE $${paramCount} OR dt.model ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    query += `
      GROUP BY dt.id
      ORDER BY dt.category, dt.brand, dt.name
    `;

    const result = await pool.query(query, params);

    // Group by category for better organization
    const groupedDevices = result.rows.reduce((acc, device) => {
      const category = device.category || 'other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(device);
      return acc;
    }, {});

    res.status(200).json({
      success: true,
      data: result.rows,
      grouped: groupedDevices,
      count: result.rows.length
    });

  } catch (error) {
    console.error('Error fetching device types:', error);
    throw error;
  }
}

async function createDeviceType(req, res) {
  const {
    name,
    slug,
    brand,
    model,
    category,
    image_url
  } = req.body;

  // Validate required fields
  if (!name || !slug || !category) {
    return res.status(400).json({
      success: false,
      message: 'Missing required fields: name, slug, category'
    });
  }

  try {
    const result = await pool.query(
      `INSERT INTO device_types (name, slug, brand, model, category, image_url)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [name, slug, brand, model, category, image_url]
    );

    res.status(201).json({
      success: true,
      message: 'Device type created successfully',
      data: result.rows[0]
    });

  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      return res.status(409).json({
        success: false,
        message: 'A device type with this slug already exists'
      });
    }
    
    throw error;
  }
}

// Insert default device types
export async function insertDefaultDeviceTypes() {
  const defaultDevices = [
    // Phones
    { name: 'iPhone 15 Pro Max', slug: 'iphone-15-pro-max', brand: 'Apple', model: 'iPhone 15 Pro Max', category: 'phone' },
    { name: 'iPhone 15 Pro', slug: 'iphone-15-pro', brand: 'Apple', model: 'iPhone 15 Pro', category: 'phone' },
    { name: 'iPhone 15', slug: 'iphone-15', brand: 'Apple', model: 'iPhone 15', category: 'phone' },
    { name: 'iPhone 14 Pro Max', slug: 'iphone-14-pro-max', brand: 'Apple', model: 'iPhone 14 Pro Max', category: 'phone' },
    { name: 'iPhone 14 Pro', slug: 'iphone-14-pro', brand: 'Apple', model: 'iPhone 14 Pro', category: 'phone' },
    { name: 'iPhone 14', slug: 'iphone-14', brand: 'Apple', model: 'iPhone 14', category: 'phone' },
    { name: 'iPhone 13 Pro Max', slug: 'iphone-13-pro-max', brand: 'Apple', model: 'iPhone 13 Pro Max', category: 'phone' },
    { name: 'iPhone 13 Pro', slug: 'iphone-13-pro', brand: 'Apple', model: 'iPhone 13 Pro', category: 'phone' },
    { name: 'iPhone 13', slug: 'iphone-13', brand: 'Apple', model: 'iPhone 13', category: 'phone' },
    { name: 'iPhone 12 Pro Max', slug: 'iphone-12-pro-max', brand: 'Apple', model: 'iPhone 12 Pro Max', category: 'phone' },
    { name: 'Samsung Galaxy S24 Ultra', slug: 'samsung-galaxy-s24-ultra', brand: 'Samsung', model: 'Galaxy S24 Ultra', category: 'phone' },
    { name: 'Samsung Galaxy S24+', slug: 'samsung-galaxy-s24-plus', brand: 'Samsung', model: 'Galaxy S24+', category: 'phone' },
    { name: 'Samsung Galaxy S24', slug: 'samsung-galaxy-s24', brand: 'Samsung', model: 'Galaxy S24', category: 'phone' },
    { name: 'Samsung Galaxy S23 Ultra', slug: 'samsung-galaxy-s23-ultra', brand: 'Samsung', model: 'Galaxy S23 Ultra', category: 'phone' },
    { name: 'Google Pixel 8 Pro', slug: 'google-pixel-8-pro', brand: 'Google', model: 'Pixel 8 Pro', category: 'phone' },
    { name: 'Google Pixel 8', slug: 'google-pixel-8', brand: 'Google', model: 'Pixel 8', category: 'phone' },

    // Tablets
    { name: 'iPad Pro 12.9" (6th gen)', slug: 'ipad-pro-12-9-6th-gen', brand: 'Apple', model: 'iPad Pro 12.9"', category: 'tablet' },
    { name: 'iPad Pro 11" (4th gen)', slug: 'ipad-pro-11-4th-gen', brand: 'Apple', model: 'iPad Pro 11"', category: 'tablet' },
    { name: 'iPad Air (5th gen)', slug: 'ipad-air-5th-gen', brand: 'Apple', model: 'iPad Air', category: 'tablet' },
    { name: 'iPad (10th gen)', slug: 'ipad-10th-gen', brand: 'Apple', model: 'iPad', category: 'tablet' },
    { name: 'iPad mini (6th gen)', slug: 'ipad-mini-6th-gen', brand: 'Apple', model: 'iPad mini', category: 'tablet' },
    { name: 'Samsung Galaxy Tab S9 Ultra', slug: 'samsung-galaxy-tab-s9-ultra', brand: 'Samsung', model: 'Galaxy Tab S9 Ultra', category: 'tablet' },
    { name: 'Samsung Galaxy Tab S9+', slug: 'samsung-galaxy-tab-s9-plus', brand: 'Samsung', model: 'Galaxy Tab S9+', category: 'tablet' },
    { name: 'Samsung Galaxy Tab S9', slug: 'samsung-galaxy-tab-s9', brand: 'Samsung', model: 'Galaxy Tab S9', category: 'tablet' },

    // Laptops
    { name: 'MacBook Pro 16" M3', slug: 'macbook-pro-16-m3', brand: 'Apple', model: 'MacBook Pro 16"', category: 'laptop' },
    { name: 'MacBook Pro 14" M3', slug: 'macbook-pro-14-m3', brand: 'Apple', model: 'MacBook Pro 14"', category: 'laptop' },
    { name: 'MacBook Air 15" M2', slug: 'macbook-air-15-m2', brand: 'Apple', model: 'MacBook Air 15"', category: 'laptop' },
    { name: 'MacBook Air 13" M2', slug: 'macbook-air-13-m2', brand: 'Apple', model: 'MacBook Air 13"', category: 'laptop' },
    { name: 'Dell XPS 13', slug: 'dell-xps-13', brand: 'Dell', model: 'XPS 13', category: 'laptop' },
    { name: 'Dell XPS 15', slug: 'dell-xps-15', brand: 'Dell', model: 'XPS 15', category: 'laptop' },
    { name: 'HP Spectre x360', slug: 'hp-spectre-x360', brand: 'HP', model: 'Spectre x360', category: 'laptop' },
    { name: 'Lenovo ThinkPad X1 Carbon', slug: 'lenovo-thinkpad-x1-carbon', brand: 'Lenovo', model: 'ThinkPad X1 Carbon', category: 'laptop' },

    // Gaming Consoles
    { name: 'PlayStation 5', slug: 'playstation-5', brand: 'Sony', model: 'PlayStation 5', category: 'gaming' },
    { name: 'PlayStation 4 Pro', slug: 'playstation-4-pro', brand: 'Sony', model: 'PlayStation 4 Pro', category: 'gaming' },
    { name: 'Xbox Series X', slug: 'xbox-series-x', brand: 'Microsoft', model: 'Xbox Series X', category: 'gaming' },
    { name: 'Xbox Series S', slug: 'xbox-series-s', brand: 'Microsoft', model: 'Xbox Series S', category: 'gaming' },
    { name: 'Nintendo Switch OLED', slug: 'nintendo-switch-oled', brand: 'Nintendo', model: 'Switch OLED', category: 'gaming' },
    { name: 'Nintendo Switch', slug: 'nintendo-switch', brand: 'Nintendo', model: 'Switch', category: 'gaming' },

    // Desktops
    { name: 'iMac 24" M3', slug: 'imac-24-m3', brand: 'Apple', model: 'iMac 24"', category: 'desktop' },
    { name: 'Mac Studio M2', slug: 'mac-studio-m2', brand: 'Apple', model: 'Mac Studio', category: 'desktop' },
    { name: 'Mac Pro M2', slug: 'mac-pro-m2', brand: 'Apple', model: 'Mac Pro', category: 'desktop' },
    { name: 'Custom PC Build', slug: 'custom-pc-build', brand: 'Various', model: 'Custom Build', category: 'desktop' }
  ];

  try {
    for (const device of defaultDevices) {
      await pool.query(
        `INSERT INTO device_types (name, slug, brand, model, category)
         VALUES ($1, $2, $3, $4, $5)
         ON CONFLICT (slug) DO NOTHING`,
        [device.name, device.slug, device.brand, device.model, device.category]
      );
    }
    console.log('Default device types inserted successfully');
  } catch (error) {
    console.error('Error inserting default device types:', error);
  }
}

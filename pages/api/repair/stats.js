// API endpoint for repair statistics
import { Pool } from 'pg';
import { getSession } from 'next-auth/react';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export default async function handler(req, res) {
  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        await getRepairStats(req, res);
        break;
      default:
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Repair stats API error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function getRepairStats(req, res) {
  const session = await getSession({ req });

  // Check if user is admin or repair admin
  if (!session?.user?.is_admin && !session?.user?.is_repair_admin) {
    return res.status(403).json({
      success: false,
      message: 'Unauthorized: Admin access required'
    });
  }

  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Get basic ticket counts
    const ticketStatsQuery = `
      SELECT 
        COUNT(*) FILTER (WHERE status IN ('submitted', 'received')) as pending_tickets,
        COUNT(*) FILTER (WHERE status IN ('diagnosed', 'approved', 'in_progress', 'testing')) as in_progress_tickets,
        COUNT(*) FILTER (WHERE status = 'completed' AND DATE(actual_completion) = CURRENT_DATE) as completed_today,
        COUNT(*) FILTER (WHERE status = 'completed' AND DATE(actual_completion) >= $1) as completed_this_week,
        COUNT(*) FILTER (WHERE status = 'completed' AND DATE(actual_completion) >= $2) as completed_this_month,
        COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_tickets,
        COUNT(*) as total_tickets,
        AVG(EXTRACT(EPOCH FROM (actual_completion - created_at))/3600) FILTER (WHERE actual_completion IS NOT NULL) as avg_completion_hours
      FROM repair_tickets
    `;

    const ticketStatsResult = await pool.query(ticketStatsQuery, [startOfWeek, startOfMonth]);
    const ticketStats = ticketStatsResult.rows[0];

    // Get revenue stats
    const revenueStatsQuery = `
      SELECT 
        COALESCE(SUM(final_cost), 0) FILTER (WHERE DATE(actual_completion) = CURRENT_DATE AND payment_status = 'paid') as revenue_today,
        COALESCE(SUM(final_cost), 0) FILTER (WHERE DATE(actual_completion) >= $1 AND payment_status = 'paid') as revenue_this_week,
        COALESCE(SUM(final_cost), 0) FILTER (WHERE DATE(actual_completion) >= $2 AND payment_status = 'paid') as revenue_this_month,
        COALESCE(SUM(estimated_cost), 0) FILTER (WHERE status IN ('diagnosed', 'approved', 'in_progress', 'testing')) as pending_revenue,
        COALESCE(AVG(final_cost), 0) FILTER (WHERE final_cost IS NOT NULL AND payment_status = 'paid') as avg_ticket_value
      FROM repair_tickets
    `;

    const revenueStatsResult = await pool.query(revenueStatsQuery, [startOfWeek, startOfMonth]);
    const revenueStats = revenueStatsResult.rows[0];

    // Get technician performance
    const technicianStatsQuery = `
      SELECT 
        u.id,
        u.first_name,
        u.last_name,
        u.technician_level,
        COUNT(rt.id) FILTER (WHERE rt.status IN ('in_progress', 'testing')) as active_tickets,
        COUNT(rt.id) FILTER (WHERE rt.status = 'completed' AND DATE(rt.actual_completion) >= $1) as completed_this_week,
        AVG(EXTRACT(EPOCH FROM (rt.actual_completion - rt.created_at))/3600) FILTER (WHERE rt.actual_completion IS NOT NULL) as avg_completion_hours,
        AVG(rsr.rating) FILTER (WHERE rsr.rating IS NOT NULL) as avg_rating
      FROM users u
      LEFT JOIN repair_tickets rt ON u.id = rt.technician_id
      LEFT JOIN repair_service_reviews rsr ON rt.id = rsr.ticket_id
      WHERE u.is_technician = true AND u.is_active = true
      GROUP BY u.id, u.first_name, u.last_name, u.technician_level
      ORDER BY completed_this_week DESC, avg_rating DESC
    `;

    const technicianStatsResult = await pool.query(technicianStatsQuery, [startOfWeek]);
    const technicianStats = technicianStatsResult.rows;

    // Get device type breakdown
    const deviceStatsQuery = `
      SELECT 
        dt.category,
        dt.name as device_name,
        COUNT(rt.id) as ticket_count,
        AVG(rt.final_cost) FILTER (WHERE rt.final_cost IS NOT NULL) as avg_repair_cost,
        AVG(EXTRACT(EPOCH FROM (rt.actual_completion - rt.created_at))/3600) FILTER (WHERE rt.actual_completion IS NOT NULL) as avg_completion_hours
      FROM device_types dt
      LEFT JOIN repair_tickets rt ON dt.id = rt.device_type_id
      WHERE rt.created_at >= $1
      GROUP BY dt.category, dt.name
      HAVING COUNT(rt.id) > 0
      ORDER BY ticket_count DESC
      LIMIT 10
    `;

    const deviceStatsResult = await pool.query(deviceStatsQuery, [startOfMonth]);
    const deviceStats = deviceStatsResult.rows;

    // Get service popularity
    const serviceStatsQuery = `
      SELECT 
        rs.name as service_name,
        rsc.name as category_name,
        COUNT(rts.id) as usage_count,
        AVG(rts.unit_price) as avg_price,
        SUM(rts.total_price) as total_revenue
      FROM repair_services rs
      LEFT JOIN repair_service_categories rsc ON rs.category_id = rsc.id
      LEFT JOIN repair_ticket_services rts ON rs.id = rts.service_id
      LEFT JOIN repair_tickets rt ON rts.ticket_id = rt.id
      WHERE rt.created_at >= $1
      GROUP BY rs.name, rsc.name
      HAVING COUNT(rts.id) > 0
      ORDER BY usage_count DESC
      LIMIT 10
    `;

    const serviceStatsResult = await pool.query(serviceStatsQuery, [startOfMonth]);
    const serviceStats = serviceStatsResult.rows;

    // Get daily ticket trends (last 30 days)
    const trendStatsQuery = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as tickets_created,
        COUNT(*) FILTER (WHERE status = 'completed') as tickets_completed,
        COALESCE(SUM(final_cost), 0) FILTER (WHERE payment_status = 'paid') as daily_revenue
      FROM repair_tickets
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;

    const trendStatsResult = await pool.query(trendStatsQuery);
    const trendStats = trendStatsResult.rows;

    // Get priority distribution
    const priorityStatsQuery = `
      SELECT 
        priority,
        COUNT(*) as count,
        AVG(EXTRACT(EPOCH FROM (actual_completion - created_at))/3600) FILTER (WHERE actual_completion IS NOT NULL) as avg_completion_hours
      FROM repair_tickets
      WHERE created_at >= $1
      GROUP BY priority
      ORDER BY 
        CASE priority 
          WHEN 'urgent' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'normal' THEN 3 
          WHEN 'low' THEN 4 
        END
    `;

    const priorityStatsResult = await pool.query(priorityStatsQuery, [startOfMonth]);
    const priorityStats = priorityStatsResult.rows;

    // Compile all statistics
    const stats = {
      // Basic counts
      pending_tickets: parseInt(ticketStats.pending_tickets) || 0,
      in_progress_tickets: parseInt(ticketStats.in_progress_tickets) || 0,
      completed_today: parseInt(ticketStats.completed_today) || 0,
      completed_this_week: parseInt(ticketStats.completed_this_week) || 0,
      completed_this_month: parseInt(ticketStats.completed_this_month) || 0,
      cancelled_tickets: parseInt(ticketStats.cancelled_tickets) || 0,
      total_tickets: parseInt(ticketStats.total_tickets) || 0,
      
      // Revenue
      revenue_today: parseFloat(revenueStats.revenue_today) || 0,
      revenue_this_week: parseFloat(revenueStats.revenue_this_week) || 0,
      revenue_this_month: parseFloat(revenueStats.revenue_this_month) || 0,
      pending_revenue: parseFloat(revenueStats.pending_revenue) || 0,
      avg_ticket_value: parseFloat(revenueStats.avg_ticket_value) || 0,
      
      // Performance metrics
      avg_completion_hours: parseFloat(ticketStats.avg_completion_hours) || 0,
      
      // Detailed breakdowns
      technician_performance: technicianStats,
      device_breakdown: deviceStats,
      service_popularity: serviceStats,
      daily_trends: trendStats,
      priority_distribution: priorityStats,
      
      // Calculated metrics
      completion_rate: ticketStats.total_tickets > 0 
        ? ((ticketStats.completed_today + ticketStats.completed_this_week + ticketStats.completed_this_month) / ticketStats.total_tickets * 100).toFixed(1)
        : 0,
      
      // Status for dashboard
      generated_at: new Date().toISOString(),
      period: {
        today: startOfDay.toISOString(),
        week: startOfWeek.toISOString(),
        month: startOfMonth.toISOString()
      }
    };

    res.status(200).json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error fetching repair stats:', error);
    throw error;
  }
}

// API endpoint for repair services
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export default async function handler(req, res) {
  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        await getRepairServices(req, res);
        break;
      case 'POST':
        await createRepairService(req, res);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Repair services API error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function getRepairServices(req, res) {
  const { category, device_type, search } = req.query;

  try {
    let query = `
      SELECT 
        rs.*,
        rsc.name as category_name,
        rsc.slug as category_slug,
        rsc.icon as category_icon,
        COALESCE(
          JSON_AGG(
            JSON_BUILD_OBJECT(
              'device_type_id', rsp.device_type_id,
              'device_name', dt.name,
              'device_brand', dt.brand,
              'device_model', dt.model,
              'price', rsp.price,
              'estimated_time_hours', rsp.estimated_time_hours,
              'parts_cost', rsp.parts_cost,
              'labor_cost', rsp.labor_cost
            )
          ) FILTER (WHERE rsp.id IS NOT NULL), '[]'
        ) as pricing
      FROM repair_services rs
      LEFT JOIN repair_service_categories rsc ON rs.category_id = rsc.id
      LEFT JOIN repair_service_pricing rsp ON rs.id = rsp.service_id AND rsp.is_active = true
      LEFT JOIN device_types dt ON rsp.device_type_id = dt.id AND dt.is_active = true
      WHERE rs.is_active = true
    `;

    const params = [];
    let paramCount = 0;

    if (category) {
      paramCount++;
      query += ` AND rsc.slug = $${paramCount}`;
      params.push(category);
    }

    if (device_type) {
      paramCount++;
      query += ` AND dt.slug = $${paramCount}`;
      params.push(device_type);
    }

    if (search) {
      paramCount++;
      query += ` AND (rs.name ILIKE $${paramCount} OR rs.description ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    query += `
      GROUP BY rs.id, rsc.name, rsc.slug, rsc.icon
      ORDER BY rsc.display_order, rs.name
    `;

    const result = await pool.query(query, params);

    res.status(200).json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('Error fetching repair services:', error);
    throw error;
  }
}

async function createRepairService(req, res) {
  const {
    category_id,
    name,
    slug,
    description,
    short_description,
    base_price,
    estimated_time_hours,
    difficulty_level,
    warranty_days,
    requires_diagnosis,
    diagnosis_fee,
    image_url,
    pricing // Array of device-specific pricing
  } = req.body;

  // Validate required fields
  if (!category_id || !name || !slug || !base_price) {
    return res.status(400).json({
      success: false,
      message: 'Missing required fields: category_id, name, slug, base_price'
    });
  }

  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Insert repair service
    const serviceResult = await client.query(
      `INSERT INTO repair_services 
       (category_id, name, slug, description, short_description, base_price, 
        estimated_time_hours, difficulty_level, warranty_days, requires_diagnosis, 
        diagnosis_fee, image_url)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
       RETURNING *`,
      [
        category_id, name, slug, description, short_description, base_price,
        estimated_time_hours || 24, difficulty_level || 'medium', warranty_days || 90,
        requires_diagnosis || false, diagnosis_fee || 0.00, image_url
      ]
    );

    const serviceId = serviceResult.rows[0].id;

    // Insert device-specific pricing if provided
    if (pricing && Array.isArray(pricing)) {
      for (const price of pricing) {
        await client.query(
          `INSERT INTO repair_service_pricing 
           (service_id, device_type_id, price, estimated_time_hours, parts_cost, labor_cost)
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [
            serviceId,
            price.device_type_id,
            price.price,
            price.estimated_time_hours || estimated_time_hours || 24,
            price.parts_cost || 0.00,
            price.labor_cost || price.price
          ]
        );
      }
    }

    await client.query('COMMIT');

    res.status(201).json({
      success: true,
      message: 'Repair service created successfully',
      data: serviceResult.rows[0]
    });

  } catch (error) {
    await client.query('ROLLBACK');
    
    if (error.code === '23505') { // Unique constraint violation
      return res.status(409).json({
        success: false,
        message: 'A repair service with this slug already exists'
      });
    }
    
    throw error;
  } finally {
    client.release();
  }
}

// API endpoint for repair technicians
import { Pool } from 'pg';
import { getSession } from 'next-auth/react';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
});

export default async function handler(req, res) {
  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        await getTechnicians(req, res);
        break;
      case 'POST':
        await createTechnician(req, res);
        break;
      case 'PUT':
        await updateTechnician(req, res);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Repair technicians API error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function getTechnicians(req, res) {
  const { technician_id, include_stats = false } = req.query;
  const session = await getSession({ req });

  // Check if user is admin or repair admin
  if (!session?.user?.is_admin && !session?.user?.is_repair_admin) {
    return res.status(403).json({
      success: false,
      message: 'Unauthorized: Admin access required'
    });
  }

  try {
    let query = `
      SELECT 
        u.id,
        u.first_name,
        u.last_name,
        u.email,
        u.phone,
        u.is_technician,
        u.technician_level,
        u.max_concurrent_repairs,
        u.created_at,
        rt.employee_id,
        rt.specializations,
        rt.skill_level,
        rt.hourly_rate,
        rt.is_active,
        rt.max_concurrent_tickets,
        rt.certifications,
        rt.hire_date
    `;

    if (include_stats === 'true') {
      query += `,
        (
          SELECT COUNT(*) 
          FROM repair_tickets rtickets 
          WHERE rtickets.technician_id = u.id 
          AND rtickets.status IN ('in_progress', 'testing')
        ) as active_tickets,
        (
          SELECT COUNT(*) 
          FROM repair_tickets rtickets 
          WHERE rtickets.technician_id = u.id 
          AND rtickets.status = 'completed'
          AND DATE(rtickets.actual_completion) >= CURRENT_DATE - INTERVAL '7 days'
        ) as completed_this_week,
        (
          SELECT AVG(rating) 
          FROM repair_service_reviews rsr
          JOIN repair_tickets rtickets ON rsr.ticket_id = rtickets.id
          WHERE rtickets.technician_id = u.id
          AND rsr.created_at >= CURRENT_DATE - INTERVAL '30 days'
        ) as avg_rating_30_days,
        (
          SELECT AVG(EXTRACT(EPOCH FROM (actual_completion - created_at))/3600)
          FROM repair_tickets rtickets 
          WHERE rtickets.technician_id = u.id 
          AND rtickets.actual_completion IS NOT NULL
          AND rtickets.actual_completion >= CURRENT_DATE - INTERVAL '30 days'
        ) as avg_completion_hours_30_days
      `;
    }

    query += `
      FROM users u
      LEFT JOIN repair_technicians rt ON u.id = rt.user_id
      WHERE u.is_technician = true
    `;

    const params = [];
    let paramCount = 0;

    if (technician_id) {
      paramCount++;
      query += ` AND u.id = $${paramCount}`;
      params.push(technician_id);
    }

    query += ` ORDER BY u.first_name, u.last_name`;

    const result = await pool.query(query, params);

    res.status(200).json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('Error fetching technicians:', error);
    throw error;
  }
}

async function createTechnician(req, res) {
  const {
    user_id,
    employee_id,
    specializations = [],
    skill_level = 'intermediate',
    hourly_rate,
    max_concurrent_tickets = 5,
    certifications,
    hire_date
  } = req.body;

  const session = await getSession({ req });

  // Check if user is admin
  if (!session?.user?.is_admin) {
    return res.status(403).json({
      success: false,
      message: 'Unauthorized: Admin access required'
    });
  }

  // Validate required fields
  if (!user_id) {
    return res.status(400).json({
      success: false,
      message: 'Missing required field: user_id'
    });
  }

  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Update user to be a technician
    await client.query(
      `UPDATE users 
       SET is_technician = true, 
           technician_level = $1,
           max_concurrent_repairs = $2,
           updated_at = CURRENT_TIMESTAMP
       WHERE id = $3`,
      [skill_level, max_concurrent_tickets, user_id]
    );

    // Insert or update technician record
    const technicianResult = await client.query(
      `INSERT INTO repair_technicians 
       (user_id, employee_id, specializations, skill_level, hourly_rate, 
        max_concurrent_tickets, certifications, hire_date)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       ON CONFLICT (user_id) 
       DO UPDATE SET
         employee_id = EXCLUDED.employee_id,
         specializations = EXCLUDED.specializations,
         skill_level = EXCLUDED.skill_level,
         hourly_rate = EXCLUDED.hourly_rate,
         max_concurrent_tickets = EXCLUDED.max_concurrent_tickets,
         certifications = EXCLUDED.certifications,
         hire_date = EXCLUDED.hire_date
       RETURNING *`,
      [
        user_id,
        employee_id,
        specializations,
        skill_level,
        hourly_rate,
        max_concurrent_tickets,
        certifications,
        hire_date || new Date()
      ]
    );

    await client.query('COMMIT');

    res.status(201).json({
      success: true,
      message: 'Technician created successfully',
      data: technicianResult.rows[0]
    });

  } catch (error) {
    await client.query('ROLLBACK');
    
    if (error.code === '23505') { // Unique violation
      res.status(400).json({
        success: false,
        message: 'Employee ID already exists'
      });
    } else {
      throw error;
    }
  } finally {
    client.release();
  }
}

async function updateTechnician(req, res) {
  const { technician_id } = req.query;
  const {
    employee_id,
    specializations,
    skill_level,
    hourly_rate,
    is_active,
    max_concurrent_tickets,
    certifications
  } = req.body;

  const session = await getSession({ req });

  // Check if user is admin
  if (!session?.user?.is_admin) {
    return res.status(403).json({
      success: false,
      message: 'Unauthorized: Admin access required'
    });
  }

  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Update technician record
    const updateFields = [];
    const updateValues = [];
    let paramCount = 0;

    if (employee_id !== undefined) {
      paramCount++;
      updateFields.push(`employee_id = $${paramCount}`);
      updateValues.push(employee_id);
    }

    if (specializations !== undefined) {
      paramCount++;
      updateFields.push(`specializations = $${paramCount}`);
      updateValues.push(specializations);
    }

    if (skill_level !== undefined) {
      paramCount++;
      updateFields.push(`skill_level = $${paramCount}`);
      updateValues.push(skill_level);
    }

    if (hourly_rate !== undefined) {
      paramCount++;
      updateFields.push(`hourly_rate = $${paramCount}`);
      updateValues.push(hourly_rate);
    }

    if (is_active !== undefined) {
      paramCount++;
      updateFields.push(`is_active = $${paramCount}`);
      updateValues.push(is_active);
    }

    if (max_concurrent_tickets !== undefined) {
      paramCount++;
      updateFields.push(`max_concurrent_tickets = $${paramCount}`);
      updateValues.push(max_concurrent_tickets);
    }

    if (certifications !== undefined) {
      paramCount++;
      updateFields.push(`certifications = $${paramCount}`);
      updateValues.push(certifications);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No fields to update'
      });
    }

    paramCount++;
    updateValues.push(technician_id);

    const updateQuery = `
      UPDATE repair_technicians 
      SET ${updateFields.join(', ')}
      WHERE user_id = $${paramCount}
      RETURNING *
    `;

    const result = await client.query(updateQuery, updateValues);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Technician not found'
      });
    }

    // Also update user table if skill level or max concurrent tickets changed
    if (skill_level !== undefined || max_concurrent_tickets !== undefined) {
      const userUpdateFields = [];
      const userUpdateValues = [];
      let userParamCount = 0;

      if (skill_level !== undefined) {
        userParamCount++;
        userUpdateFields.push(`technician_level = $${userParamCount}`);
        userUpdateValues.push(skill_level);
      }

      if (max_concurrent_tickets !== undefined) {
        userParamCount++;
        userUpdateFields.push(`max_concurrent_repairs = $${userParamCount}`);
        userUpdateValues.push(max_concurrent_tickets);
      }

      if (userUpdateFields.length > 0) {
        userParamCount++;
        userUpdateFields.push(`updated_at = $${userParamCount}`);
        userUpdateValues.push(new Date());

        userParamCount++;
        userUpdateValues.push(technician_id);

        const userUpdateQuery = `
          UPDATE users 
          SET ${userUpdateFields.join(', ')}
          WHERE id = $${userParamCount}
        `;

        await client.query(userUpdateQuery, userUpdateValues);
      }
    }

    await client.query('COMMIT');

    res.status(200).json({
      success: true,
      message: 'Technician updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

import { salesOptimizationService } from '../../../lib/sales-optimization.js';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { productId, quantity = 1, userId } = req.query;

    if (!productId) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }

    const parsedProductId = parseInt(productId);
    const parsedQuantity = parseInt(quantity);
    const parsedUserId = userId ? parseInt(userId) : null;

    if (isNaN(parsedProductId) || isNaN(parsedQuantity) || parsedQuantity <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID or quantity'
      });
    }

    const pricing = await salesOptimizationService.calculateDynamicPricing(
      parsedProductId,
      parsedUserId,
      parsedQuantity
    );

    return res.status(200).json({
      success: true,
      data: pricing
    });

  } catch (error) {
    console.error('❌ Dynamic pricing API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to calculate dynamic pricing'
    });
  }
}

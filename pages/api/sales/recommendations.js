import { salesOptimizationService } from '../../../lib/sales-optimization.js';
import { requireAuth } from '../../../lib/auth-middleware.js';

async function handler(req, res) {
  try {
    switch (req.method) {
      case 'GET':
        return await handleGetRecommendations(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    console.error('❌ Recommendations API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

/**
 * Get product recommendations
 */
async function handleGetRecommendations(req, res) {
  try {
    const { productId, type = 'related', limit = 6 } = req.query;
    const userId = req.user?.id;

    if (!productId) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }

    const validTypes = ['frequently_bought_together', 'customers_also_viewed', 'category_bestsellers', 'personalized'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid recommendation type'
      });
    }

    const recommendations = await salesOptimizationService.getProductRecommendations(
      parseInt(productId),
      userId,
      type
    );

    return res.status(200).json({
      success: true,
      data: {
        recommendations: recommendations.slice(0, parseInt(limit)),
        type,
        productId: parseInt(productId)
      }
    });

  } catch (error) {
    console.error('❌ Get recommendations error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get recommendations'
    });
  }
}

// Export with optional authentication
export default async (req, res) => {
  // Try to get user info but don't require authentication
  try {
    await requireAuth(req, res);
  } catch (error) {
    // Continue without user info for anonymous recommendations
    req.user = null;
  }
  
  return handler(req, res);
};

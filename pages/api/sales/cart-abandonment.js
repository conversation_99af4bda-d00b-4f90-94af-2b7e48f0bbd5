import { salesOptimizationService } from '../../../lib/sales-optimization.js';
import { withAuth } from '../../../lib/auth-middleware.js';

async function handler(req, res) {
  try {
    switch (req.method) {
      case 'POST':
        return await handleTrackAbandonment(req, res);
      case 'GET':
        return await handleGetAbandonedCart(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    console.error('❌ Cart abandonment API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

/**
 * Track cart abandonment
 */
async function handleTrackAbandonment(req, res) {
  try {
    const { cartData } = req.body;
    const userId = req.user.id;

    if (!cartData || !cartData.items || cartData.items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Cart data is required'
      });
    }

    // Validate cart items
    const validItems = cartData.items.every(item => 
      item.productId && 
      item.quantity && 
      item.price &&
      item.quantity > 0 &&
      parseFloat(item.price) > 0
    );

    if (!validItems) {
      return res.status(400).json({
        success: false,
        message: 'Invalid cart items'
      });
    }

    await salesOptimizationService.trackCartAbandonment(userId, cartData);

    return res.status(200).json({
      success: true,
      message: 'Cart abandonment tracked successfully'
    });

  } catch (error) {
    console.error('❌ Track abandonment error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to track cart abandonment'
    });
  }
}

/**
 * Get abandoned cart data
 */
async function handleGetAbandonedCart(req, res) {
  try {
    const userId = req.user.id;
    const { Pool } = await import('pg');
    
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/midastechnical_store',
    });

    const client = await pool.connect();
    
    try {
      const result = await client.query(`
        SELECT cart_data, cart_value, items_count, recovery_emails_sent,
               last_updated, created_at
        FROM abandoned_carts
        WHERE user_id = $1
      `, [userId]);

      if (result.rows.length === 0) {
        return res.status(200).json({
          success: true,
          data: null
        });
      }

      const abandonedCart = result.rows[0];
      
      return res.status(200).json({
        success: true,
        data: {
          cartData: JSON.parse(abandonedCart.cart_data),
          cartValue: parseFloat(abandonedCart.cart_value),
          itemsCount: abandonedCart.items_count,
          recoveryEmailsSent: abandonedCart.recovery_emails_sent,
          lastUpdated: abandonedCart.last_updated,
          createdAt: abandonedCart.created_at
        }
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('❌ Get abandoned cart error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get abandoned cart'
    });
  }
}

// Export with authentication required
export default withAuth(handler, { 
  requireAuth: true,
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100
  }
});

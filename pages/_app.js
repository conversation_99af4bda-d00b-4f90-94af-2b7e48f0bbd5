import { SessionProvider } from 'next-auth/react';
import { useRouter } from 'next/router';
import Layout from '../components/Layout/Layout';
import '../styles/globals.css';

// Pages that don't need the default layout
const pagesWithoutLayout = [
  '/auth/signin',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password'
];

// Pages that use custom layouts
const pagesWithCustomLayout = [
  '/admin',
  '/repair',
  '/products'
];

function MyApp({ Component, pageProps: { session, ...pageProps } }) {
  const router = useRouter();
  
  // Check if current page should use layout
  const useLayout = !pagesWithoutLayout.includes(router.pathname) && 
                   !pagesWithoutLayout.some(path => router.pathname.startsWith(path));

  // Check if component has its own layout
  const hasCustomLayout = Component.getLayout || 
                         pagesWithCustomLayout.some(path => router.pathname.startsWith(path));

  return (
    <SessionProvider session={session}>
      {Component.getLayout ? (
        // Use component's custom layout
        Component.getLayout(<Component {...pageProps} />)
      ) : useLayout && !hasCustomLayout ? (
        // Use default layout
        <Layout>
          <Component {...pageProps} />
        </Layout>
      ) : (
        // No layout
        <Component {...pageProps} />
      )}
    </SessionProvider>
  );
}

export default MyApp;

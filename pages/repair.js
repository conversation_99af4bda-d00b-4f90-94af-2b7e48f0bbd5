import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  PhoneIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon,
  CpuChipIcon,
  WrenchScrewdriverIcon,
  ClockIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  StarIcon
} from '@heroicons/react/24/outline';

export default function RepairServices() {
  const [services, setServices] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDevice, setSelectedDevice] = useState('all');
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRepairServices();
    fetchDeviceTypes();
  }, [selectedCategory, selectedDevice]);

  const fetchRepairServices = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (selectedDevice !== 'all') params.append('device_type', selectedDevice);

      const response = await fetch(`/api/repair/services?${params}`);
      const data = await response.json();

      if (data.success) {
        setServices(data.data);
        
        // Extract unique categories
        const uniqueCategories = [...new Set(data.data.map(service => ({
          slug: service.category_slug,
          name: service.category_name,
          icon: service.category_icon
        })))];
        setCategories(uniqueCategories);
      }
    } catch (error) {
      console.error('Error fetching repair services:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDeviceTypes = async () => {
    try {
      const response = await fetch('/api/repair/devices');
      const data = await response.json();

      if (data.success) {
        setDevices(data.data);
      }
    } catch (error) {
      console.error('Error fetching device types:', error);
    }
  };

  const getCategoryIcon = (iconName) => {
    const iconMap = {
      'screen': PhoneIcon,
      'battery': CpuChipIcon,
      'water-drop': WrenchScrewdriverIcon,
      'plug': WrenchScrewdriverIcon,
      'camera': WrenchScrewdriverIcon,
      'volume-up': WrenchScrewdriverIcon,
      'radio-button-on': WrenchScrewdriverIcon,
      'code': ComputerDesktopIcon,
      'folder-open': WrenchScrewdriverIcon,
      'hardware-chip': CpuChipIcon
    };
    return iconMap[iconName] || WrenchScrewdriverIcon;
  };

  const getDeviceIcon = (category) => {
    const iconMap = {
      'phone': PhoneIcon,
      'tablet': DeviceTabletIcon,
      'laptop': ComputerDesktopIcon,
      'desktop': ComputerDesktopIcon,
      'gaming': CpuChipIcon
    };
    return iconMap[category] || WrenchScrewdriverIcon;
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  return (
    <>
      <Head>
        <title>Professional Device Repair Services | Midas Technical</title>
        <meta name="description" content="Expert repair services for phones, tablets, laptops, and gaming devices. Fast, reliable, and affordable repairs with warranty." />
        <meta name="keywords" content="device repair, phone repair, laptop repair, tablet repair, gaming console repair, screen repair, battery replacement" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <motion.h1 
                className="text-4xl md:text-6xl font-bold mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                Professional Device Repair Services
              </motion.h1>
              <motion.p 
                className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Expert repairs for all your devices with fast turnaround, competitive pricing, and comprehensive warranty coverage.
              </motion.p>
              <motion.div 
                className="flex flex-col sm:flex-row gap-4 justify-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <Link href="/repair/quote" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                  Get Free Quote
                </Link>
                <Link href="/repair/track" className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                  Track Repair
                </Link>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <ClockIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Fast Turnaround</h3>
                <p className="text-gray-600">Most repairs completed within 24-48 hours</p>
              </div>
              <div className="text-center">
                <ShieldCheckIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">90-Day Warranty</h3>
                <p className="text-gray-600">Comprehensive warranty on all repairs</p>
              </div>
              <div className="text-center">
                <CurrencyDollarIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Competitive Pricing</h3>
                <p className="text-gray-600">Fair and transparent pricing with no hidden fees</p>
              </div>
              <div className="text-center">
                <StarIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Expert Technicians</h3>
                <p className="text-gray-600">Certified professionals with years of experience</p>
              </div>
            </div>
          </div>
        </section>

        {/* Filters Section */}
        <section className="py-8 bg-gray-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Category Filter */}
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">Service Category</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.slug} value={category.slug}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Device Filter */}
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">Device Type</label>
                <select
                  value={selectedDevice}
                  onChange={(e) => setSelectedDevice(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Devices</option>
                  {devices.map((device) => (
                    <option key={device.slug} value={device.slug}>
                      {device.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading repair services...</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {services.map((service) => {
                  const IconComponent = getCategoryIcon(service.category_icon);
                  const minPrice = service.pricing.length > 0 
                    ? Math.min(...service.pricing.map(p => parseFloat(p.price)))
                    : parseFloat(service.base_price);

                  return (
                    <motion.div
                      key={service.id}
                      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      <div className="p-6">
                        <div className="flex items-center mb-4">
                          <IconComponent className="h-8 w-8 text-blue-600 mr-3" />
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">{service.name}</h3>
                            <p className="text-sm text-gray-500">{service.category_name}</p>
                          </div>
                        </div>
                        
                        <p className="text-gray-600 mb-4 line-clamp-3">{service.short_description || service.description}</p>
                        
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <span className="text-2xl font-bold text-blue-600">
                              {formatPrice(minPrice)}
                            </span>
                            {service.pricing.length > 1 && (
                              <span className="text-sm text-gray-500 ml-1">starting</span>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-500">Est. Time</p>
                            <p className="font-semibold">{service.estimated_time_hours}h</p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-sm text-gray-500">
                            <ShieldCheckIcon className="h-4 w-4 mr-1" />
                            {service.warranty_days} days warranty
                          </div>
                          <Link 
                            href={`/repair/quote?service=${service.slug}`}
                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                          >
                            Get Quote
                          </Link>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}

            {!loading && services.length === 0 && (
              <div className="text-center py-12">
                <WrenchScrewdriverIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No services found</h3>
                <p className="text-gray-600">Try adjusting your filters to see more repair services.</p>
              </div>
            )}
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-blue-600 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-4">Need a Custom Repair Quote?</h2>
            <p className="text-xl mb-8">Our expert technicians can diagnose and repair any device issue.</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/repair/quote" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Get Free Estimate
              </Link>
              <Link href="/contact" className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                Contact Us
              </Link>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}

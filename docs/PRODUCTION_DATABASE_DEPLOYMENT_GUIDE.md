# 🗄️ MIDAS TECHNICAL PRODUCTION DATABASE DEPLOYMENT GUIDE

## 📋 Overview

This guide provides step-by-step instructions for deploying a production-ready PostgreSQL database for the Midas Technical e-commerce platform with comprehensive repair services.

## 🎯 Requirements Met

✅ **PostgreSQL 14+** with production optimizations  
✅ **SSL/TLS 1.2+** encryption for secure connections  
✅ **Automated daily backups** to AWS S3 with 30-day retention  
✅ **Database user** with appropriate permissions (no superuser)  
✅ **Comprehensive monitoring** with alerts and performance tracking  
✅ **Connection pooling** with PgBouncer (max 100 connections)  
✅ **Performance optimization** with proper memory allocation  
✅ **Complete schema import** with all 565+ products and repair system  

## 🚀 Quick Start

### Prerequisites

- Ubuntu 20.04+ or Debian 11+ server
- Minimum 4GB RAM and 2+ CPU cores
- Root access to the server
- AWS account for backups (optional but recommended)

### Step 1: Initial Database Setup

```bash
# Clone the repository
git clone <repository-url>
cd MDTSTech.store

# Make scripts executable
chmod +x Scripts/*.sh

# Run the main database setup
sudo bash Scripts/production-database-setup.sh
```

### Step 2: Import Database Schema

```bash
# Import complete schema and seed data
sudo bash Scripts/import-database-schema.sh
```

### Step 3: Configure Automated Backups

```bash
# Set up AWS S3 backups with 30-day retention
sudo bash Scripts/setup-database-backups.sh

# Configure AWS credentials
aws configure
```

### Step 4: Enable Monitoring

```bash
# Set up comprehensive monitoring
sudo bash Scripts/setup-database-monitoring.sh
```

## 📊 Database Configuration Details

### Performance Settings

The setup automatically configures optimal PostgreSQL settings based on your server's RAM:

- **Shared Buffers**: 25% of available RAM
- **Effective Cache Size**: 75% of available RAM
- **Work Memory**: 16MB per operation
- **Maintenance Work Memory**: 256MB
- **Max Connections**: 100 (with PgBouncer pooling)

### Security Configuration

- **SSL/TLS 1.2+** encryption enabled
- **Dedicated database user** with minimal required permissions
- **Connection restrictions** to application servers only
- **Password authentication** with encrypted passwords
- **No superuser access** for application connections

### Monitoring Features

- **Query performance monitoring** (logs queries >1000ms)
- **Connection usage tracking** with alerts at 80%/95%
- **Disk space monitoring** with alerts at 80%/90%
- **Memory usage monitoring**
- **Automated daily performance reports**
- **Real-time health checks** every 5 minutes

## 🗃️ Database Schema Overview

### E-commerce Tables
- `products` - Product catalog (565+ items)
- `categories` - Product categories (21 categories)
- `orders` - Customer orders
- `order_items` - Order line items
- `users` - Customer and admin accounts
- `cart_items` - Shopping cart contents

### Repair System Tables
- `repair_tickets` - Repair service requests
- `repair_services` - Available repair services
- `repair_service_categories` - Service categorization
- `device_types` - Supported device models (40+ devices)
- `repair_technicians` - Technician management
- `repair_parts` - Parts inventory

### Inventory Management
- `inventory_transactions` - Stock movement tracking
- `stock_alerts` - Low stock notifications
- `purchase_orders` - Supplier orders
- `suppliers` - Supplier information

### Payment & Shipping
- `payment_sessions` - Stripe payment tracking
- `webhook_logs` - Payment webhook logs
- `shipping_labels` - Shipping label management
- `order_status_history` - Order status tracking

## 🔧 Management Commands

### Database Operations

```bash
# Connect to database
psql -h localhost -U midastechnical_user -d midastechnical_store

# Check database status
sudo systemctl status postgresql

# Restart PostgreSQL
sudo systemctl restart postgresql

# View PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-14-main.log
```

### Backup Operations

```bash
# Manual backup
sudo /usr/local/bin/midas-db-backup.sh

# Check backup status
sudo /usr/local/bin/midas-backup-status.sh

# View backup logs
sudo tail -f /var/log/midas-db-backup.log

# List S3 backups
aws s3 ls s3://midastechnical-backups/database-backups/ --recursive
```

### Monitoring Operations

```bash
# Manual health check
sudo /usr/local/bin/midas-db-monitor.sh

# Generate performance report
sudo /usr/local/bin/midas-db-report.sh

# View monitoring logs
sudo tail -f /var/log/midas-db-monitor.log

# View daily reports
sudo tail -f /var/log/midas-db-daily-report.log
```

### Connection Pooling

```bash
# Check PgBouncer status
sudo systemctl status pgbouncer

# Connect via PgBouncer
psql -h localhost -p 6432 -U midastechnical_user -d midastechnical_store

# View PgBouncer stats
psql -h localhost -p 6432 -U midastechnical_user -d pgbouncer -c "SHOW STATS;"
```

## 🔗 Connection Strings

### Direct Database Connection
```
postgresql://midastechnical_user:PASSWORD@localhost:5432/midastechnical_store
```

### Pooled Connection (Recommended)
```
postgresql://midastechnical_user:PASSWORD@localhost:6432/midastechnical_store
```

### SSL Connection
```
postgresql://midastechnical_user:PASSWORD@localhost:5432/midastechnical_store?sslmode=require
```

## 📈 Performance Optimization

### Indexes Created

The schema includes optimized indexes for:
- Product searches and filtering
- Order queries by user and status
- Repair ticket management
- Inventory tracking
- Payment processing

### Query Optimization

- **pg_stat_statements** enabled for query analysis
- **Slow query monitoring** (>1000ms queries logged)
- **Connection statistics** tracking
- **Table size monitoring**

## 🚨 Monitoring & Alerts

### Alert Thresholds

- **Disk Space**: Warning at 80%, Critical at 90%
- **Connections**: Warning at 80%, Critical at 95%
- **Memory Usage**: Warning at 90%
- **Slow Queries**: Alert when >10 queries >1000ms

### Monitoring Schedule

- **Health Checks**: Every 5 minutes
- **Daily Reports**: 6:00 AM
- **Backup Verification**: After each backup
- **Log Rotation**: 30 days for monitoring, 12 weeks for reports

## 🔄 Backup & Recovery

### Backup Strategy

- **Frequency**: Daily at 2:00 AM
- **Retention**: 30 days in S3, 7 days local
- **Compression**: gzip compression for storage efficiency
- **Verification**: Automated backup success verification

### Recovery Procedure

```bash
# Download backup from S3
aws s3 cp s3://midastechnical-backups/database-backups/YYYY/MM/DD/backup.sql.gz ./

# Decompress backup
gunzip backup.sql.gz

# Restore database (WARNING: This will overwrite existing data)
psql -h localhost -U midastechnical_user -d midastechnical_store < backup.sql
```

## 🔒 Security Best Practices

### Database Security

- ✅ SSL/TLS encryption enabled
- ✅ Non-superuser database account
- ✅ Connection restrictions by IP
- ✅ Password authentication
- ✅ Regular security updates

### Access Control

- ✅ Minimal required permissions
- ✅ Separate backup user (if needed)
- ✅ Audit logging enabled
- ✅ Connection logging

## 🧪 Testing & Validation

### Post-Deployment Tests

```bash
# Test database connection
psql -h localhost -U midastechnical_user -d midastechnical_store -c "SELECT version();"

# Verify data import
psql -h localhost -U midastechnical_user -d midastechnical_store -c "
SELECT 
    (SELECT COUNT(*) FROM products WHERE is_active = true) as products,
    (SELECT COUNT(*) FROM categories WHERE is_active = true) as categories,
    (SELECT COUNT(*) FROM repair_service_categories) as repair_categories,
    (SELECT COUNT(*) FROM device_types) as device_types;
"

# Test backup system
sudo /usr/local/bin/midas-db-backup.sh

# Test monitoring
sudo /usr/local/bin/midas-db-monitor.sh
```

## 📞 Support & Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check PostgreSQL service: `sudo systemctl status postgresql`
   - Verify pg_hba.conf configuration
   - Check firewall settings

2. **Backup Failures**
   - Verify AWS credentials: `aws sts get-caller-identity`
   - Check S3 bucket permissions
   - Review backup logs

3. **Performance Issues**
   - Run performance report: `sudo /usr/local/bin/midas-db-report.sh`
   - Check slow queries view
   - Monitor system resources

### Log Locations

- **PostgreSQL**: `/var/log/postgresql/`
- **Backup Logs**: `/var/log/midas-db-backup.log`
- **Monitoring Logs**: `/var/log/midas-db-monitor.log`
- **Daily Reports**: `/var/log/midas-db-daily-report.log`

## 🎯 Success Criteria

✅ **Database Performance**: Queries <100ms average  
✅ **Uptime**: >99.9% availability  
✅ **Backup Success**: 100% successful daily backups  
✅ **Security**: SSL encryption and proper access controls  
✅ **Monitoring**: Real-time alerts and daily reports  
✅ **Data Integrity**: All 565+ products and repair system data imported  

---

**🎉 Your Midas Technical production database is now ready for high-performance e-commerce operations!**

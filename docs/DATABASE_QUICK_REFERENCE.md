# 🚀 MIDAS TECHNICAL DATABASE QUICK REFERENCE

## 📋 One-Command Deployment

```bash
# Complete database setup (run as root)
sudo bash Scripts/production-database-setup.sh && \
sudo bash Scripts/import-database-schema.sh && \
sudo bash Scripts/setup-database-backups.sh && \
sudo bash Scripts/setup-database-monitoring.sh && \
sudo bash Scripts/validate-production-database.sh
```

## 🔗 Connection Strings

### Production Application (.env.local)
```bash
# Direct connection
DATABASE_URL=postgresql://midastechnical_user:PASSWORD@localhost:5432/midastechnical_store

# Pooled connection (recommended)
DATABASE_URL=postgresql://midastechnical_user:PASSWORD@localhost:6432/midastechnical_store

# SSL connection
DATABASE_URL=postgresql://midastechnical_user:PASSWORD@localhost:5432/midastechnical_store?sslmode=require
```

## 🛠️ Essential Commands

### Database Management
```bash
# Connect to database
psql -h localhost -U midastechnical_user -d midastechnical_store

# Check service status
sudo systemctl status postgresql
sudo systemctl status pgbouncer

# Restart services
sudo systemctl restart postgresql
sudo systemctl restart pgbouncer
```

### Backup Operations
```bash
# Manual backup
sudo /usr/local/bin/midas-db-backup.sh

# Check backup status
sudo /usr/local/bin/midas-backup-status.sh

# View backup logs
sudo tail -f /var/log/midas-db-backup.log
```

### Monitoring
```bash
# Health check
sudo /usr/local/bin/midas-db-monitor.sh

# Performance report
sudo /usr/local/bin/midas-db-report.sh

# View monitoring logs
sudo tail -f /var/log/midas-db-monitor.log
```

### Validation
```bash
# Validate entire setup
sudo bash Scripts/validate-production-database.sh

# Quick connection test
psql -h localhost -U midastechnical_user -d midastechnical_store -c "SELECT version();"
```

## 📊 Key Metrics

### Expected Data Counts
- **Products**: 565+ active products
- **Categories**: 21+ active categories
- **Repair Categories**: 10+ service categories
- **Device Types**: 40+ supported devices
- **Inventory Value**: $583,000+

### Performance Targets
- **Query Response**: <100ms average
- **Connection Pool**: Max 100 connections
- **Backup Success**: 100% daily success rate
- **Uptime**: >99.9% availability

## 🔒 Security Checklist

- ✅ SSL/TLS 1.2+ encryption
- ✅ Non-superuser database account
- ✅ Connection restrictions
- ✅ Password authentication
- ✅ Audit logging enabled

## 📁 Important File Locations

### Configuration Files
- Database credentials: `/root/database_credentials.txt`
- PostgreSQL config: `/etc/postgresql/14/main/postgresql.conf`
- Authentication: `/etc/postgresql/14/main/pg_hba.conf`
- PgBouncer config: `/etc/pgbouncer/pgbouncer.ini`

### Log Files
- PostgreSQL: `/var/log/postgresql/`
- Backup logs: `/var/log/midas-db-backup.log`
- Monitoring: `/var/log/midas-db-monitor.log`
- Daily reports: `/var/log/midas-db-daily-report.log`

### Scripts
- Backup: `/usr/local/bin/midas-db-backup.sh`
- Monitoring: `/usr/local/bin/midas-db-monitor.sh`
- Reports: `/usr/local/bin/midas-db-report.sh`
- Status: `/usr/local/bin/midas-backup-status.sh`

## 🚨 Troubleshooting

### Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check port availability
sudo netstat -tlnp | grep :5432

# Test local connection
sudo -u postgres psql -c "SELECT version();"
```

### Performance Issues
```bash
# Check slow queries
psql -h localhost -U midastechnical_user -d midastechnical_store -c "SELECT * FROM slow_queries LIMIT 5;"

# Check connection usage
psql -h localhost -U midastechnical_user -d midastechnical_store -c "SELECT count(*) FROM pg_stat_activity;"

# System resources
htop
df -h
free -h
```

### Backup Issues
```bash
# Check AWS credentials
aws sts get-caller-identity

# Test S3 access
aws s3 ls s3://midastechnical-backups/

# Manual backup test
sudo /usr/local/bin/midas-db-backup.sh
```

## 📞 Emergency Procedures

### Database Recovery
```bash
# Stop application
sudo systemctl stop nginx  # or your web server

# Download latest backup
aws s3 cp s3://midastechnical-backups/database-backups/latest/backup.sql.gz ./

# Restore database
gunzip backup.sql.gz
psql -h localhost -U midastechnical_user -d midastechnical_store < backup.sql

# Restart application
sudo systemctl start nginx
```

### Performance Emergency
```bash
# Kill long-running queries
psql -h localhost -U midastechnical_user -d midastechnical_store -c "
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'active' AND query_start < now() - interval '5 minutes';"

# Restart PostgreSQL
sudo systemctl restart postgresql
```

## 🎯 Success Indicators

### Healthy Database Signs
- ✅ All validation tests pass
- ✅ Daily backups completing successfully
- ✅ Query response times <100ms
- ✅ Connection pool utilization <80%
- ✅ No critical alerts in monitoring logs

### Warning Signs
- ⚠️ Disk usage >80%
- ⚠️ Connection pool >80% utilized
- ⚠️ Slow queries increasing
- ⚠️ Backup failures
- ⚠️ Memory usage >90%

---

**🎉 Your Midas Technical production database is ready for high-performance e-commerce operations!**

For detailed information, see: `docs/PRODUCTION_DATABASE_DEPLOYMENT_GUIDE.md`

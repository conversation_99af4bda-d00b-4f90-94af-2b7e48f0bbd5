# 🚀 MIDAS TECHNICAL DOMAIN & SSL QUICK REFERENCE

## 📋 One-Command Deployment

```bash
# Complete domain and SSL setup (run on production server)
sudo bash Scripts/setup-production-domain.sh && \
sudo bash Scripts/update-production-config.sh && \
sudo bash Scripts/validate-ssl-domain.sh
```

## 🔗 Production URLs

### Main Application
```
Homepage:        https://midastechnical.com
Products:        https://midastechnical.com/products
Repair Services: https://midastechnical.com/repair
Admin Dashboard: https://midastechnical.com/admin
Customer Portal: https://midastechnical.com/account
```

### API Endpoints
```
Health Check:    https://midastechnical.com/api/health
Products API:    https://midastechnical.com/api/products
Repair API:      https://midastechnical.com/api/repair/services
Stripe Webhook:  https://midastechnical.com/api/webhooks/stripe
SendGrid Hook:   https://midastechnical.com/api/webhooks/sendgrid
```

## 🛠️ Essential Commands

### SSL Certificate Management
```bash
# Check certificate status
sudo certbot certificates

# Test auto-renewal
sudo certbot renew --dry-run

# View certificate details
openssl x509 -in /etc/letsencrypt/live/midastechnical.com/fullchain.pem -text -noout

# Check SSL connection
openssl s_client -connect midastechnical.com:443 -servername midastechnical.com
```

### Nginx Management
```bash
# Test configuration
sudo nginx -t

# Reload configuration
sudo systemctl reload nginx

# Check status
sudo systemctl status nginx

# View access logs
sudo tail -f /var/log/nginx/access.log

# View error logs
sudo tail -f /var/log/nginx/error.log
```

### Application Management
```bash
# Start production service
sudo systemctl start midastechnical

# Check service status
sudo systemctl status midastechnical

# View application logs
sudo journalctl -u midastechnical -f

# Restart application
sudo systemctl restart midastechnical
```

### Validation and Testing
```bash
# Comprehensive validation
sudo bash Scripts/validate-ssl-domain.sh

# Quick SSL test
curl -I https://midastechnical.com

# Test HTTP redirect
curl -I http://midastechnical.com

# Test specific endpoints
curl https://midastechnical.com/api/health
```

## 📧 Email Configuration

### Updated Email Addresses
```
From:        <EMAIL>
Reply-To:    <EMAIL>
Admin:       <EMAIL>
Business:    <EMAIL>
```

### SendGrid DNS Records
```
TXT Record:  "v=spf1 include:sendgrid.net ~all"
CNAME s1._domainkey: s1.domainkey.u12345.wl123.sendgrid.net
CNAME s2._domainkey: s2.domainkey.u12345.wl123.sendgrid.net
```

## 💳 Stripe Configuration

### Webhook URLs to Update
```
Main Webhook: https://midastechnical.com/api/webhooks/stripe

Required Events:
- checkout.session.completed
- payment_intent.succeeded
- payment_intent.payment_failed
- invoice.payment_succeeded
- customer.subscription.created
- customer.subscription.updated
- customer.subscription.deleted
```

## 🔍 Validation Tools

### External Testing
```
SSL Labs:        https://www.ssllabs.com/ssltest/analyze.html?d=midastechnical.com
Security Headers: https://securityheaders.com/?q=https://midastechnical.com
PageSpeed:       https://pagespeed.web.dev/report?url=https://midastechnical.com
GTmetrix:        https://gtmetrix.com/?url=https://midastechnical.com
```

### Expected Results
```
SSL Labs Grade:     A+
Security Headers:   A+
Page Load Time:     < 3 seconds
HTTP Redirect:      301 to HTTPS
All Endpoints:      200 response codes
```

## 🔧 Configuration Files

### Important File Locations
```
Nginx Config:       /etc/nginx/sites-available/midastechnical.com
SSL Certificates:   /etc/letsencrypt/live/midastechnical.com/
Environment:        .env.local
Next.js Config:     next.config.js
Systemd Service:    /etc/systemd/system/midastechnical.service
```

### Environment Variables
```bash
# Key production variables
NODE_ENV=production
NEXTAUTH_URL=https://midastechnical.com
NEXT_PUBLIC_SITE_URL=https://midastechnical.com
NEXTAUTH_SECRET=[generated-secret]
```

## 🚨 Troubleshooting

### DNS Issues
```bash
# Check DNS propagation
dig midastechnical.com
dig www.midastechnical.com

# Test from different DNS servers
dig @******* midastechnical.com
dig @******* midastechnical.com
```

### SSL Issues
```bash
# Check certificate generation logs
sudo tail -f /var/log/letsencrypt/letsencrypt.log

# Test certificate renewal
sudo certbot renew --dry-run

# Verify domain ownership
sudo certbot certonly --webroot --webroot-path=/var/www/html -d midastechnical.com --dry-run
```

### Application Issues
```bash
# Check application logs
sudo journalctl -u midastechnical -n 50

# Test application manually
cd /path/to/app && sudo -u www-data npm run start:prod

# Check environment variables
sudo -u www-data env | grep NEXT
```

### Performance Issues
```bash
# Monitor server resources
htop
iotop
df -h

# Check Nginx performance
sudo tail -f /var/log/nginx/access.log | grep -E "HTTP/[0-9.]+ [45][0-9][0-9]"

# Test page load times
curl -o /dev/null -s -w "%{time_total}" https://midastechnical.com
```

## 📊 Monitoring Checklist

### Daily Checks
- [ ] SSL certificate validity
- [ ] Application health endpoints
- [ ] Nginx error logs
- [ ] Server resource usage

### Weekly Checks
- [ ] SSL Labs rating
- [ ] Security headers validation
- [ ] Performance metrics
- [ ] External service integrations

### Monthly Checks
- [ ] Certificate renewal logs
- [ ] Security updates
- [ ] Performance optimization
- [ ] Backup verification

## 🎯 Success Indicators

### Healthy Domain Signs
- ✅ SSL Labs A+ rating
- ✅ All validation tests pass
- ✅ HTTP redirects to HTTPS (301)
- ✅ Security headers present
- ✅ Page load times < 3 seconds
- ✅ All API endpoints responding

### Warning Signs
- ⚠️ SSL certificate expires < 30 days
- ⚠️ Page load times > 3 seconds
- ⚠️ Security headers missing
- ⚠️ HTTP not redirecting to HTTPS
- ⚠️ API endpoints returning errors

## 📞 Emergency Procedures

### SSL Certificate Emergency
```bash
# Force certificate renewal
sudo certbot renew --force-renewal

# Restart Nginx
sudo systemctl restart nginx

# Check certificate status
sudo certbot certificates
```

### Application Down Emergency
```bash
# Check service status
sudo systemctl status midastechnical

# Restart application
sudo systemctl restart midastechnical

# Check logs for errors
sudo journalctl -u midastechnical -n 100

# Fallback: restart Nginx
sudo systemctl restart nginx
```

---

**🌟 Your Midas Technical platform is now live with production domain and enterprise-grade SSL security!**

For detailed information, see: `docs/PRODUCTION_DOMAIN_SSL_GUIDE.md`

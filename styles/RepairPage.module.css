.repairPage {
  min-height: 100vh;
  background: #f9fafb;
}

.tabNavigation {
  background: #fff;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 100;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.tabs {
  display: flex;
  gap: 0;
  overflow-x: auto;
}

.tab {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
}

.tab:hover {
  color: #374151;
  background: #f9fafb;
}

.tab.active {
  color: #d4af37;
  border-bottom-color: #d4af37;
  background: #fffbeb;
}

.tabContent {
  padding: 2rem 0;
}

.overview {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.hero {
  background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
  color: #fff;
  padding: 4rem 0;
  text-align: center;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.heroContent h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.heroContent p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.heroActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  color: #fff;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
}

.primaryButton:hover {
  background: linear-gradient(135deg, #b8941f 0%, #9c7a1a 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.secondaryButton {
  background: transparent;
  color: #fff;
  border: 2px solid #fff;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
}

.secondaryButton:hover {
  background: #fff;
  color: #1e40af;
  transform: translateY(-2px);
}

.buttonIcon {
  width: 1.25rem;
  height: 1.25rem;
}

.servicesSection {
  background: #fff;
  padding: 4rem 0;
}

.servicesSection h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 3rem;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.serviceCard {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.serviceCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  border-color: #d4af37;
}

.serviceIcon {
  width: 4rem;
  height: 4rem;
  color: #d4af37;
  margin: 0 auto 1.5rem;
}

.serviceCard h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
}

.serviceCard p {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.featureList li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #374151;
  margin-bottom: 0.5rem;
}

.checkIcon {
  width: 1rem;
  height: 1rem;
  color: #10b981;
  flex-shrink: 0;
}

.featuresSection {
  background: #f9fafb;
  padding: 4rem 0;
}

.featuresSection h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 3rem;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.featureCard {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.featureCard:hover {
  transform: translateY(-2px);
}

.featureIcon {
  width: 3rem;
  height: 3rem;
  color: #d4af37;
  margin: 0 auto 1rem;
}

.featureCard h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.featureCard p {
  color: #6b7280;
  line-height: 1.6;
}

.processSection {
  background: #fff;
  padding: 4rem 0;
}

.processSection h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 3rem;
}

.processSteps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.processStep {
  text-align: center;
  padding: 1.5rem;
}

.stepNumber {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 auto 1rem;
}

.processStep h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.processStep p {
  color: #6b7280;
  line-height: 1.6;
}

.unauthorized {
  text-align: center;
  padding: 4rem 2rem;
  background: #fff;
  border-radius: 12px;
  margin: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.unauthorizedIcon {
  width: 4rem;
  height: 4rem;
  color: #ef4444;
  margin: 0 auto 1rem;
}

.unauthorized h2 {
  color: #1f2937;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.unauthorized p {
  color: #6b7280;
  font-size: 1.1rem;
}

.servicePrice {
  font-size: 1.25rem;
  font-weight: 700;
  color: #d4af37;
  margin: 1rem 0;
}

.serviceButton {
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 1rem;
  width: 100%;
}

.serviceButton:hover {
  background: linear-gradient(135deg, #b8941f 0%, #9c7a1a 100%);
  transform: translateY(-1px);
}

.quickActions {
  background: #f9fafb;
  padding: 4rem 0;
}

.quickActions h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 3rem;
}

.actionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.actionCard {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.actionCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  border-color: #d4af37;
}

.actionIcon {
  width: 3rem;
  height: 3rem;
  color: #d4af37;
  margin: 0 auto 1rem;
}

.actionCard h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.actionCard p {
  color: #6b7280;
  line-height: 1.6;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #d4af37;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  color: #6b7280;
  font-size: 1.1rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .heroContent h1 {
    font-size: 2rem;
  }

  .heroContent p {
    font-size: 1rem;
  }

  .heroActions {
    flex-direction: column;
    align-items: center;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .servicesGrid {
    grid-template-columns: 1fr;
  }

  .featuresGrid {
    grid-template-columns: 1fr;
  }

  .processSteps {
    grid-template-columns: 1fr;
  }

  .tabs {
    justify-content: flex-start;
  }

  .tab {
    padding: 1rem 1.5rem;
    font-size: 0.875rem;
  }

  .servicesSection h2,
  .featuresSection h2,
  .processSection h2 {
    font-size: 2rem;
  }
}

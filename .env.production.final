# =============================================================================
# PRODUCTION ENVIRONMENT - MIDASTECHNICAL.COM
# =============================================================================
# Generated: 2025-06-04T19:05:44.629Z
# Status: Production Ready

NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# =============================================================================
# CORE APPLICATION
# =============================================================================
NEXTAUTH_URL=https://midastechnical.com
NEXTAUTH_SECRET=e5fbc1483bfe59abcff26e2621d66e96996a1a2c3a93b0adc549edf2c0ae9432
JWT_SECRET=bce9d2aca613dc87c6a18da39d52b586c5704208c3afc8a0e387e67e5a8b1c84

# =============================================================================
# DATABASE CONFIGURATION (PRODUCTION)
# =============================================================================
# Replace with your production database URL
DATABASE_URL=postgresql://midastechnical_user:<EMAIL>:5432/midastechnical_store
DB_SSL=true
DB_POOL_MIN=2
DB_POOL_MAX=20

# =============================================================================
# STRIPE PAYMENT PROCESSING (PRODUCTION)
# =============================================================================
# Replace with your production Stripe keys
STRIPE_SECRET_KEY=sk_live_YOUR_PRODUCTION_SECRET_KEY
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_PRODUCTION_PUBLISHABLE_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_PRODUCTION_WEBHOOK_SECRET
STRIPE_WEBHOOK_ENDPOINT=https://midastechnical.com/api/webhooks/stripe

# =============================================================================
# EMAIL SERVICE (PRODUCTION)
# =============================================================================
# Replace with your production SendGrid API key
SENDGRID_API_KEY=SG.YOUR_PRODUCTION_SENDGRID_API_KEY
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Midas Technical Solutions

# =============================================================================
# CLOUDINARY CDN (PRODUCTION)
# =============================================================================
# Replace with your production Cloudinary credentials
CLOUDINARY_CLOUD_NAME=midastechnical-prod
CLOUDINARY_API_KEY=YOUR_PRODUCTION_API_KEY
CLOUDINARY_API_SECRET=YOUR_PRODUCTION_API_SECRET
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=midastechnical-prod

# =============================================================================
# ANALYTICS & MONITORING (PRODUCTION)
# =============================================================================
# Replace with your production analytics IDs
NEXT_PUBLIC_GA_TRACKING_ID=G-YOUR_PRODUCTION_GA_ID
NEXT_PUBLIC_GTM_ID=GTM-YOUR_PRODUCTION_GTM_ID
SENTRY_DSN=https://<EMAIL>/PROJECT_ID
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/PROJECT_ID

# =============================================================================
# SECURITY & PERFORMANCE
# =============================================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=https://midastechnical.com
CDN_URL=https://cdn.midastechnical.com

# =============================================================================
# EXTERNAL INTEGRATIONS
# =============================================================================
# Notion CMS (Production)
NOTION_API_KEY=secret_YOUR_PRODUCTION_NOTION_API_KEY
NOTION_PRODUCTS_DATABASE_ID=YOUR_PRODUCTION_PRODUCTS_DB_ID
NOTION_ORDERS_DATABASE_ID=YOUR_PRODUCTION_ORDERS_DB_ID

# Twilio SMS (Production)
TWILIO_ACCOUNT_SID=YOUR_PRODUCTION_TWILIO_SID
TWILIO_AUTH_TOKEN=YOUR_PRODUCTION_TWILIO_TOKEN
TWILIO_PHONE_NUMBER=+1YOUR_PRODUCTION_PHONE

# =============================================================================
# BACKUP & MONITORING
# =============================================================================
AWS_ACCESS_KEY_ID=YOUR_PRODUCTION_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=YOUR_PRODUCTION_AWS_SECRET_KEY
AWS_REGION=us-east-1
AWS_S3_BACKUP_BUCKET=midastechnical-backups-prod

# Redis Cache (Production)
REDIS_URL=redis://<EMAIL>:6379

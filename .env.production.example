# =====================================================
# MIDAS TECHNICAL PRODUCTION ENVIRONMENT CONFIGURATION
# =====================================================
# Copy this file to .env.local and fill in your production values

# =====================================================
# BASIC APPLICATION SETTINGS
# =====================================================
NODE_ENV=production
NEXTAUTH_URL=https://midastechnical.com
NEXTAUTH_SECRET=your-super-secure-nextauth-secret-here

# =====================================================
# DATABASE CONFIGURATION
# =====================================================
DATABASE_URL=****************************************/midastechnical_store

# =====================================================
# STRIPE PAYMENT CONFIGURATION (LIVE KEYS)
# =====================================================
# Live Stripe Keys (for production)
STRIPE_SECRET_KEY_LIVE=sk_live_your_live_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_LIVE=pk_live_your_live_publishable_key_here
STRIPE_WEBHOOK_SECRET_LIVE=whsec_your_live_webhook_secret_here

# Test Stripe Keys (for development/testing)
STRIPE_SECRET_KEY_TEST=sk_test_your_test_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_TEST=pk_test_your_test_publishable_key_here
STRIPE_WEBHOOK_SECRET_TEST=whsec_your_test_webhook_secret_here

# Default keys (will use live in production, test in development)
STRIPE_SECRET_KEY=sk_live_your_live_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret_here

# =====================================================
# EMAIL CONFIGURATION
# =====================================================
# SMTP Settings for transactional emails
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# Admin email for notifications
ADMIN_EMAIL=<EMAIL>

# =====================================================
# CLOUDINARY CONFIGURATION (Image Management)
# =====================================================
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# =====================================================
# ANALYTICS AND MONITORING
# =====================================================
# Google Analytics
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Sentry Error Tracking
SENTRY_DSN=https://<EMAIL>/project-id

# =====================================================
# REDIS CONFIGURATION (Optional - for caching)
# =====================================================
REDIS_URL=redis://localhost:6379

# =====================================================
# SHIPPING INTEGRATION
# =====================================================
# UPS API Configuration
UPS_ACCESS_KEY=your-ups-access-key
UPS_USERNAME=your-ups-username
UPS_PASSWORD=your-ups-password
UPS_ACCOUNT_NUMBER=your-ups-account-number

# FedEx API Configuration
FEDEX_KEY=your-fedex-key
FEDEX_PASSWORD=your-fedex-password
FEDEX_ACCOUNT_NUMBER=your-fedex-account-number
FEDEX_METER_NUMBER=your-fedex-meter-number

# =====================================================
# BUSINESS CONFIGURATION
# =====================================================
# Business Information
BUSINESS_NAME=Midas Technical Store
BUSINESS_ADDRESS=123 Tech Street, Silicon Valley, CA 94000
BUSINESS_PHONE=1-555-MIDAS-TECH
BUSINESS_EMAIL=<EMAIL>

# Tax Configuration
TAX_RATE=0.0875
TAX_ENABLED=true

# Shipping Configuration
FREE_SHIPPING_THRESHOLD=75.00
STANDARD_SHIPPING_RATE=9.99
EXPRESS_SHIPPING_RATE=19.99

# =====================================================
# SECURITY SETTINGS
# =====================================================
# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_MAX_AGE=2592000
SESSION_UPDATE_AGE=86400

# =====================================================
# FEATURE FLAGS
# =====================================================
# Enable/disable features
ENABLE_REPAIR_SERVICES=true
ENABLE_SUBSCRIPTIONS=false
ENABLE_MARKETPLACE=false
ENABLE_REVIEWS=true
ENABLE_WISHLIST=true
ENABLE_LIVE_CHAT=true

# =====================================================
# THIRD-PARTY INTEGRATIONS
# =====================================================
# Notion Integration (for CMS)
NOTION_TOKEN=your-notion-integration-token
NOTION_DATABASE_ID=your-notion-database-id

# Slack Integration (for notifications)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook

# =====================================================
# DEVELOPMENT/DEBUG SETTINGS
# =====================================================
# Only set these in development
# DEBUG=true
# LOG_LEVEL=debug
# DISABLE_SSL_VERIFY=false

# =====================================================
# BACKUP CONFIGURATION
# =====================================================
# AWS S3 for backups
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BACKUP_BUCKET=midastechnical-backups

# =====================================================
# CDN CONFIGURATION
# =====================================================
# CloudFront or other CDN
CDN_URL=https://cdn.midastechnical.com
STATIC_ASSETS_URL=https://static.midastechnical.com

# =====================================================
# PERFORMANCE MONITORING
# =====================================================
# New Relic
NEW_RELIC_LICENSE_KEY=your-new-relic-license-key
NEW_RELIC_APP_NAME=Midas Technical Store

# =====================================================
# SOCIAL MEDIA INTEGRATION
# =====================================================
# Facebook
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Google
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# =====================================================
# INVENTORY MANAGEMENT
# =====================================================
# Stock alert thresholds
LOW_STOCK_THRESHOLD=10
CRITICAL_STOCK_THRESHOLD=5
AUTO_REORDER_ENABLED=false

# =====================================================
# REPAIR SERVICE CONFIGURATION
# =====================================================
# Business hours for repair services
REPAIR_BUSINESS_HOURS={"monday":"9:00-17:00","tuesday":"9:00-17:00","wednesday":"9:00-17:00","thursday":"9:00-17:00","friday":"9:00-17:00","saturday":"10:00-15:00","sunday":"closed"}

# Emergency contact
REPAIR_EMERGENCY_CONTACT=+1-555-REPAIR

# Default warranty period (days)
DEFAULT_WARRANTY_DAYS=90

# =====================================================
# LEGAL AND COMPLIANCE
# =====================================================
# Privacy policy and terms URLs
PRIVACY_POLICY_URL=https://midastechnical.com/privacy
TERMS_OF_SERVICE_URL=https://midastechnical.com/terms
COOKIE_POLICY_URL=https://midastechnical.com/cookies

# GDPR Compliance
ENABLE_GDPR_COMPLIANCE=true
ENABLE_COOKIE_CONSENT=true

# =====================================================
# LOCALIZATION
# =====================================================
DEFAULT_LOCALE=en-US
SUPPORTED_LOCALES=en-US,es-ES,fr-FR
DEFAULT_CURRENCY=USD
SUPPORTED_CURRENCIES=USD,EUR,GBP,CAD

# =====================================================
# API KEYS AND EXTERNAL SERVICES
# =====================================================
# Geocoding service
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# SMS service (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# =====================================================
# MAINTENANCE MODE
# =====================================================
# Set to true to enable maintenance mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=We're currently performing scheduled maintenance. Please check back soon.

# =====================================================
# NOTES
# =====================================================
# 1. Never commit this file with real values to version control
# 2. Use strong, unique passwords and secrets
# 3. Rotate secrets regularly
# 4. Use environment-specific values for each deployment
# 5. Test all integrations in staging before production
# 6. Monitor all external service quotas and limits
# 7. Set up alerts for critical failures
# 8. Backup your environment variables securely

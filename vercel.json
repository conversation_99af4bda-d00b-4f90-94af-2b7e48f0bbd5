{"version": 2, "name": "midastechnical-com", "alias": ["midastechnical.com", "www.midastechnical.com"], "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/$1"}], "env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}, "functions": {"pages/api/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/shop", "destination": "/products", "permanent": true}]}
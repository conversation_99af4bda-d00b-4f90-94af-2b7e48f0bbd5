# Database
DATABASE_URL=postgres://postgres:password@localhost:5432/midastechnical

# NextAuth.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Stripe (for payments)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Email (for notifications)
EMAIL_SERVER=smtp://username:<EMAIL>:587
EMAIL_FROM=<EMAIL>

# Firebase
NEXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id

# Notion
NOTION_API_KEY=your-notion-api-key
NOTION_PRODUCTS_DATABASE_ID=your-products-database-id
NOTION_ORDERS_DATABASE_ID=your-orders-database-id
NOTION_CUSTOMERS_DATABASE_ID=your-customers-database-id
NOTION_CONTENT_DATABASE_ID=your-content-database-id

# Zapier Webhooks
ZAPIER_WEBHOOK_NEW_ORDER=https://hooks.zapier.com/hooks/catch/your-hook-id/new-order/
ZAPIER_WEBHOOK_LOW_INVENTORY=https://hooks.zapier.com/hooks/catch/your-hook-id/low-inventory/
ZAPIER_WEBHOOK_NEW_CUSTOMER=https://hooks.zapier.com/hooks/catch/your-hook-id/new-customer/
ZAPIER_WEBHOOK_ABANDONED_CART=https://hooks.zapier.com/hooks/catch/your-hook-id/abandoned-cart/
ZAPIER_WEBHOOK_PRODUCT_REVIEW=https://hooks.zapier.com/hooks/catch/your-hook-id/product-review/
ZAPIER_WEBHOOK_SUPPORT_REQUEST=https://hooks.zapier.com/hooks/catch/your-hook-id/support-request/

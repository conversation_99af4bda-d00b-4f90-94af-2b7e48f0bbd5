import React, { useState } from 'react';
import Link from 'next/link';
import {
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ClockIcon,
  WrenchScrewdriverIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import styles from './Footer.module.css';

const Footer = () => {
  const [email, setEmail] = useState('');
  const [subscribed, setSubscribed] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleNewsletterSubmit = async (e) => {
    e.preventDefault();
    if (!email.trim()) return;

    setLoading(true);
    try {
      // Simulate newsletter subscription
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSubscribed(true);
      setEmail('');
    } catch (error) {
      console.error('Newsletter subscription error:', error);
    } finally {
      setLoading(false);
    }
  };

  const currentYear = new Date().getFullYear();

  return (
    <footer className={styles.footer}>
      {/* Newsletter Section */}
      <div className={styles.newsletter}>
        <div className={styles.container}>
          <div className={styles.newsletterContent}>
            <div className={styles.newsletterText}>
              <h3>Stay Updated with Repair Tips & Deals</h3>
              <p>Get the latest repair guides, product updates, and exclusive offers delivered to your inbox.</p>
            </div>
            
            {subscribed ? (
              <div className={styles.subscriptionSuccess}>
                <ShieldCheckIcon className={styles.successIcon} />
                <span>Thank you for subscribing!</span>
              </div>
            ) : (
              <form onSubmit={handleNewsletterSubmit} className={styles.newsletterForm}>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className={styles.newsletterInput}
                  required
                />
                <button 
                  type="submit" 
                  className={styles.newsletterButton}
                  disabled={loading}
                >
                  {loading ? 'Subscribing...' : 'Subscribe'}
                </button>
              </form>
            )}
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className={styles.mainFooter}>
        <div className={styles.container}>
          <div className={styles.footerGrid}>
            
            {/* Company Info */}
            <div className={styles.footerColumn}>
              <div className={styles.companyInfo}>
                <h3 className={styles.companyName}>Midas Technical</h3>
                <p className={styles.companyDescription}>
                  Your trusted partner for professional device repair services and quality electronic components. 
                  Expert technicians, genuine parts, and comprehensive warranties.
                </p>
                
                <div className={styles.contactInfo}>
                  <div className={styles.contactItem}>
                    <MapPinIcon className={styles.contactIcon} />
                    <span>Vienna, VA 22182, USA</span>
                  </div>
                  <div className={styles.contactItem}>
                    <PhoneIcon className={styles.contactIcon} />
                    <span>+****************</span>
                  </div>
                  <div className={styles.contactItem}>
                    <EnvelopeIcon className={styles.contactIcon} />
                    <span><EMAIL></span>
                  </div>
                </div>
              </div>
            </div>

            {/* Repair Services */}
            <div className={styles.footerColumn}>
              <h4 className={styles.columnTitle}>
                <WrenchScrewdriverIcon className={styles.titleIcon} />
                Repair Services
              </h4>
              <ul className={styles.linkList}>
                <li>
                  <Link href="/repair" className={styles.footerLink}>
                    All Repair Services
                  </Link>
                </li>
                <li>
                  <Link href="/repair/quote" className={styles.footerLink}>
                    Get Free Quote
                  </Link>
                </li>
                <li>
                  <Link href="/repair/track" className={styles.footerLink}>
                    Track Your Repair
                  </Link>
                </li>
                <li>
                  <Link href="/repair?category=screen-repair" className={styles.footerLink}>
                    Screen Repair
                  </Link>
                </li>
                <li>
                  <Link href="/repair?category=battery-replacement" className={styles.footerLink}>
                    Battery Replacement
                  </Link>
                </li>
                <li>
                  <Link href="/repair?category=water-damage" className={styles.footerLink}>
                    Water Damage Repair
                  </Link>
                </li>
                <li>
                  <Link href="/repair/warranty" className={styles.footerLink}>
                    Warranty Information
                  </Link>
                </li>
              </ul>
            </div>

            {/* Products & Services */}
            <div className={styles.footerColumn}>
              <h4 className={styles.columnTitle}>Products & Services</h4>
              <ul className={styles.linkList}>
                <li>
                  <Link href="/products" className={styles.footerLink}>
                    All Products
                  </Link>
                </li>
                <li>
                  <Link href="/categories" className={styles.footerLink}>
                    Categories
                  </Link>
                </li>
                <li>
                  <Link href="/lcd-buyback" className={styles.footerLink}>
                    LCD Buyback Program
                  </Link>
                </li>
                <li>
                  <Link href="/gapp" className={styles.footerLink}>
                    Apple Parts Program
                  </Link>
                </li>
                <li>
                  <Link href="/wholesale" className={styles.footerLink}>
                    Wholesale Pricing
                  </Link>
                </li>
                <li>
                  <Link href="/bulk-orders" className={styles.footerLink}>
                    Bulk Orders
                  </Link>
                </li>
              </ul>
            </div>

            {/* Support & Legal */}
            <div className={styles.footerColumn}>
              <h4 className={styles.columnTitle}>Support & Legal</h4>
              <ul className={styles.linkList}>
                <li>
                  <Link href="/contact" className={styles.footerLink}>
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/contact?department=repair" className={styles.footerLink}>
                    Repair Department
                  </Link>
                </li>
                <li>
                  <Link href="/help" className={styles.footerLink}>
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="/shipping" className={styles.footerLink}>
                    Shipping Information
                  </Link>
                </li>
                <li>
                  <Link href="/returns" className={styles.footerLink}>
                    Returns & Refunds
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className={styles.footerLink}>
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className={styles.footerLink}>
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link href="/repair/terms" className={styles.footerLink}>
                    Repair Terms & Conditions
                  </Link>
                </li>
              </ul>
            </div>

          </div>

          {/* Repair Service Hours & Contact */}
          <div className={styles.serviceHours}>
            <div className={styles.serviceHoursGrid}>
              
              <div className={styles.hoursSection}>
                <h4 className={styles.hoursTitle}>
                  <ClockIcon className={styles.hoursIcon} />
                  Repair Service Hours
                </h4>
                <div className={styles.hoursContent}>
                  <div className={styles.hoursItem}>
                    <span className={styles.hoursDay}>Monday - Friday:</span>
                    <span className={styles.hoursTime}>9:00 AM - 10:00 PM EST</span>
                  </div>
                  <div className={styles.hoursItem}>
                    <span className={styles.hoursDay}>Saturday:</span>
                    <span className={styles.hoursTime}>10:00 AM - 8:00 PM EST</span>
                  </div>
                  <div className={styles.hoursItem}>
                    <span className={styles.hoursDay}>Sunday:</span>
                    <span className={styles.hoursTime}>12:00 PM - 6:00 PM EST</span>
                  </div>
                </div>
              </div>

              <div className={styles.repairContact}>
                <h4 className={styles.hoursTitle}>
                  <ChatBubbleLeftRightIcon className={styles.hoursIcon} />
                  Repair Support
                </h4>
                <div className={styles.repairContactContent}>
                  <div className={styles.contactMethod}>
                    <PhoneIcon className={styles.contactMethodIcon} />
                    <div>
                      <span className={styles.contactMethodLabel}>Phone Support:</span>
                      <span className={styles.contactMethodValue}>+****************</span>
                    </div>
                  </div>
                  <div className={styles.contactMethod}>
                    <EnvelopeIcon className={styles.contactMethodIcon} />
                    <div>
                      <span className={styles.contactMethodLabel}>Email Support:</span>
                      <span className={styles.contactMethodValue}><EMAIL></span>
                    </div>
                  </div>
                  <div className={styles.contactMethod}>
                    <WrenchScrewdriverIcon className={styles.contactMethodIcon} />
                    <div>
                      <span className={styles.contactMethodLabel}>Emergency Repairs:</span>
                      <span className={styles.contactMethodValue}>Available 24/7</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className={styles.warrantyInfo}>
                <h4 className={styles.hoursTitle}>
                  <ShieldCheckIcon className={styles.hoursIcon} />
                  Warranty & Quality
                </h4>
                <div className={styles.warrantyContent}>
                  <p>✓ 90-day warranty on all repairs</p>
                  <p>✓ Genuine parts guarantee</p>
                  <p>✓ Certified technicians</p>
                  <p>✓ Free diagnostic service</p>
                  <Link href="/repair/warranty" className={styles.warrantyLink}>
                    View Full Warranty Terms
                  </Link>
                </div>
              </div>

            </div>
          </div>

          {/* Payment Methods & Certifications */}
          <div className={styles.trustSection}>
            <div className={styles.paymentMethods}>
              <h5>Accepted Payment Methods:</h5>
              <div className={styles.paymentIcons}>
                <div className={styles.paymentIcon}>💳 Visa</div>
                <div className={styles.paymentIcon}>💳 Mastercard</div>
                <div className={styles.paymentIcon}>💳 American Express</div>
                <div className={styles.paymentIcon}>💰 PayPal</div>
                <div className={styles.paymentIcon}>🍎 Apple Pay</div>
                <div className={styles.paymentIcon}>📱 Google Pay</div>
              </div>
            </div>

            <div className={styles.certifications}>
              <h5>Certifications & Memberships:</h5>
              <div className={styles.certificationList}>
                <span>🏆 Better Business Bureau A+</span>
                <span>🔧 Certified Repair Technicians</span>
                <span>🛡️ ISO 9001 Quality Management</span>
                <span>♻️ R2 Responsible Recycling</span>
              </div>
            </div>
          </div>

        </div>
      </div>

      {/* Bottom Footer */}
      <div className={styles.bottomFooter}>
        <div className={styles.container}>
          <div className={styles.bottomContent}>
            <div className={styles.copyright}>
              <p>© {currentYear} Midas Technical Solutions. All rights reserved.</p>
              <p className={styles.disclaimer}>
                Professional device repair services with genuine parts and comprehensive warranties.
              </p>
            </div>

            <div className={styles.socialLinks}>
              <a href="#" className={styles.socialLink} aria-label="Facebook">
                📘
              </a>
              <a href="#" className={styles.socialLink} aria-label="Twitter">
                🐦
              </a>
              <a href="#" className={styles.socialLink} aria-label="LinkedIn">
                💼
              </a>
              <a href="#" className={styles.socialLink} aria-label="YouTube">
                📺
              </a>
            </div>

            <div className={styles.quickActions}>
              <Link href="/repair/quote" className={styles.quickAction}>
                Get Repair Quote
              </Link>
              <Link href="/repair/track" className={styles.quickAction}>
                Track Repair
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

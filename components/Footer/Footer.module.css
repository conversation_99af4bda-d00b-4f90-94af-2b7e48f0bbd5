/* Footer.module.css */
.footer {
  background: linear-gradient(135deg, #1f2937, #111827);
  color: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Newsletter Section */
.newsletter {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  padding: 40px 0;
}

.newsletterContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.newsletterText h3 {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.newsletterText p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.newsletterForm {
  display: flex;
  gap: 12px;
  min-width: 400px;
}

.newsletterInput {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #374151;
}

.newsletterInput:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.newsletterButton {
  padding: 12px 24px;
  background: #1f2937;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.newsletterButton:hover {
  background: #374151;
  transform: translateY(-1px);
}

.newsletterButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.subscriptionSuccess {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 8px;
  color: #22c55e;
  font-weight: 500;
}

.successIcon {
  width: 20px;
  height: 20px;
}

/* Main Footer */
.mainFooter {
  padding: 60px 0 40px;
}

.footerGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footerColumn {
  display: flex;
  flex-direction: column;
}

/* Company Info */
.companyInfo {
  max-width: 300px;
}

.companyName {
  font-size: 24px;
  font-weight: 700;
  color: #3b82f6;
  margin: 0 0 16px 0;
}

.companyDescription {
  font-size: 14px;
  line-height: 1.6;
  color: #d1d5db;
  margin: 0 0 24px 0;
}

.contactInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contactItem {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #e5e7eb;
}

.contactIcon {
  width: 18px;
  height: 18px;
  color: #3b82f6;
  flex-shrink: 0;
}

/* Column Titles */
.columnTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0 0 20px 0;
}

.titleIcon {
  width: 20px;
  height: 20px;
  color: #3b82f6;
}

/* Link Lists */
.linkList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footerLink {
  color: #d1d5db;
  text-decoration: none;
  font-size: 14px;
  padding: 4px 0;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.footerLink:hover {
  color: #3b82f6;
  padding-left: 8px;
}

/* Service Hours Section */
.serviceHours {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 40px;
}

.serviceHoursGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 40px;
}

.hoursSection,
.repairContact,
.warrantyInfo {
  display: flex;
  flex-direction: column;
}

.hoursTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin: 0 0 16px 0;
}

.hoursIcon {
  width: 18px;
  height: 18px;
  color: #3b82f6;
}

.hoursContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hoursItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.hoursDay {
  color: #d1d5db;
  font-weight: 500;
}

.hoursTime {
  color: #3b82f6;
  font-weight: 600;
}

/* Repair Contact */
.repairContactContent {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contactMethod {
  display: flex;
  align-items: center;
  gap: 12px;
}

.contactMethodIcon {
  width: 16px;
  height: 16px;
  color: #3b82f6;
  flex-shrink: 0;
}

.contactMethodLabel {
  font-size: 12px;
  color: #9ca3af;
  display: block;
}

.contactMethodValue {
  font-size: 14px;
  color: #e5e7eb;
  font-weight: 500;
  display: block;
}

/* Warranty Info */
.warrantyContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.warrantyContent p {
  font-size: 14px;
  color: #d1d5db;
  margin: 0;
}

.warrantyLink {
  color: #3b82f6;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
  transition: color 0.3s ease;
}

.warrantyLink:hover {
  color: #60a5fa;
}

/* Trust Section */
.trustSection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  padding: 32px 0;
  border-top: 1px solid rgba(55, 65, 81, 0.5);
  border-bottom: 1px solid rgba(55, 65, 81, 0.5);
}

.paymentMethods h5,
.certifications h5 {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.paymentIcons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.paymentIcon {
  padding: 8px 12px;
  background: rgba(55, 65, 81, 0.5);
  border-radius: 6px;
  font-size: 12px;
  color: #d1d5db;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.certificationList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.certificationList span {
  font-size: 12px;
  color: #d1d5db;
  padding: 4px 0;
}

/* Bottom Footer */
.bottomFooter {
  background: rgba(17, 24, 39, 0.8);
  padding: 24px 0;
}

.bottomContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.copyright {
  flex: 1;
}

.copyright p {
  margin: 0;
  font-size: 14px;
  color: #9ca3af;
}

.disclaimer {
  font-size: 12px !important;
  color: #6b7280 !important;
  margin-top: 4px !important;
}

.socialLinks {
  display: flex;
  gap: 12px;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(55, 65, 81, 0.5);
  border-radius: 50%;
  text-decoration: none;
  font-size: 16px;
  transition: all 0.3s ease;
}

.socialLink:hover {
  background: #3b82f6;
  transform: translateY(-2px);
}

.quickActions {
  display: flex;
  gap: 12px;
}

.quickAction {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.quickAction:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footerGrid {
    grid-template-columns: 1fr 1fr;
    gap: 32px;
  }
  
  .serviceHoursGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .trustSection {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .newsletterContent {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  
  .newsletterForm {
    min-width: auto;
    width: 100%;
  }
  
  .footerGrid {
    grid-template-columns: 1fr;
    gap: 32px;
  }
  
  .bottomContent {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .quickActions {
    flex-direction: column;
    width: 100%;
  }
  
  .quickAction {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }
  
  .newsletter {
    padding: 32px 0;
  }
  
  .mainFooter {
    padding: 40px 0 32px;
  }
  
  .serviceHours {
    padding: 24px;
  }
  
  .newsletterText h3 {
    font-size: 20px;
  }
  
  .paymentIcons {
    justify-content: center;
  }
  
  .socialLinks {
    justify-content: center;
  }
}

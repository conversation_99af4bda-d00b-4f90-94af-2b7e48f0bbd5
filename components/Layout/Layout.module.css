/* Layout.module.css */
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main {
  flex: 1;
  width: 100%;
}

.adminLayout {
  background-color: #f8fafc;
  min-height: calc(100vh - 160px); /* Adjust based on header/footer height */
}

/* Global styles for consistent spacing */
.main > * {
  width: 100%;
}

/* Ensure proper spacing for page content */
.main section,
.main article,
.main div[class*="container"] {
  width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .adminLayout {
    min-height: calc(100vh - 140px);
  }
}

/* Print styles */
@media print {
  .layout {
    background: white;
  }
  
  .main {
    background: white;
    color: black;
  }
}

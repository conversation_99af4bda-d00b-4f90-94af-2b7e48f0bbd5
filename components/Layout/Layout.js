import React from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Header from '../Header/Header';
import Footer from '../Footer/Footer';
import styles from './Layout.module.css';

const Layout = ({ 
  children, 
  title = 'Midas Technical - Professional Device Repair & Electronic Components',
  description = 'Expert device repair services and quality electronic components. Professional technicians, genuine parts, and comprehensive warranties.',
  keywords = 'device repair, phone repair, laptop repair, electronic components, screen replacement, battery replacement',
  canonical,
  noIndex = false,
  structuredData
}) => {
  const router = useRouter();
  const currentUrl = `https://midastechnical.com${router.asPath}`;
  const canonicalUrl = canonical || currentUrl;

  // Default structured data for the organization
  const defaultStructuredData = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Midas Technical",
    "description": "Professional device repair services and electronic components",
    "url": "https://midastechnical.com",
    "telephone": "******-351-0511",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Vienna",
      "addressRegion": "VA",
      "postalCode": "22182",
      "addressCountry": "US"
    },
    "openingHours": [
      "Mo-Fr 09:00-22:00",
      "Sa 10:00-20:00", 
      "Su 12:00-18:00"
    ],
    "serviceArea": {
      "@type": "Country",
      "name": "United States"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Repair Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Screen Repair",
            "description": "Professional screen replacement for phones, tablets, and laptops"
          }
        },
        {
          "@type": "Offer", 
          "itemOffered": {
            "@type": "Service",
            "name": "Battery Replacement",
            "description": "Battery replacement and power-related issue repairs"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service", 
            "name": "Water Damage Repair",
            "description": "Liquid damage assessment and restoration services"
          }
        }
      ]
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "247"
    },
    "priceRange": "$29.99 - $499.99"
  };

  // Merge custom structured data with default
  const finalStructuredData = structuredData 
    ? { ...defaultStructuredData, ...structuredData }
    : defaultStructuredData;

  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="keywords" content={keywords} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content={noIndex ? 'noindex,nofollow' : 'index,follow'} />
        
        {/* Canonical URL */}
        <link rel="canonical" href={canonicalUrl} />
        
        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content={currentUrl} />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:image" content="https://midastechnical.com/images/og-image.jpg" />
        <meta property="og:site_name" content="Midas Technical" />
        
        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content={currentUrl} />
        <meta property="twitter:title" content={title} />
        <meta property="twitter:description" content={description} />
        <meta property="twitter:image" content="https://midastechnical.com/images/twitter-image.jpg" />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        
        {/* Manifest */}
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#1e40af" />
        
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(finalStructuredData)
          }}
        />
        
        {/* Additional repair service structured data for repair pages */}
        {router.pathname.startsWith('/repair') && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                "@context": "https://schema.org",
                "@type": "RepairAction",
                "agent": {
                  "@type": "LocalBusiness",
                  "name": "Midas Technical",
                  "url": "https://midastechnical.com"
                },
                "object": "Electronic Devices",
                "result": "Restored Device Functionality",
                "instrument": "Professional Repair Tools and Genuine Parts"
              })
            }}
          />
        )}
      </Head>

      <div className={styles.layout}>
        <Header />
        
        <main className={styles.main}>
          {children}
        </main>
        
        <Footer />
      </div>
    </>
  );
};

export default Layout;

// Higher-order component for pages that need layout
export const withLayout = (WrappedComponent, layoutProps = {}) => {
  const WithLayoutComponent = (props) => {
    return (
      <Layout {...layoutProps}>
        <WrappedComponent {...props} />
      </Layout>
    );
  };

  WithLayoutComponent.displayName = `withLayout(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return WithLayoutComponent;
};

// Specialized layout components for different page types
export const RepairLayout = ({ children, ...props }) => {
  return (
    <Layout
      title={props.title || 'Professional Device Repair Services | Midas Technical'}
      description={props.description || 'Expert device repair services with fast turnaround, competitive pricing, and comprehensive warranty coverage.'}
      keywords={props.keywords || 'device repair, phone repair, laptop repair, screen replacement, battery replacement, water damage repair'}
      {...props}
    >
      {children}
    </Layout>
  );
};

export const ProductLayout = ({ children, ...props }) => {
  return (
    <Layout
      title={props.title || 'Electronic Components & Parts | Midas Technical'}
      description={props.description || 'Quality electronic components and replacement parts for device repair and electronics projects.'}
      keywords={props.keywords || 'electronic components, replacement parts, phone parts, laptop parts, repair parts'}
      {...props}
    >
      {children}
    </Layout>
  );
};

export const AdminLayout = ({ children, ...props }) => {
  return (
    <Layout
      title={props.title || 'Admin Dashboard | Midas Technical'}
      description="Administrative dashboard for managing repair services, orders, and business operations."
      noIndex={true}
      {...props}
    >
      <div className={styles.adminLayout}>
        {children}
      </div>
    </Layout>
  );
};

import {
  DocumentTextIcon,
  ClockIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  WrenchScrewdriverIcon,
  BeakerIcon,
  TruckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export default function RepairStatusBadge({ status, size = 'md', showIcon = true }) {
  const getStatusConfig = (status) => {
    const configs = {
      'submitted': {
        label: 'Submitted',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: DocumentTextIcon,
        description: 'Repair request submitted'
      },
      'received': {
        label: 'Received',
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: CheckCircleIcon,
        description: 'Device received at facility'
      },
      'diagnosed': {
        label: 'Diagnosed',
        color: 'bg-purple-100 text-purple-800 border-purple-200',
        icon: MagnifyingGlassIcon,
        description: 'Initial diagnosis completed'
      },
      'approved': {
        label: 'Approved',
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircleIcon,
        description: 'Repair approved and scheduled'
      },
      'in_progress': {
        label: 'In Progress',
        color: 'bg-orange-100 text-orange-800 border-orange-200',
        icon: WrenchScrewdriverIcon,
        description: 'Repair work in progress'
      },
      'testing': {
        label: 'Testing',
        color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
        icon: BeakerIcon,
        description: 'Quality testing and verification'
      },
      'completed': {
        label: 'Completed',
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircleIcon,
        description: 'Repair completed successfully'
      },
      'ready_pickup': {
        label: 'Ready for Pickup',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: TruckIcon,
        description: 'Ready for pickup or delivery'
      },
      'delivered': {
        label: 'Delivered',
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircleIcon,
        description: 'Device returned to customer'
      },
      'cancelled': {
        label: 'Cancelled',
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: ExclamationTriangleIcon,
        description: 'Repair cancelled'
      }
    };

    return configs[status] || {
      label: status,
      color: 'bg-gray-100 text-gray-800 border-gray-200',
      icon: ClockIcon,
      description: 'Unknown status'
    };
  };

  const config = getStatusConfig(status);
  const IconComponent = config.icon;

  const sizeClasses = {
    'sm': 'px-2 py-1 text-xs',
    'md': 'px-2.5 py-0.5 text-sm',
    'lg': 'px-3 py-1 text-base'
  };

  const iconSizes = {
    'sm': 'h-3 w-3',
    'md': 'h-4 w-4',
    'lg': 'h-5 w-5'
  };

  return (
    <span className={`
      inline-flex items-center rounded-full font-medium border
      ${config.color}
      ${sizeClasses[size]}
    `}>
      {showIcon && (
        <IconComponent className={`${iconSizes[size]} mr-1`} />
      )}
      {config.label}
    </span>
  );
}

export function RepairStatusProgress({ currentStatus, showLabels = true }) {
  const statusFlow = [
    'submitted',
    'received', 
    'diagnosed',
    'approved',
    'in_progress',
    'testing',
    'completed',
    'ready_pickup',
    'delivered'
  ];

  const currentIndex = statusFlow.indexOf(currentStatus);
  
  return (
    <div className="w-full">
      <div className="flex items-center">
        {statusFlow.map((status, index) => {
          const isCompleted = index <= currentIndex;
          const isCurrent = index === currentIndex;
          const config = getStatusConfig(status);
          const IconComponent = config.icon;

          return (
            <div key={status} className="flex items-center flex-1">
              {/* Step Circle */}
              <div className={`
                relative flex items-center justify-center w-8 h-8 rounded-full border-2
                ${isCompleted 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-400'
                }
                ${isCurrent ? 'ring-4 ring-blue-100' : ''}
              `}>
                {isCompleted ? (
                  <CheckCircleIcon className="w-5 h-5" />
                ) : (
                  <IconComponent className="w-4 h-4" />
                )}
              </div>

              {/* Step Label */}
              {showLabels && (
                <div className="ml-2 min-w-0 flex-1">
                  <p className={`text-sm font-medium ${
                    isCompleted ? 'text-gray-900' : 'text-gray-500'
                  }`}>
                    {config.label}
                  </p>
                  <p className="text-xs text-gray-500">{config.description}</p>
                </div>
              )}

              {/* Connector Line */}
              {index < statusFlow.length - 1 && (
                <div className={`
                  flex-1 h-0.5 mx-2
                  ${index < currentIndex ? 'bg-blue-600' : 'bg-gray-300'}
                `} />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

function getStatusConfig(status) {
  const configs = {
    'submitted': {
      label: 'Submitted',
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      icon: DocumentTextIcon,
      description: 'Repair request submitted'
    },
    'received': {
      label: 'Received',
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      icon: CheckCircleIcon,
      description: 'Device received at facility'
    },
    'diagnosed': {
      label: 'Diagnosed',
      color: 'bg-purple-100 text-purple-800 border-purple-200',
      icon: MagnifyingGlassIcon,
      description: 'Initial diagnosis completed'
    },
    'approved': {
      label: 'Approved',
      color: 'bg-green-100 text-green-800 border-green-200',
      icon: CheckCircleIcon,
      description: 'Repair approved and scheduled'
    },
    'in_progress': {
      label: 'In Progress',
      color: 'bg-orange-100 text-orange-800 border-orange-200',
      icon: WrenchScrewdriverIcon,
      description: 'Repair work in progress'
    },
    'testing': {
      label: 'Testing',
      color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      icon: BeakerIcon,
      description: 'Quality testing and verification'
    },
    'completed': {
      label: 'Completed',
      color: 'bg-green-100 text-green-800 border-green-200',
      icon: CheckCircleIcon,
      description: 'Repair completed successfully'
    },
    'ready_pickup': {
      label: 'Ready for Pickup',
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      icon: TruckIcon,
      description: 'Ready for pickup or delivery'
    },
    'delivered': {
      label: 'Delivered',
      color: 'bg-green-100 text-green-800 border-green-200',
      icon: CheckCircleIcon,
      description: 'Device returned to customer'
    },
    'cancelled': {
      label: 'Cancelled',
      color: 'bg-red-100 text-red-800 border-red-200',
      icon: ExclamationTriangleIcon,
      description: 'Repair cancelled'
    }
  };

  return configs[status] || {
    label: status,
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: ClockIcon,
    description: 'Unknown status'
  };
}

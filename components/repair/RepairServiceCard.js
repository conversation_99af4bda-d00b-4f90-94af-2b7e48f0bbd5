import Link from 'next/link';
import {
  ClockIcon,
  CurrencyDollarIcon,
  ShieldCheckIcon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline';

export default function RepairServiceCard({ service, onSelectService }) {
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getMinPrice = () => {
    if (service.pricing && service.pricing.length > 0) {
      return Math.min(...service.pricing.map(p => parseFloat(p.price)));
    }
    return parseFloat(service.base_price);
  };

  const getDifficultyColor = (difficulty) => {
    const colors = {
      'easy': 'text-green-600',
      'medium': 'text-yellow-600',
      'hard': 'text-orange-600',
      'expert': 'text-red-600'
    };
    return colors[difficulty] || 'text-gray-600';
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      {service.image_url && (
        <div className="h-48 bg-gray-200 overflow-hidden">
          <img
            src={service.image_url}
            alt={service.name}
            className="w-full h-full object-cover"
          />
        </div>
      )}
      
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-1">{service.name}</h3>
            <p className="text-sm text-gray-500">{service.category_name}</p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-blue-600">
              {formatPrice(getMinPrice())}
            </p>
            {service.pricing && service.pricing.length > 1 && (
              <p className="text-xs text-gray-500">starting</p>
            )}
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {service.short_description || service.description}
        </p>

        {/* Service Details */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-600">
            <ClockIcon className="h-4 w-4 mr-2" />
            <span>Est. {service.estimated_time_hours} hours</span>
          </div>
          
          <div className="flex items-center text-sm text-gray-600">
            <ShieldCheckIcon className="h-4 w-4 mr-2" />
            <span>{service.warranty_days} days warranty</span>
          </div>

          <div className="flex items-center text-sm">
            <WrenchScrewdriverIcon className="h-4 w-4 mr-2 text-gray-400" />
            <span className={getDifficultyColor(service.difficulty_level)}>
              {service.difficulty_level} difficulty
            </span>
          </div>

          {service.requires_diagnosis && (
            <div className="flex items-center text-sm text-orange-600">
              <CurrencyDollarIcon className="h-4 w-4 mr-2" />
              <span>Diagnosis required (+{formatPrice(service.diagnosis_fee)})</span>
            </div>
          )}
        </div>

        {/* Device Compatibility */}
        {service.pricing && service.pricing.length > 0 && (
          <div className="mb-4">
            <p className="text-xs font-medium text-gray-700 mb-2">Compatible Devices:</p>
            <div className="flex flex-wrap gap-1">
              {service.pricing.slice(0, 3).map((pricing, index) => (
                <span
                  key={index}
                  className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                >
                  {pricing.device_brand} {pricing.device_name}
                </span>
              ))}
              {service.pricing.length > 3 && (
                <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                  +{service.pricing.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex space-x-2">
          <Link
            href={`/repair/quote?service=${service.slug}`}
            className="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            Get Quote
          </Link>
          
          {onSelectService && (
            <button
              onClick={() => onSelectService(service)}
              className="flex-1 border border-blue-600 text-blue-600 text-center py-2 px-4 rounded-md hover:bg-blue-50 transition-colors text-sm font-medium"
            >
              Select
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  WrenchScrewdriverIcon,
  TruckIcon,
  DocumentTextIcon,
  CreditCardIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import styles from './RepairStatusTracker.module.css';

const RepairStatusTracker = ({ ticketNumber: initialTicketNumber }) => {
  const router = useRouter();
  const [ticketNumber, setTicketNumber] = useState(initialTicketNumber || '');
  const [ticketData, setTicketData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showEstimate, setShowEstimate] = useState(false);

  useEffect(() => {
    if (router.query.ticket) {
      setTicketNumber(router.query.ticket);
      searchTicket(router.query.ticket);
    }
  }, [router.query.ticket]);

  const searchTicket = async (ticket = ticketNumber) => {
    if (!ticket.trim()) {
      setError('Please enter a ticket number');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/repair/tickets?ticket_number=${encodeURIComponent(ticket)}`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.data && data.data.length > 0) {
          setTicketData(data.data[0]);
          
          // Update URL without page reload
          const newUrl = `/repair/track?ticket=${encodeURIComponent(ticket)}`;
          window.history.replaceState(null, '', newUrl);
        } else {
          setError('Ticket not found. Please check your ticket number.');
          setTicketData(null);
        }
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to fetch ticket information');
        setTicketData(null);
      }
    } catch (error) {
      console.error('Error searching ticket:', error);
      setError('Network error. Please try again.');
      setTicketData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleEstimateAction = async (action) => {
    if (!ticketData) return;

    try {
      const response = await fetch('/api/repair/estimate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ticket_id: ticketData.id,
          action: action // 'approve' or 'reject'
        }),
      });

      if (response.ok) {
        // Refresh ticket data
        searchTicket(ticketNumber);
        setShowEstimate(false);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to process estimate');
      }
    } catch (error) {
      console.error('Error processing estimate:', error);
      setError('Network error. Please try again.');
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'submitted':
        return <DocumentTextIcon className={styles.statusIcon} />;
      case 'received':
        return <CheckCircleIcon className={styles.statusIcon} />;
      case 'diagnosed':
        return <ExclamationTriangleIcon className={styles.statusIcon} />;
      case 'approved':
        return <CheckCircleIcon className={styles.statusIcon} />;
      case 'in_progress':
        return <WrenchScrewdriverIcon className={styles.statusIcon} />;
      case 'testing':
        return <ClockIcon className={styles.statusIcon} />;
      case 'completed':
        return <CheckCircleIcon className={styles.statusIcon} />;
      case 'ready_pickup':
        return <TruckIcon className={styles.statusIcon} />;
      case 'delivered':
        return <CheckCircleIcon className={styles.statusIcon} />;
      case 'cancelled':
        return <ExclamationTriangleIcon className={styles.statusIcon} />;
      default:
        return <ClockIcon className={styles.statusIcon} />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'submitted':
      case 'received':
        return 'blue';
      case 'diagnosed':
        return 'yellow';
      case 'approved':
      case 'in_progress':
      case 'testing':
        return 'orange';
      case 'completed':
      case 'ready_pickup':
      case 'delivered':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'gray';
    }
  };

  const formatStatus = (status) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const statusSteps = [
    { key: 'submitted', label: 'Submitted', description: 'Ticket received' },
    { key: 'received', label: 'Received', description: 'Device in our facility' },
    { key: 'diagnosed', label: 'Diagnosed', description: 'Issue identified' },
    { key: 'approved', label: 'Approved', description: 'Repair authorized' },
    { key: 'in_progress', label: 'In Progress', description: 'Repair underway' },
    { key: 'testing', label: 'Testing', description: 'Quality assurance' },
    { key: 'completed', label: 'Completed', description: 'Repair finished' },
    { key: 'ready_pickup', label: 'Ready', description: 'Ready for pickup' },
    { key: 'delivered', label: 'Delivered', description: 'Device returned' }
  ];

  const getCurrentStepIndex = () => {
    if (!ticketData) return -1;
    return statusSteps.findIndex(step => step.key === ticketData.status);
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>Track Your Repair</h1>
        <p>Enter your ticket number to check the status of your repair</p>
      </div>

      <div className={styles.searchSection}>
        <div className={styles.searchForm}>
          <input
            type="text"
            value={ticketNumber}
            onChange={(e) => setTicketNumber(e.target.value)}
            placeholder="Enter ticket number (e.g., RPR-1234567890-123)"
            className={styles.searchInput}
            onKeyPress={(e) => e.key === 'Enter' && searchTicket()}
          />
          <button 
            onClick={() => searchTicket()}
            className={styles.searchButton}
            disabled={loading}
          >
            {loading ? 'Searching...' : 'Track Repair'}
          </button>
        </div>

        {error && (
          <div className={styles.error}>
            <ExclamationTriangleIcon className={styles.errorIcon} />
            {error}
          </div>
        )}
      </div>

      {ticketData && (
        <div className={styles.ticketInfo}>
          {/* Ticket Header */}
          <div className={styles.ticketHeader}>
            <div className={styles.ticketNumber}>
              <h2>Ticket #{ticketData.ticket_number}</h2>
              <div className={`${styles.statusBadge} ${styles[getStatusColor(ticketData.status)]}`}>
                {getStatusIcon(ticketData.status)}
                {formatStatus(ticketData.status)}
              </div>
            </div>
            <div className={styles.ticketMeta}>
              <p><strong>Device:</strong> {ticketData.device_brand} {ticketData.device_model}</p>
              <p><strong>Submitted:</strong> {formatDate(ticketData.created_at)}</p>
              {ticketData.estimated_completion && (
                <p><strong>Estimated Completion:</strong> {formatDate(ticketData.estimated_completion)}</p>
              )}
            </div>
          </div>

          {/* Status Timeline */}
          <div className={styles.timeline}>
            <h3>Repair Progress</h3>
            <div className={styles.timelineSteps}>
              {statusSteps.map((step, index) => {
                const currentIndex = getCurrentStepIndex();
                const isCompleted = index <= currentIndex;
                const isCurrent = index === currentIndex;
                
                return (
                  <div 
                    key={step.key}
                    className={`${styles.timelineStep} ${isCompleted ? styles.completed : ''} ${isCurrent ? styles.current : ''}`}
                  >
                    <div className={styles.stepIcon}>
                      {isCompleted ? <CheckCircleIcon /> : <ClockIcon />}
                    </div>
                    <div className={styles.stepContent}>
                      <h4>{step.label}</h4>
                      <p>{step.description}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Repair Details */}
          <div className={styles.repairDetails}>
            <div className={styles.detailsGrid}>
              <div className={styles.detailCard}>
                <h4>Problem Description</h4>
                <p>{ticketData.problem_description}</p>
              </div>

              {ticketData.diagnosis_notes && (
                <div className={styles.detailCard}>
                  <h4>Diagnosis</h4>
                  <p>{ticketData.diagnosis_notes}</p>
                </div>
              )}

              {ticketData.repair_notes && (
                <div className={styles.detailCard}>
                  <h4>Repair Notes</h4>
                  <p>{ticketData.repair_notes}</p>
                </div>
              )}

              <div className={styles.detailCard}>
                <h4>Cost Information</h4>
                {ticketData.estimated_cost && (
                  <p><strong>Estimated:</strong> ${ticketData.estimated_cost}</p>
                )}
                {ticketData.final_cost && (
                  <p><strong>Final:</strong> ${ticketData.final_cost}</p>
                )}
                <p><strong>Payment Status:</strong> {formatStatus(ticketData.payment_status)}</p>
              </div>
            </div>
          </div>

          {/* Estimate Approval */}
          {ticketData.status === 'diagnosed' && ticketData.estimated_cost && (
            <div className={styles.estimateSection}>
              <div className={styles.estimateCard}>
                <h3>Repair Estimate</h3>
                <div className={styles.estimateAmount}>
                  ${ticketData.estimated_cost}
                </div>
                <p>Please review and approve the estimate to proceed with the repair.</p>
                <div className={styles.estimateActions}>
                  <button 
                    onClick={() => handleEstimateAction('approve')}
                    className={styles.approveButton}
                  >
                    <CheckCircleIcon className={styles.buttonIcon} />
                    Approve Estimate
                  </button>
                  <button 
                    onClick={() => handleEstimateAction('reject')}
                    className={styles.rejectButton}
                  >
                    <ExclamationTriangleIcon className={styles.buttonIcon} />
                    Reject Estimate
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Payment Section */}
          {ticketData.status === 'completed' && ticketData.payment_status === 'pending' && (
            <div className={styles.paymentSection}>
              <div className={styles.paymentCard}>
                <h3>Payment Required</h3>
                <div className={styles.paymentAmount}>
                  ${ticketData.final_cost || ticketData.estimated_cost}
                </div>
                <p>Your repair is complete! Please pay to arrange pickup or delivery.</p>
                <button 
                  onClick={() => router.push(`/repair/payment?ticket=${ticketData.ticket_number}`)}
                  className={styles.payButton}
                >
                  <CreditCardIcon className={styles.buttonIcon} />
                  Pay Now
                </button>
              </div>
            </div>
          )}

          {/* Contact Section */}
          <div className={styles.contactSection}>
            <h3>Need Help?</h3>
            <div className={styles.contactOptions}>
              <button 
                onClick={() => router.push(`/contact?ticket=${ticketData.ticket_number}`)}
                className={styles.contactButton}
              >
                <ChatBubbleLeftRightIcon className={styles.buttonIcon} />
                Contact Support
              </button>
              <a href="tel:******-REPAIR" className={styles.contactButton}>
                <span className={styles.buttonIcon}>📞</span>
                Call Us
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RepairStatusTracker;

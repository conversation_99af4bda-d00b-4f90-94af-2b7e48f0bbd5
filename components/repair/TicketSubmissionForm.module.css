.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.header p {
  color: #6b7280;
  font-size: 1.1rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.section h3 {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #d4af37;
}

.row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field label {
  color: #374151;
  font-weight: 500;
  font-size: 0.9rem;
}

.field input,
.field select,
.field textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.field input:focus,
.field select:focus,
.field textarea:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.field input.error,
.field select.error,
.field textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.errorText {
  color: #ef4444;
  font-size: 0.875rem;
  font-weight: 500;
}

.deviceIcon {
  width: 1.25rem;
  height: 1.25rem;
  display: inline-block;
  margin-right: 0.5rem;
  color: #6b7280;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.serviceCard {
  background: #fff;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.serviceCard:hover {
  border-color: #d4af37;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.serviceCard.selected {
  border-color: #d4af37;
  background: #fffbeb;
}

.serviceCard h4 {
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.serviceCard p {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.servicePrice {
  color: #d4af37;
  font-weight: 600;
  font-size: 1rem;
}

.priorityOptions {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.priorityOption {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: #fff;
}

.priorityOption:hover {
  border-color: #d4af37;
}

.priorityOption.selected {
  border-color: #d4af37;
  background: #fffbeb;
  color: #92400e;
}

.priorityOption.normal {
  color: #059669;
}

.priorityOption.high {
  color: #dc2626;
}

.priorityOption.urgent {
  color: #7c2d12;
  font-weight: 600;
}

.fileUpload {
  margin-top: 1rem;
}

.fileInput {
  display: none;
}

.fileUploadButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.fileUploadButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.uploadIcon {
  width: 1.25rem;
  height: 1.25rem;
}

.fileList {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.fileItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background: #f9fafb;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.fileName {
  color: #374151;
  font-size: 0.875rem;
}

.removeFileButton {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.removeFileButton:hover {
  background: #fee2e2;
}

.submitSection {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.submitError {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #ef4444;
  background: #fee2e2;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.errorIcon {
  width: 1.25rem;
  height: 1.25rem;
}

.submitButton {
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  color: #fff;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 200px;
}

.submitButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #b8941f 0%, #9c7a1a 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.successContainer {
  text-align: center;
  padding: 3rem 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.successIcon {
  width: 4rem;
  height: 4rem;
  color: #10b981;
  margin: 0 auto 1rem;
}

.successContainer h2 {
  color: #1f2937;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.successContainer p {
  color: #6b7280;
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.successActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.trackButton {
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.trackButton:hover {
  background: linear-gradient(135deg, #b8941f 0%, #9c7a1a 100%);
  transform: translateY(-1px);
}

.newTicketButton {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.newTicketButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    margin: 1rem;
    padding: 1.5rem;
  }

  .row {
    grid-template-columns: 1fr;
  }

  .servicesGrid {
    grid-template-columns: 1fr;
  }

  .priorityOptions {
    flex-direction: column;
  }

  .successActions {
    flex-direction: column;
  }

  .header h1 {
    font-size: 1.5rem;
  }
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: #1f2937;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.header p {
  color: #6b7280;
  font-size: 1.1rem;
}

.unauthorized {
  text-align: center;
  padding: 4rem 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.unauthorizedIcon {
  width: 4rem;
  height: 4rem;
  color: #ef4444;
  margin: 0 auto 1rem;
}

.unauthorized h2 {
  color: #1f2937;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.unauthorized p {
  color: #6b7280;
  font-size: 1.1rem;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: #fff;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s;
}

.statCard:hover {
  transform: translateY(-2px);
}

.statIcon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.statIcon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.statContent h3 {
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.statContent p {
  color: #6b7280;
  margin: 0;
  font-size: 0.875rem;
}

.filtersSection {
  background: #fff;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.filters {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 1rem;
  align-items: center;
}

.searchFilter {
  position: relative;
}

.searchIcon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: #6b7280;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.searchInput:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.filterSelect {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  background: #fff;
  transition: border-color 0.2s;
}

.filterSelect:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.ticketsSection {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.sectionHeader h2 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.addButton {
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.addButton:hover {
  background: linear-gradient(135deg, #b8941f 0%, #9c7a1a 100%);
  transform: translateY(-1px);
}

.buttonIcon {
  width: 1rem;
  height: 1rem;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
  font-size: 1.1rem;
}

.ticketsTable {
  display: flex;
  flex-direction: column;
}

.tableHeader {
  display: grid;
  grid-template-columns: 1.5fr 1.5fr 1.5fr 1fr 0.8fr 1.2fr 1fr 1.5fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.tableRow {
  display: grid;
  grid-template-columns: 1.5fr 1.5fr 1.5fr 1fr 0.8fr 1.2fr 1fr 1.5fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  align-items: center;
  transition: background-color 0.2s;
}

.tableRow:hover {
  background: #f9fafb;
}

.ticketNumber {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.customerInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.customerEmail {
  color: #6b7280;
  font-size: 0.75rem;
}

.deviceInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.deviceType {
  color: #6b7280;
  font-size: 0.75rem;
}

.statusBadge,
.priorityBadge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.statusBadge.blue,
.priorityBadge.blue {
  background: #dbeafe;
  color: #1e40af;
}

.statusBadge.yellow,
.priorityBadge.yellow {
  background: #fef3c7;
  color: #92400e;
}

.statusBadge.orange,
.priorityBadge.orange {
  background: #fed7aa;
  color: #c2410c;
}

.statusBadge.green,
.priorityBadge.green {
  background: #d1fae5;
  color: #065f46;
}

.statusBadge.red,
.priorityBadge.red {
  background: #fee2e2;
  color: #dc2626;
}

.statusBadge.gray,
.priorityBadge.gray {
  background: #f3f4f6;
  color: #374151;
}

.technicianInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.assignedTech {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
}

.techIcon {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
}

.assignButton {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.assignButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.dateInfo {
  color: #6b7280;
  font-size: 0.875rem;
}

.actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.viewButton {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.viewButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.statusSelect {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.75rem;
  background: #fff;
  min-width: 120px;
}

.noTickets {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.noTicketsIcon {
  width: 3rem;
  height: 3rem;
  margin: 0 auto 1rem;
  color: #d1d5db;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalContent {
  background: #fff;
  padding: 2rem;
  border-radius: 12px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modalContent h3 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.modalContent p {
  color: #6b7280;
  margin: 0 0 0.5rem 0;
}

.technicianList {
  display: grid;
  gap: 1rem;
  margin: 1.5rem 0;
}

.technicianCard {
  background: #f9fafb;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.technicianCard:hover {
  border-color: #d4af37;
  background: #fffbeb;
}

.technicianCard h4 {
  color: #1f2937;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.technicianCard p {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancelButton {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.cancelButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* Mobile Responsive */
@media (max-width: 1200px) {
  .tableHeader,
  .tableRow {
    grid-template-columns: 1fr 1fr 1fr 1fr;
    font-size: 0.75rem;
  }

  .tableHeader div:nth-child(n+5),
  .tableRow div:nth-child(n+5) {
    display: none;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .filters {
    grid-template-columns: 1fr;
  }

  .statsGrid {
    grid-template-columns: 1fr 1fr;
  }

  .tableHeader,
  .tableRow {
    grid-template-columns: 1fr 1fr;
    font-size: 0.75rem;
  }

  .tableHeader div:nth-child(n+3),
  .tableRow div:nth-child(n+3) {
    display: none;
  }

  .header h1 {
    font-size: 2rem;
  }
}

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import {
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon,
  CameraIcon,
  PhotoIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import styles from './TicketSubmissionForm.module.css';

const TicketSubmissionForm = () => {
  const { data: session } = useSession();
  const router = useRouter();
  
  // Form state
  const [formData, setFormData] = useState({
    customer_name: '',
    customer_email: '',
    customer_phone: '',
    device_type_id: '',
    device_brand: '',
    device_model: '',
    device_serial: '',
    device_imei: '',
    device_condition: '',
    problem_description: '',
    requested_services: [],
    priority: 'normal',
    customer_notes: ''
  });

  // Component state
  const [deviceTypes, setDeviceTypes] = useState([]);
  const [repairServices, setRepairServices] = useState([]);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [ticketNumber, setTicketNumber] = useState('');

  // Load user data if logged in
  useEffect(() => {
    if (session?.user) {
      setFormData(prev => ({
        ...prev,
        customer_name: `${session.user.first_name || ''} ${session.user.last_name || ''}`.trim(),
        customer_email: session.user.email || '',
        customer_phone: session.user.phone || ''
      }));
    }
  }, [session]);

  // Load device types and services
  useEffect(() => {
    loadDeviceTypes();
    loadRepairServices();
  }, []);

  const loadDeviceTypes = async () => {
    try {
      const response = await fetch('/api/repair/devices');
      if (response.ok) {
        const data = await response.json();
        setDeviceTypes(data.data || []);
      }
    } catch (error) {
      console.error('Error loading device types:', error);
    }
  };

  const loadRepairServices = async () => {
    try {
      const response = await fetch('/api/repair/services');
      if (response.ok) {
        const data = await response.json();
        setRepairServices(data.data || []);
      }
    } catch (error) {
      console.error('Error loading repair services:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleServiceToggle = (serviceId) => {
    setFormData(prev => ({
      ...prev,
      requested_services: prev.requested_services.includes(serviceId)
        ? prev.requested_services.filter(id => id !== serviceId)
        : [...prev.requested_services, serviceId]
    }));
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    const validFiles = files.filter(file => {
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      const maxSize = 10 * 1024 * 1024; // 10MB
      return validTypes.includes(file.type) && file.size <= maxSize;
    });
    
    setSelectedFiles(prev => [...prev, ...validFiles]);
  };

  const removeFile = (index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.customer_name.trim()) {
      newErrors.customer_name = 'Name is required';
    }

    if (!formData.customer_email.trim()) {
      newErrors.customer_email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.customer_email)) {
      newErrors.customer_email = 'Please enter a valid email';
    }

    if (!formData.problem_description.trim()) {
      newErrors.problem_description = 'Problem description is required';
    }

    if (!formData.device_type_id) {
      newErrors.device_type_id = 'Please select a device type';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Submit ticket
      const response = await fetch('/api/repair/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        setTicketNumber(result.data.ticket_number);
        setSubmitSuccess(true);

        // Upload files if any
        if (selectedFiles.length > 0) {
          await uploadFiles(result.data.id);
        }

        // Reset form
        setFormData({
          customer_name: session?.user ? `${session.user.first_name || ''} ${session.user.last_name || ''}`.trim() : '',
          customer_email: session?.user?.email || '',
          customer_phone: session?.user?.phone || '',
          device_type_id: '',
          device_brand: '',
          device_model: '',
          device_serial: '',
          device_imei: '',
          device_condition: '',
          problem_description: '',
          requested_services: [],
          priority: 'normal',
          customer_notes: ''
        });
        setSelectedFiles([]);
      } else {
        const errorData = await response.json();
        setErrors({ submit: errorData.message || 'Failed to submit ticket' });
      }
    } catch (error) {
      console.error('Error submitting ticket:', error);
      setErrors({ submit: 'Network error. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const uploadFiles = async (ticketId) => {
    const formData = new FormData();
    selectedFiles.forEach(file => {
      formData.append('files', file);
    });
    formData.append('ticket_id', ticketId);

    try {
      await fetch('/api/repair/upload', {
        method: 'POST',
        body: formData,
      });
    } catch (error) {
      console.error('Error uploading files:', error);
    }
  };

  const getDeviceIcon = (category) => {
    switch (category) {
      case 'phone':
        return <DevicePhoneMobileIcon className={styles.deviceIcon} />;
      case 'tablet':
        return <DeviceTabletIcon className={styles.deviceIcon} />;
      case 'laptop':
      case 'desktop':
        return <ComputerDesktopIcon className={styles.deviceIcon} />;
      default:
        return <DevicePhoneMobileIcon className={styles.deviceIcon} />;
    }
  };

  if (submitSuccess) {
    return (
      <div className={styles.successContainer}>
        <CheckCircleIcon className={styles.successIcon} />
        <h2>Repair Ticket Submitted Successfully!</h2>
        <p>Your ticket number is: <strong>{ticketNumber}</strong></p>
        <p>We'll send you an email confirmation shortly with tracking details.</p>
        <div className={styles.successActions}>
          <button 
            onClick={() => router.push(`/repair/track?ticket=${ticketNumber}`)}
            className={styles.trackButton}
          >
            Track Your Repair
          </button>
          <button 
            onClick={() => setSubmitSuccess(false)}
            className={styles.newTicketButton}
          >
            Submit Another Ticket
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>Submit Repair Ticket</h1>
        <p>Fill out the form below to get your device repaired by our expert technicians.</p>
      </div>

      <form onSubmit={handleSubmit} className={styles.form}>
        {/* Customer Information */}
        <div className={styles.section}>
          <h3>Customer Information</h3>
          <div className={styles.row}>
            <div className={styles.field}>
              <label htmlFor="customer_name">Full Name *</label>
              <input
                type="text"
                id="customer_name"
                name="customer_name"
                value={formData.customer_name}
                onChange={handleInputChange}
                className={errors.customer_name ? styles.error : ''}
                required
              />
              {errors.customer_name && <span className={styles.errorText}>{errors.customer_name}</span>}
            </div>
            <div className={styles.field}>
              <label htmlFor="customer_email">Email Address *</label>
              <input
                type="email"
                id="customer_email"
                name="customer_email"
                value={formData.customer_email}
                onChange={handleInputChange}
                className={errors.customer_email ? styles.error : ''}
                required
              />
              {errors.customer_email && <span className={styles.errorText}>{errors.customer_email}</span>}
            </div>
          </div>
          <div className={styles.field}>
            <label htmlFor="customer_phone">Phone Number</label>
            <input
              type="tel"
              id="customer_phone"
              name="customer_phone"
              value={formData.customer_phone}
              onChange={handleInputChange}
              placeholder="(*************"
            />
          </div>
        </div>

        {/* Device Information */}
        <div className={styles.section}>
          <h3>Device Information</h3>
          <div className={styles.field}>
            <label htmlFor="device_type_id">Device Type *</label>
            <select
              id="device_type_id"
              name="device_type_id"
              value={formData.device_type_id}
              onChange={handleInputChange}
              className={errors.device_type_id ? styles.error : ''}
              required
            >
              <option value="">Select Device Type</option>
              {deviceTypes.map(device => (
                <option key={device.id} value={device.id}>
                  {getDeviceIcon(device.category)} {device.brand} {device.name}
                </option>
              ))}
            </select>
            {errors.device_type_id && <span className={styles.errorText}>{errors.device_type_id}</span>}
          </div>
          
          <div className={styles.row}>
            <div className={styles.field}>
              <label htmlFor="device_brand">Brand</label>
              <input
                type="text"
                id="device_brand"
                name="device_brand"
                value={formData.device_brand}
                onChange={handleInputChange}
                placeholder="e.g., Apple, Samsung, Google"
              />
            </div>
            <div className={styles.field}>
              <label htmlFor="device_model">Model</label>
              <input
                type="text"
                id="device_model"
                name="device_model"
                value={formData.device_model}
                onChange={handleInputChange}
                placeholder="e.g., iPhone 14 Pro, Galaxy S23"
              />
            </div>
          </div>

          <div className={styles.row}>
            <div className={styles.field}>
              <label htmlFor="device_serial">Serial Number</label>
              <input
                type="text"
                id="device_serial"
                name="device_serial"
                value={formData.device_serial}
                onChange={handleInputChange}
                placeholder="Device serial number"
              />
            </div>
            <div className={styles.field}>
              <label htmlFor="device_imei">IMEI (for phones)</label>
              <input
                type="text"
                id="device_imei"
                name="device_imei"
                value={formData.device_imei}
                onChange={handleInputChange}
                placeholder="15-digit IMEI number"
              />
            </div>
          </div>
        </div>

        {/* Problem Description */}
        <div className={styles.section}>
          <h3>Problem Description</h3>
          <div className={styles.field}>
            <label htmlFor="problem_description">Describe the Issue *</label>
            <textarea
              id="problem_description"
              name="problem_description"
              value={formData.problem_description}
              onChange={handleInputChange}
              className={errors.problem_description ? styles.error : ''}
              rows="4"
              placeholder="Please describe the problem in detail..."
              required
            />
            {errors.problem_description && <span className={styles.errorText}>{errors.problem_description}</span>}
          </div>

          <div className={styles.field}>
            <label htmlFor="device_condition">Device Condition</label>
            <textarea
              id="device_condition"
              name="device_condition"
              value={formData.device_condition}
              onChange={handleInputChange}
              rows="2"
              placeholder="Any physical damage, water exposure, etc."
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className={styles.submitSection}>
          {errors.submit && (
            <div className={styles.submitError}>
              <ExclamationTriangleIcon className={styles.errorIcon} />
              {errors.submit}
            </div>
          )}
          
          <button 
            type="submit" 
            className={styles.submitButton}
            disabled={loading}
          >
            {loading ? 'Submitting...' : 'Submit Repair Ticket'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TicketSubmissionForm;

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  WrenchScrewdriverIcon,
  UserIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import styles from './AdminDashboard.module.css';

const AdminDashboard = () => {
  const { data: session } = useSession();
  const [tickets, setTickets] = useState([]);
  const [technicians, setTechnicians] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: '',
    technician: '',
    priority: '',
    search: ''
  });
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showAssignModal, setShowAssignModal] = useState(false);

  useEffect(() => {
    if (session?.user?.is_admin || session?.user?.is_repair_admin) {
      loadDashboardData();
    }
  }, [session, filters]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load tickets with filters
      const ticketParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) ticketParams.append(key, value);
      });

      const [ticketsRes, techniciansRes, statsRes] = await Promise.all([
        fetch(`/api/repair/tickets?${ticketParams}`),
        fetch('/api/repair/technicians'),
        fetch('/api/repair/stats')
      ]);

      if (ticketsRes.ok) {
        const ticketsData = await ticketsRes.json();
        setTickets(ticketsData.data || []);
      }

      if (techniciansRes.ok) {
        const techniciansData = await techniciansRes.json();
        setTechnicians(techniciansData.data || []);
      }

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData.data || {});
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleTicketUpdate = async (ticketId, updates) => {
    try {
      const response = await fetch(`/api/repair/tickets/${ticketId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (response.ok) {
        loadDashboardData(); // Refresh data
        setSelectedTicket(null);
        setShowAssignModal(false);
      } else {
        const errorData = await response.json();
        alert(errorData.message || 'Failed to update ticket');
      }
    } catch (error) {
      console.error('Error updating ticket:', error);
      alert('Network error. Please try again.');
    }
  };

  const assignTechnician = (ticketId, technicianId) => {
    handleTicketUpdate(ticketId, { 
      technician_id: technicianId,
      status: 'in_progress'
    });
  };

  const updateTicketStatus = (ticketId, status) => {
    handleTicketUpdate(ticketId, { status });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'submitted':
      case 'received':
        return 'blue';
      case 'diagnosed':
        return 'yellow';
      case 'approved':
      case 'in_progress':
      case 'testing':
        return 'orange';
      case 'completed':
      case 'ready_pickup':
      case 'delivered':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent':
        return 'red';
      case 'high':
        return 'orange';
      case 'normal':
        return 'blue';
      case 'low':
        return 'gray';
      default:
        return 'gray';
    }
  };

  const formatStatus = (status) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!session?.user?.is_admin && !session?.user?.is_repair_admin) {
    return (
      <div className={styles.unauthorized}>
        <ExclamationTriangleIcon className={styles.unauthorizedIcon} />
        <h2>Access Denied</h2>
        <p>You don't have permission to access the repair admin dashboard.</p>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>Repair Management Dashboard</h1>
        <p>Manage repair tickets, assign technicians, and track progress</p>
      </div>

      {/* Stats Cards */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <ClockIcon />
          </div>
          <div className={styles.statContent}>
            <h3>{stats.pending_tickets || 0}</h3>
            <p>Pending Tickets</p>
          </div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <WrenchScrewdriverIcon />
          </div>
          <div className={styles.statContent}>
            <h3>{stats.in_progress_tickets || 0}</h3>
            <p>In Progress</p>
          </div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <CheckCircleIcon />
          </div>
          <div className={styles.statContent}>
            <h3>{stats.completed_today || 0}</h3>
            <p>Completed Today</p>
          </div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <CurrencyDollarIcon />
          </div>
          <div className={styles.statContent}>
            <h3>${stats.revenue_today || 0}</h3>
            <p>Revenue Today</p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className={styles.filtersSection}>
        <div className={styles.filters}>
          <div className={styles.searchFilter}>
            <MagnifyingGlassIcon className={styles.searchIcon} />
            <input
              type="text"
              placeholder="Search tickets..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className={styles.filterSelect}
          >
            <option value="">All Statuses</option>
            <option value="submitted">Submitted</option>
            <option value="received">Received</option>
            <option value="diagnosed">Diagnosed</option>
            <option value="approved">Approved</option>
            <option value="in_progress">In Progress</option>
            <option value="testing">Testing</option>
            <option value="completed">Completed</option>
            <option value="ready_pickup">Ready for Pickup</option>
            <option value="delivered">Delivered</option>
          </select>

          <select
            value={filters.technician}
            onChange={(e) => handleFilterChange('technician', e.target.value)}
            className={styles.filterSelect}
          >
            <option value="">All Technicians</option>
            {technicians.map(tech => (
              <option key={tech.id} value={tech.id}>
                {tech.first_name} {tech.last_name}
              </option>
            ))}
          </select>

          <select
            value={filters.priority}
            onChange={(e) => handleFilterChange('priority', e.target.value)}
            className={styles.filterSelect}
          >
            <option value="">All Priorities</option>
            <option value="urgent">Urgent</option>
            <option value="high">High</option>
            <option value="normal">Normal</option>
            <option value="low">Low</option>
          </select>
        </div>
      </div>

      {/* Tickets Table */}
      <div className={styles.ticketsSection}>
        <div className={styles.sectionHeader}>
          <h2>Repair Tickets</h2>
          <button className={styles.addButton}>
            <PlusIcon className={styles.buttonIcon} />
            New Ticket
          </button>
        </div>

        {loading ? (
          <div className={styles.loading}>Loading tickets...</div>
        ) : (
          <div className={styles.ticketsTable}>
            <div className={styles.tableHeader}>
              <div>Ticket #</div>
              <div>Customer</div>
              <div>Device</div>
              <div>Status</div>
              <div>Priority</div>
              <div>Technician</div>
              <div>Created</div>
              <div>Actions</div>
            </div>

            {tickets.map(ticket => (
              <div key={ticket.id} className={styles.tableRow}>
                <div className={styles.ticketNumber}>
                  {ticket.ticket_number}
                </div>
                <div className={styles.customerInfo}>
                  <div>{ticket.customer_name}</div>
                  <div className={styles.customerEmail}>{ticket.customer_email}</div>
                </div>
                <div className={styles.deviceInfo}>
                  <div>{ticket.device_brand} {ticket.device_model}</div>
                  <div className={styles.deviceType}>{ticket.device_name}</div>
                </div>
                <div>
                  <span className={`${styles.statusBadge} ${styles[getStatusColor(ticket.status)]}`}>
                    {formatStatus(ticket.status)}
                  </span>
                </div>
                <div>
                  <span className={`${styles.priorityBadge} ${styles[getPriorityColor(ticket.priority)]}`}>
                    {ticket.priority}
                  </span>
                </div>
                <div className={styles.technicianInfo}>
                  {ticket.technician_first_name ? (
                    <div className={styles.assignedTech}>
                      <UserIcon className={styles.techIcon} />
                      {ticket.technician_first_name} {ticket.technician_last_name}
                    </div>
                  ) : (
                    <button
                      onClick={() => {
                        setSelectedTicket(ticket);
                        setShowAssignModal(true);
                      }}
                      className={styles.assignButton}
                    >
                      Assign
                    </button>
                  )}
                </div>
                <div className={styles.dateInfo}>
                  {formatDate(ticket.created_at)}
                </div>
                <div className={styles.actions}>
                  <button
                    onClick={() => setSelectedTicket(ticket)}
                    className={styles.viewButton}
                  >
                    View
                  </button>
                  <select
                    value={ticket.status}
                    onChange={(e) => updateTicketStatus(ticket.id, e.target.value)}
                    className={styles.statusSelect}
                  >
                    <option value="submitted">Submitted</option>
                    <option value="received">Received</option>
                    <option value="diagnosed">Diagnosed</option>
                    <option value="approved">Approved</option>
                    <option value="in_progress">In Progress</option>
                    <option value="testing">Testing</option>
                    <option value="completed">Completed</option>
                    <option value="ready_pickup">Ready for Pickup</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
              </div>
            ))}

            {tickets.length === 0 && (
              <div className={styles.noTickets}>
                <ClockIcon className={styles.noTicketsIcon} />
                <p>No tickets found matching your filters.</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Assign Technician Modal */}
      {showAssignModal && selectedTicket && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <h3>Assign Technician</h3>
            <p>Ticket: {selectedTicket.ticket_number}</p>
            <p>Device: {selectedTicket.device_brand} {selectedTicket.device_model}</p>
            
            <div className={styles.technicianList}>
              {technicians.map(tech => (
                <div 
                  key={tech.id}
                  className={styles.technicianCard}
                  onClick={() => assignTechnician(selectedTicket.id, tech.id)}
                >
                  <div className={styles.technicianInfo}>
                    <h4>{tech.first_name} {tech.last_name}</h4>
                    <p>Level: {tech.technician_level}</p>
                    <p>Active Tickets: {tech.active_tickets || 0}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className={styles.modalActions}>
              <button 
                onClick={() => setShowAssignModal(false)}
                className={styles.cancelButton}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: #1f2937;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.header p {
  color: #6b7280;
  font-size: 1.1rem;
}

.searchSection {
  background: #fff;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.searchForm {
  display: flex;
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.searchInput {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.searchInput:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.searchButton {
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  color: #fff;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.searchButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #b8941f 0%, #9c7a1a 100%);
  transform: translateY(-1px);
}

.searchButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #ef4444;
  background: #fee2e2;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.errorIcon {
  width: 1.25rem;
  height: 1.25rem;
}

.ticketInfo {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.ticketHeader {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.ticketNumber {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.ticketNumber h2 {
  color: #1f2937;
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
}

.statusBadge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.875rem;
}

.statusBadge.blue {
  background: #dbeafe;
  color: #1e40af;
}

.statusBadge.yellow {
  background: #fef3c7;
  color: #92400e;
}

.statusBadge.orange {
  background: #fed7aa;
  color: #c2410c;
}

.statusBadge.green {
  background: #d1fae5;
  color: #065f46;
}

.statusBadge.red {
  background: #fee2e2;
  color: #dc2626;
}

.statusBadge.gray {
  background: #f3f4f6;
  color: #374151;
}

.statusIcon {
  width: 1rem;
  height: 1rem;
}

.ticketMeta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.ticketMeta p {
  color: #6b7280;
  margin: 0;
}

.timeline {
  padding: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.timeline h3 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.timelineSteps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.timelineStep {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.2s;
}

.timelineStep.completed {
  background: #f0fdf4;
  border-left: 4px solid #10b981;
}

.timelineStep.current {
  background: #fffbeb;
  border-left: 4px solid #d4af37;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stepIcon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  color: #6b7280;
}

.timelineStep.completed .stepIcon {
  background: #10b981;
  color: #fff;
}

.timelineStep.current .stepIcon {
  background: #d4af37;
  color: #fff;
}

.stepIcon svg {
  width: 1rem;
  height: 1rem;
}

.stepContent h4 {
  color: #1f2937;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.stepContent p {
  color: #6b7280;
  margin: 0;
  font-size: 0.875rem;
}

.repairDetails {
  padding: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.detailCard {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.detailCard h4 {
  color: #1f2937;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
}

.detailCard p {
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.estimateSection,
.paymentSection {
  padding: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.estimateCard,
.paymentCard {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  border: 2px solid #d4af37;
}

.estimateCard h3,
.paymentCard h3 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.estimateAmount,
.paymentAmount {
  font-size: 2.5rem;
  font-weight: 700;
  color: #d4af37;
  margin: 1rem 0;
}

.estimateActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1.5rem;
}

.approveButton,
.payButton {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.approveButton:hover,
.payButton:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
}

.rejectButton {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rejectButton:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-1px);
}

.buttonIcon {
  width: 1rem;
  height: 1rem;
}

.contactSection {
  padding: 2rem;
}

.contactSection h3 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

.contactOptions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.contactButton {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.contactButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .searchForm {
    flex-direction: column;
  }

  .ticketNumber {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .ticketMeta {
    grid-template-columns: 1fr;
  }

  .detailsGrid {
    grid-template-columns: 1fr;
  }

  .estimateActions,
  .contactOptions {
    flex-direction: column;
  }

  .header h1 {
    font-size: 2rem;
  }

  .estimateAmount,
  .paymentAmount {
    font-size: 2rem;
  }
}

import React, { useState, useEffect, useRef } from 'react';
import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  MagnifyingGlassIcon,
  ShoppingCartIcon,
  UserIcon,
  WrenchScrewdriverIcon,
  Bars3Icon,
  XMarkIcon,
  ClockIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';
import styles from './Header.module.css';

const Header = () => {
  const { data: session } = useSession();
  const router = useRouter();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [repairDropdownOpen, setRepairDropdownOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [repairTicketNumber, setRepairTicketNumber] = useState('');
  const repairDropdownRef = useRef(null);
  const userDropdownRef = useRef(null);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (repairDropdownRef.current && !repairDropdownRef.current.contains(event.target)) {
        setRepairDropdownOpen(false);
      }
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target)) {
        setUserDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleRepairTrack = (e) => {
    e.preventDefault();
    if (repairTicketNumber.trim()) {
      router.push(`/repair/track?ticket=${encodeURIComponent(repairTicketNumber.trim())}`);
      setRepairDropdownOpen(false);
      setRepairTicketNumber('');
    }
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const isActive = (path) => {
    return router.pathname === path || router.pathname.startsWith(path + '/');
  };

  return (
    <header className={`${styles.header} ${scrolled ? styles.scrolled : ''}`}>
      {/* Top Banner */}
      <div className={styles.topBanner}>
        <div className={styles.bannerContent}>
          <div className={styles.bannerLeft}>
            <PhoneIcon className={styles.bannerIcon} />
            <span>Need Help? Call (*************</span>
          </div>
          <div className={styles.bannerCenter}>
            <span>🔧 Professional Device Repair Services Available</span>
          </div>
          <div className={styles.bannerRight}>
            <ClockIcon className={styles.bannerIcon} />
            <span>Mon-Fri 9AM-10PM EST</span>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className={styles.mainHeader}>
        <div className={styles.headerContainer}>
          {/* Logo */}
          <div className={styles.logo}>
            <Link href="/">
              <span className={styles.logoText}>Midas Technical</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className={styles.desktopNav}>
            <Link 
              href="/products" 
              className={`${styles.navLink} ${isActive('/products') ? styles.active : ''}`}
            >
              Products
            </Link>
            <Link 
              href="/categories" 
              className={`${styles.navLink} ${isActive('/categories') ? styles.active : ''}`}
            >
              Categories
            </Link>
            
            {/* Repair Services Dropdown */}
            <div className={styles.dropdown} ref={repairDropdownRef}>
              <button
                className={`${styles.navLink} ${styles.dropdownToggle} ${isActive('/repair') ? styles.active : ''}`}
                onClick={() => setRepairDropdownOpen(!repairDropdownOpen)}
              >
                <WrenchScrewdriverIcon className={styles.navIcon} />
                Repair Services
              </button>
              
              {repairDropdownOpen && (
                <div className={styles.dropdownMenu}>
                  <div className={styles.dropdownSection}>
                    <h4>Services</h4>
                    <Link href="/repair" className={styles.dropdownLink}>
                      All Repair Services
                    </Link>
                    <Link href="/repair/quote" className={styles.dropdownLink}>
                      Get Free Quote
                    </Link>
                    <Link href="/repair?category=screen-repair" className={styles.dropdownLink}>
                      Screen Repair
                    </Link>
                    <Link href="/repair?category=battery-replacement" className={styles.dropdownLink}>
                      Battery Replacement
                    </Link>
                    <Link href="/repair?category=water-damage" className={styles.dropdownLink}>
                      Water Damage
                    </Link>
                  </div>
                  
                  <div className={styles.dropdownSection}>
                    <h4>Track Repair</h4>
                    <form onSubmit={handleRepairTrack} className={styles.trackForm}>
                      <input
                        type="text"
                        placeholder="Enter ticket number"
                        value={repairTicketNumber}
                        onChange={(e) => setRepairTicketNumber(e.target.value)}
                        className={styles.trackInput}
                      />
                      <button type="submit" className={styles.trackButton}>
                        Track
                      </button>
                    </form>
                    <Link href="/repair/track" className={styles.dropdownLink}>
                      Track Without Number
                    </Link>
                  </div>
                  
                  <div className={styles.dropdownSection}>
                    <h4>Support</h4>
                    <Link href="/repair/warranty" className={styles.dropdownLink}>
                      Warranty Information
                    </Link>
                    <Link href="/contact?department=repair" className={styles.dropdownLink}>
                      Contact Repair Dept
                    </Link>
                  </div>
                </div>
              )}
            </div>

            <Link 
              href="/lcd-buyback" 
              className={`${styles.navLink} ${isActive('/lcd-buyback') ? styles.active : ''}`}
            >
              LCD Buyback
            </Link>
          </nav>

          {/* Search Bar */}
          <form onSubmit={handleSearch} className={styles.searchForm}>
            <input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={styles.searchInput}
            />
            <button type="submit" className={styles.searchButton}>
              <MagnifyingGlassIcon className={styles.searchIcon} />
            </button>
          </form>

          {/* Right Side Actions */}
          <div className={styles.rightActions}>
            {/* Quick Repair Access for Logged-in Users */}
            {session && (
              <Link href="/repair/track" className={styles.quickRepair}>
                <WrenchScrewdriverIcon className={styles.actionIcon} />
                <span className={styles.actionText}>My Repairs</span>
              </Link>
            )}

            {/* Cart */}
            <Link href="/cart" className={styles.cartLink}>
              <ShoppingCartIcon className={styles.actionIcon} />
              <span className={styles.actionText}>Cart</span>
            </Link>

            {/* User Account Dropdown */}
            <div className={styles.dropdown} ref={userDropdownRef}>
              <button
                className={styles.userButton}
                onClick={() => setUserDropdownOpen(!userDropdownOpen)}
              >
                <UserIcon className={styles.actionIcon} />
                <span className={styles.actionText}>
                  {session ? session.user.name || 'Account' : 'Sign In'}
                </span>
              </button>

              {userDropdownOpen && (
                <div className={styles.dropdownMenu}>
                  {session ? (
                    <>
                      <Link href="/account" className={styles.dropdownLink}>
                        My Account
                      </Link>
                      <Link href="/orders" className={styles.dropdownLink}>
                        My Orders
                      </Link>
                      <Link href="/repair/track" className={styles.dropdownLink}>
                        My Repairs
                      </Link>
                      <Link href="/wishlist" className={styles.dropdownLink}>
                        Wishlist
                      </Link>
                      <hr className={styles.dropdownDivider} />
                      <button 
                        onClick={() => signOut()}
                        className={styles.dropdownLink}
                      >
                        Sign Out
                      </button>
                    </>
                  ) : (
                    <>
                      <Link href="/auth/signin" className={styles.dropdownLink}>
                        Sign In
                      </Link>
                      <Link href="/auth/register" className={styles.dropdownLink}>
                        Create Account
                      </Link>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              className={styles.mobileMenuButton}
              onClick={toggleMobileMenu}
              aria-label="Toggle mobile menu"
            >
              {mobileMenuOpen ? (
                <XMarkIcon className={styles.menuIcon} />
              ) : (
                <Bars3Icon className={styles.menuIcon} />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className={styles.mobileMenu}>
          <div className={styles.mobileMenuContent}>
            {/* Mobile Search */}
            <form onSubmit={handleSearch} className={styles.mobileSearch}>
              <input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={styles.mobileSearchInput}
              />
              <button type="submit" className={styles.mobileSearchButton}>
                <MagnifyingGlassIcon className={styles.searchIcon} />
              </button>
            </form>

            {/* Mobile Navigation Links */}
            <nav className={styles.mobileNav}>
              <Link href="/products" className={styles.mobileNavLink}>
                Products
              </Link>
              <Link href="/categories" className={styles.mobileNavLink}>
                Categories
              </Link>
              
              {/* Mobile Repair Services */}
              <div className={styles.mobileRepairSection}>
                <h4 className={styles.mobileRepairTitle}>Repair Services</h4>
                <Link href="/repair" className={styles.mobileNavLink}>
                  All Repair Services
                </Link>
                <Link href="/repair/quote" className={styles.mobileNavLink}>
                  Get Free Quote
                </Link>
                <Link href="/repair/track" className={styles.mobileNavLink}>
                  Track Repair
                </Link>
                <Link href="/repair/warranty" className={styles.mobileNavLink}>
                  Warranty Info
                </Link>
              </div>

              <Link href="/lcd-buyback" className={styles.mobileNavLink}>
                LCD Buyback
              </Link>
              <Link href="/cart" className={styles.mobileNavLink}>
                Cart
              </Link>
              
              {session ? (
                <>
                  <Link href="/account" className={styles.mobileNavLink}>
                    My Account
                  </Link>
                  <Link href="/orders" className={styles.mobileNavLink}>
                    My Orders
                  </Link>
                  <button 
                    onClick={() => signOut()}
                    className={styles.mobileNavLink}
                  >
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <Link href="/auth/signin" className={styles.mobileNavLink}>
                    Sign In
                  </Link>
                  <Link href="/auth/register" className={styles.mobileNavLink}>
                    Create Account
                  </Link>
                </>
              )}
            </nav>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;

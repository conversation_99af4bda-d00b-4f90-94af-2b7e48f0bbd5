/* Header.module.css */
.header {
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.scrolled {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Top Banner */
.topBanner {
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  color: white;
  padding: 8px 0;
  font-size: 14px;
}

.bannerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.bannerLeft,
.bannerCenter,
.bannerRight {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bannerIcon {
  width: 16px;
  height: 16px;
}

.bannerCenter {
  font-weight: 500;
}

/* Main Header */
.mainHeader {
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
}

.headerContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  gap: 20px;
}

/* Logo */
.logo {
  flex-shrink: 0;
}

.logoText {
  font-size: 24px;
  font-weight: 700;
  color: #1e40af;
  text-decoration: none;
  transition: color 0.3s ease;
}

.logoText:hover {
  color: #3b82f6;
}

/* Desktop Navigation */
.desktopNav {
  display: flex;
  align-items: center;
  gap: 32px;
}

.navLink {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  color: #374151;
  text-decoration: none;
  font-weight: 500;
  font-size: 15px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
}

.navLink:hover {
  color: #1e40af;
  background-color: #f8fafc;
}

.navLink.active {
  color: #1e40af;
  background-color: #eff6ff;
}

.navIcon {
  width: 18px;
  height: 18px;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdownToggle {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 280px;
  display: flex;
  gap: 24px;
  z-index: 1000;
  margin-top: 8px;
}

.dropdownSection h4 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dropdownLink {
  display: block;
  padding: 8px 12px;
  color: #6b7280;
  text-decoration: none;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-bottom: 4px;
}

.dropdownLink:hover {
  color: #1e40af;
  background-color: #f8fafc;
}

.trackForm {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.trackInput {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
}

.trackButton {
  padding: 6px 12px;
  background: #1e40af;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.trackButton:hover {
  background: #1d4ed8;
}

.dropdownDivider {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 8px 0;
}

/* Search Form */
.searchForm {
  display: flex;
  align-items: center;
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 2px;
  flex: 1;
  max-width: 400px;
}

.searchInput {
  flex: 1;
  padding: 10px 12px;
  border: none;
  background: transparent;
  font-size: 14px;
  outline: none;
}

.searchButton {
  padding: 8px;
  background: #1e40af;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.searchButton:hover {
  background: #1d4ed8;
}

.searchIcon {
  width: 18px;
  height: 18px;
}

/* Right Actions */
.rightActions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.quickRepair,
.cartLink,
.userButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  color: #374151;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

.quickRepair:hover,
.cartLink:hover,
.userButton:hover {
  color: #1e40af;
  background-color: #f8fafc;
}

.actionIcon {
  width: 20px;
  height: 20px;
}

.actionText {
  font-size: 14px;
}

/* Mobile Menu Button */
.mobileMenuButton {
  display: none;
  padding: 8px;
  background: none;
  border: none;
  cursor: pointer;
  color: #374151;
}

.menuIcon {
  width: 24px;
  height: 24px;
}

/* Mobile Menu */
.mobileMenu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}

.mobileMenuContent {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background: white;
  padding: 20px;
  overflow-y: auto;
}

.mobileSearch {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
}

.mobileSearchInput {
  flex: 1;
  padding: 10px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.mobileSearchButton {
  padding: 10px;
  background: #1e40af;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.mobileNav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mobileNavLink {
  padding: 12px 16px;
  color: #374151;
  text-decoration: none;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-family: inherit;
  font-size: 16px;
}

.mobileNavLink:hover {
  color: #1e40af;
  background-color: #f8fafc;
}

.mobileRepairSection {
  margin: 16px 0;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.mobileRepairTitle {
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
  margin: 0 0 12px 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .bannerLeft,
  .bannerRight {
    display: none;
  }
  
  .bannerContent {
    justify-content: center;
  }
  
  .desktopNav {
    gap: 20px;
  }
  
  .searchForm {
    max-width: 300px;
  }
}

@media (max-width: 768px) {
  .topBanner {
    padding: 6px 0;
    font-size: 12px;
  }
  
  .headerContainer {
    height: 70px;
    padding: 0 16px;
  }
  
  .desktopNav {
    display: none;
  }
  
  .searchForm {
    display: none;
  }
  
  .rightActions {
    gap: 8px;
  }
  
  .actionText {
    display: none;
  }
  
  .mobileMenuButton {
    display: block;
  }
  
  .mobileMenu {
    display: block;
  }
  
  .quickRepair {
    display: none;
  }
}

@media (max-width: 480px) {
  .logoText {
    font-size: 20px;
  }
  
  .headerContainer {
    padding: 0 12px;
  }
  
  .mobileMenuContent {
    width: 280px;
  }
}

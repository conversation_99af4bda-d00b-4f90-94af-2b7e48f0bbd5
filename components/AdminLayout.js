import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useSession, signOut } from 'next-auth/react';
import Layout from './Layout/Layout';

const AdminLayout = ({ children, title = 'Admin Dashboard' }) => {
  const { data: session } = useSession();
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: 'chart-bar' },
    { name: 'Orders', href: '/admin/orders', icon: 'shopping-bag' },
    { name: 'Products', href: '/admin/products', icon: 'cube' },
    { name: 'Categories', href: '/admin/categories', icon: 'folder' },
    { name: 'Customers', href: '/admin/customers', icon: 'users' },
    { name: 'Repair Management', href: '/admin/repair-management', icon: 'wrench' },
    { name: 'Inventory', href: '/admin/inventory', icon: 'archive' },
    { name: 'Sales Analytics', href: '/admin/sales-analytics', icon: 'trending-up' },
    { name: 'Settings', href: '/admin/settings', icon: 'cog' },
  ];

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/' });
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          {/* Sidebar */}
          <div className={`${sidebarOpen ? 'block' : 'hidden'} fixed inset-0 z-40 lg:relative lg:inset-auto lg:block lg:z-auto`}>
            <div className="flex">
              <div className="flex flex-col w-64 bg-white shadow-lg">
                <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Admin Panel</h2>
                  <button
                    onClick={() => setSidebarOpen(false)}
                    className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500"
                  >
                    <span className="sr-only">Close sidebar</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <nav className="flex-1 px-4 py-4 space-y-2">
                  {navigation.map((item) => {
                    const isActive = router.pathname === item.href;
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={`${
                          isActive
                            ? 'bg-blue-50 border-blue-500 text-blue-700'
                            : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        } group flex items-center px-3 py-2 text-sm font-medium border-l-4 rounded-md transition-colors duration-200`}
                      >
                        <span className="truncate">{item.name}</span>
                      </Link>
                    );
                  })}
                </nav>

                <div className="border-t border-gray-200 p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center">
                        <span className="text-sm font-medium text-white">
                          {session?.user?.name?.charAt(0) || 'A'}
                        </span>
                      </div>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">
                        {session?.user?.name || 'Admin'}
                      </p>
                      <p className="text-xs text-gray-500">
                        Administrator
                      </p>
                    </div>
                  </div>
                  <div className="mt-3 space-y-1">
                    <Link
                      href="/account"
                      className="block text-sm text-gray-500 hover:text-gray-700"
                    >
                      View as Customer
                    </Link>
                    <button
                      onClick={handleSignOut}
                      className="block w-full text-left text-sm text-gray-500 hover:text-gray-700"
                    >
                      Sign out
                    </button>
                  </div>
                </div>
              </div>
              {sidebarOpen && (
                <div className="flex-shrink-0 w-14" aria-hidden="true">
                  {/* Force sidebar to shrink to fit close icon */}
                </div>
              )}
            </div>
          </div>

          {/* Main content */}
          <div className="flex-1 lg:pl-0">
            <div className="lg:hidden">
              <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 bg-white">
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="p-2 rounded-md text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">Open sidebar</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
                <h1 className="text-lg font-semibold text-gray-900">{title}</h1>
                <div></div>
              </div>
            </div>

            <main className="flex-1">
              <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="hidden lg:block mb-6">
                    <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                  </div>
                  {children}
                </div>
              </div>
            </main>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AdminLayout;
